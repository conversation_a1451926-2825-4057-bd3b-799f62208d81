services:
  analysis-system:
    image: sdharbor.sdvideo.cn/av-group/analysis-system-jre8:v1.2.5
    volumes:
      - ./algConfig:/app/algConfig
      - ./config:/app/config
      - ./h2Db:/app/h2Db
      - ./logs:/app/logs
      - /data/tmp/img:/app/tmp
    ports:
      - "8080:8080"
    command: >
      -Xms2048m -Xmx2048m -Duser.timezone=Asia/Shanghai -Dfile.encoding=utf-8 -jar /app/app.jar --spring.profiles.active=prod
      -Xms2048m -Xmx2048m -Duser.timezone=Asia/Shanghai -Dfile.encoding=utf-8 -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9999 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -jar /app/app.jar --spring.profiles.active=prod


  stream-shot:
    image: sdharbor.sdvideo.cn/av-group/stream-shot-arm-with-ubuntu-2404:v1.0.0
#    image: shot:v0.0.1
    volumes:
      - /data/tmp/img:/app/tmp
    ports:
      - "18776:18776"
