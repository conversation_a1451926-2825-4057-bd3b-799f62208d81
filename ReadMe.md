算法分析这个地方不应该绘图 更不应该上传3个图
理想的状态是先把告警推送出来
而告警的图片链接 用服务的方式进行访问
这样可以二次绘图  如果没人访问这个图片 也不会产生消耗
但是byd前面的人根本不存这个告警图片  这会导致频繁的访问这个地址  不知道会不会有什么资源消耗  如果没有是最好的


妈的 在家推送好麻烦
首先登录vpn 
绑定host：
******** sdharbor.sdvideo.cn
在docker中配置一个忽略SSL
"insecure-registries": ["sdharbor.sdvideo.cn"],

记录一下docker的国内镜像
"registry-mirrors": [
"https://docker.1ms.run",
"https://doublezonline.cloud",
"https://dislabaiot.xyz",
"https://docker.fxxk.dedyn.io",
"https://dockerpull.org",
"https://docker.unsee.tech",
"https://hub.rat.dev",
"https://docker.1panel.live",
"https://docker.nastool.de",
"https://docker.zhai.cm",
"https://docker.5z5f.com",
"https://a.ussh.net",
"https://docker.udayun.com",
"https://hub.geekery.cn",
"https://dockerpull.org/"
]


先登录私服 如果登录过了就不用登录了
harbor登录账号密码：用户名 saida 密码 NXun9^#@
登录harbor：docker login sdharbor.sdvideo.cn

mvn clean package -Dmaven.test.skip=true

把打包后的target/analysis-system.jar 拷贝到Dockerfile所在目录

[0.9.92] 代表的是你要打包的版本号

doc/Dockerfile 
来这个地方打包
命令打包：
docker build -t analysis-system-jre8:v0.9.92 .
如果你是mac 还是打x64 镜像
docker buildx build --platform linux/amd64 --tag sdharbor.sdvideo.cn/av-group/analysis-system-jre8:v1.4.31 --push  .
docker build --tag sdharbor.sdvideo.cn/av-group/analysis-system-arm-jre8:v1.0.2 --push  .

没网络的情况下这样来
docker save -o analysis-system-jre8-v1.4.05.tar sdharbor.sdvideo.cn/av-group/analysis-system-jre8:v1.4.05
docker load -i analysis-system-jre8-v1.4.05.tar


如果是idea 的话直接把analysis-system-jre8:v0.9.92 放在 配置的image Tag里面

然后打tag：  这个地方要打到对应域名上
docker tag analysis-system-jre8:v0.9.92 sdharbor.sdvideo.cn/av-group/analysis-system-jre8:v0.9.92

然后就能推送了

docker push sdharbor.sdvideo.cn/av-group/analysis-system-jre8:v0.9.92


Docker 推送命令：
在项目中标记镜像：
docker tag analysis-system-jre8[:TAG] sdharbor.sdvideo.cn/av-group/analysis-system-jre8[:TAG]
例如：docker tag analysis-system-jre8:0.9.92 sdharbor.sdvideo.cn/av-group/analysis-system-jre8:0.9.92

推送镜像到当前项目：
docker push sdharbor.sdvideo.cn/av-group/analysis-system-jre8[:TAG]
例如：docker push sdharbor.sdvideo.cn/av-group/analysis-system-jre8:0.9.92


其他：
开监控 这个9999 docker也要对外暴露
-Xms2048m -Xmx2048m -Duser.timezone=Asia/Shanghai -Dfile.encoding=utf-8 -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=9999 -Dcom.sun.management.jmxremote.rmi.port=9999 -Djava.rmi.server.hostname=********* -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -jar /app/app.jar --spring.profiles.active=prod


挂载到自启动

sudo vim /etc/systemd/system/analysis-system.service

[Unit]
Description=analysis-system Docker Compose
Requires=docker.service
After=docker.service

[Service]
WorkingDirectory=/data/apps/analysis-system
ExecStart=docker compose up -d
ExecStop=docker compose down
Restart=always
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target


sudo systemctl daemon-reexec
sudo systemctl daemon-reload
sudo systemctl enable analysis-system.service
sudo systemctl start analysis-system.service




