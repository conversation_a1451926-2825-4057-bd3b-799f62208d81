2025-08-18 10:51:02 jdbc[144]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1375)
	at org.h2.jdbc.JdbcConnection.createStatement(JdbcConnection.java:206)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:164)
	at com.zaxxer.hikari.pool.HikariPool$KeepaliveTask.run(HikariPool.java:847)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-08-18 10:51:03 jdbc[45]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1375)
	at org.h2.jdbc.JdbcConnection.createStatement(JdbcConnection.java:206)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:164)
	at com.zaxxer.hikari.pool.HikariPool$KeepaliveTask.run(HikariPool.java:847)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-08-18 10:51:03 jdbc[37]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1375)
	at org.h2.jdbc.JdbcConnection.createStatement(JdbcConnection.java:206)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:164)
	at com.zaxxer.hikari.pool.HikariPool$KeepaliveTask.run(HikariPool.java:847)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-08-18 10:51:03 jdbc[156]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1375)
	at org.h2.jdbc.JdbcConnection.createStatement(JdbcConnection.java:206)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:164)
	at com.zaxxer.hikari.pool.HikariPool$KeepaliveTask.run(HikariPool.java:847)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
2025-08-18 10:56:29 jdbc[5]: exception
org.h2.jdbc.JdbcSQLNonTransientConnectionException: Database is already closed (to disable automatic closing at VM shutdown, add ";DB_CLOSE_ON_EXIT=FALSE" to the db URL) [90121-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.checkClosed(JdbcConnection.java:1375)
	at org.h2.jdbc.JdbcConnection.createStatement(JdbcConnection.java:206)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:164)
	at com.zaxxer.hikari.pool.HikariPool$KeepaliveTask.run(HikariPool.java:847)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:829)
