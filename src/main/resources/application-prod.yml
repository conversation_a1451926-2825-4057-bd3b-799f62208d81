server:
  port: 8080
spring:
  servlet:
    multipart:
      enabled: true
      max-request-size: 1024MB
      max-file-size: 1024MB
  main:
    allow-bean-definition-overriding: true
  flyway:
    enabled: true
    locations: classpath:db/migration   # 确保 Flyway 能找到 SQL 脚本
  # 数据源配置
  datasource:
    url: jdbc:h2:./h2Db/analysisDb
    username: sa
    password: 123456
    driver-class-name: org.h2.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 客户端等待连接池连接的最大毫秒数
      connection-timeout: 30000
      # 池中维护的最小空闲连接数 minIdle<0 或者 minIdle>maxPoolSize，则被重置为maxPoolSize
      minimum-idle: 200
      # 配置最大池大小
      maximum-pool-size: 5000
      # 允许连接在连接池中空闲的最长时间（以毫秒为单位）
      idle-timeout: 60000
      # 池中连接关闭后的最长生命周期（以毫秒为单位）
      max-lifetime: 500000
      # 每5分钟发送一次 ping
      keepalive-time: 300000
      # 配置从池返回的连接的默认自动提交行为。默认值为true。
      auto-commit: true
      # 连接池的名称
      pool-name: VlinkerHikariCP
      # 开启连接监测泄露
      leak-detection-threshold: 5000
      # 测试连接数据库
      connection-test-query: SELECT 1

  jackson:
    default-property-inclusion: non_null
    generator:
      write-numbers-as-strings: true
mybatis-plus:
  type-aliases-package: com.saida.services.system.**.entity
  # xxxMapper.xml 路径
  mapper-locations: classpath:/mapper/**/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: false

# 日志级别，会打印sql语句
logging:
  level:
    com.saida.services.system: debug
    com.alibaba.nacos.client.config.impl: WARN
    org.apache.rocketmq: OFF  # 关闭mq的日志 不然直接打到本地
  config: classpath:logback-spring.xml

rocketmq:
  enable: true
  enhance:
    # 启动隔离，用于激活配置类EnvironmentIsolationConfig
    # 启动后会自动在topic上拼接激活的配置文件，达到自动隔离的效果
    enabledIsolation: false
    # 隔离环境名称，拼接到topic后，topic_dev，默认空字符串
    environment: ${spring.profiles.active}
  consumer:
    group: springboot_consumer_group
    # 一次拉取消息最大值，注意是拉取消息的最大值而非消费最大值
    pull-batch-size: 10
  name-server: 223.247.152.9:9876 #测试环境
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: analysis_system_group
    # 发送消息超时时间，默认3000
    sendMessageTimeout: 10000
    # 发送消息失败重试次数，默认2
    retryTimesWhenSendFailed: 10
    # 异步消息重试此处，默认2
    retryTimesWhenSendAsyncFailed: 10
    # 消息最大长度，默认1024 * 1024 * 4(默认4M)
    #    maxMessageSize: 4096
    # 压缩消息阈值，默认4k(1024 * 4)
    compressMessageBodyThreshold: 4096
    # 是否在内部发送失败时重试另一个broker，默认false
    retryNextServer: true

oss:
  enable: true  # @ConditionalOnProperty(prefix = "minio", name = "enable", havingValue = "true")
  endpoint: http://10.1.1.63:12001
  accessKey: Sdmin
  region: dev
  secretKey: PW2KDUQyUCrQU2x5_WN
  secure: false
  bucketDefaultName: v-linker
  # 请以 / 结尾
  returnPoint: https://test-vlinker-minio-api.sdvideo.cn:48801/

vLinker:
  ding-enable: true
  ding:
    token: 40051e0d7b51796b8767cb417c39f9fa3403c62a2d3bf9cda1544fa29db028d8
    secret: SECcad4d76f13e500baf1b77e425d6db39d8c8f2a1e63fcfcf279c6a050d512c451
  nodeId: node_1
  # 算法绘图服务
  algDrawUrl: https://test-vlinker-conv-api.sdvideo.cn:48801/algorithm-system/drawImg/
grpc:
  server:
    host: host.docker.internal
    port: 18777
grpc2:
  server:
    host: stream-shot
    port: 18776
    # 后面不要加 /
    outputPath: /app/tmp
#赛达算法
saida-alg:
  enable: true
  # 算法请求地址
  reqAddress: http://127.0.0.1:18989
#赋安 算法
fu-an-alg:
  enable: false
  # 算法请求地址
  reqAddress: http://*************:16201
  # 算法回调地址
  callbackAddress: http://**************:8081

network:
  replacement:
    - id: "123"
      oldUrl: "************"
      newUrl: "*********"


confu:
  enable: true
  #  confuExePath: /Users/<USER>/Documents/CLionProjects/universeAI/cmake-build-minsizerel/confu
  confuExePath: /Users/<USER>/Documents/CLionProjects/universeAI/cmake-build-debug/confu
  frameCountToCache: 100
  imageOutputDirPath: /Users/<USER>/Documents/ideaProject/analysis-system/confuSdk/img
  aiSoPath: /Users/<USER>/Documents/CLionProjects/universeAI/cmake-build-minsizerel/libsaida_ai_library.dylib
  aiConfigJSONFilePath: /Users/<USER>/Documents/ideaProject/analysis-system/confuSdk/aa.json