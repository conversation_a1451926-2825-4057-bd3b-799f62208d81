package com.saida.analysis.enums;

/**
 * 项目导入的枚举类
 */
public enum AlgEnum {

    DETECTION_ALGORITHM_UNKNOWN("DETECTION_ALGORITHM_UNKNOWN", "DETECTION_ALGORITHM_UNKNOWN"),
    BirdDetection_Low("BirdDetection_Low", "DETECTION_ALGORITHM_BIRD_LOW_ACCURACY"),
    PlantSpeciesRecognition("PlantSpeciesRecognition", "DETECTION_ALGORITHM_BIRD_HUNAN_YONGZHOU"),
    VehicleIntrusionDetection_High("VehicleIntrusionDetection_High", "DETECTION_ALGORITHM_VEHICLE_INTRUSION_HIGH_ACCURACY"),
    VehicleIntrusionDetection_Low("VehicleIntrusionDetection_Low", "DETECTION_ALGORITHM_VEHICLE_INTRUSION_LOW_ACCURACY"),
    HumanIntrusionDetection_High("HumanIntrusionDetection_High", "DETECTION_ALGORITHM_HUMAN_INTRUSION_HIGH_ACCURACY"),
    HumanIntrusionDetection_Low("HumanIntrusionDetection_Low", "DETECTION_ALGORITHM_HUMAN_INTRUSION_LOW_ACCURACY"),
    BareSoilDetection_High("BareSoilDetection_High", "DETECTION_ALGORITHM_BARE_SOIL_HIGH_ACCURACY"),
    BoatIntrusionDetection_High("BoatIntrusionDetection_High", "DETECTION_ALGORITHM_SHIP_INTRUSION_HIGH_ACCURACY"),
    FireDetection_High("FireDetection_High", "DETECTION_ALGORITHM_FIRE_HIGH_ACCURACY"),
    IllegalConstructionDetection_High("IllegalConstructionDetection_High", "DETECTION_ALGORITHM_ILLEGAL_BUILDING_HIGH_ACCURACY"),
    IllegalAnglingDetection_Low("IllegalAnglingDetection_Low", "DETECTION_ALGORITHM_ILLEGAL_ANGLING_LOW_ACCURACY"),
    IllegalFishingDetection_Low("IllegalFishingDetection_Low", "DETECTION_ALGORITHM_ILLEGAL_FISHING_LOW_ACCURACY"),
    TrashDetection_Low("TrashDetection_Low", "DETECTION_ALGORITHM_GARBAGE_LOW_ACCURACY"),
    Species_modelScope8288Cls("Species_modelScope8288Cls", "DETECTION_ALGORITHM_SPECIES_MODEL"),
    TietaAlgos_人("TietaAlgos_人", "DETECTION_ALGORITHM_TIE_TA_HUMAN"),
    TietaAlgos_烟火("TietaAlgos_烟火", "DETECTION_ALGORITHM_TIE_TA_FIREWORK"),
    TietaAlgos_船只("TietaAlgos_船只", "DETECTION_ALGORITHM_TIE_TA_SHIP"),
    TietaAlgos_裸土("TietaAlgos_裸土", "DETECTION_ALGORITHM_TIE_TA_BARE_SOIL"),
    TietaAlgos_车辆("TietaAlgos_车辆", "DETECTION_ALGORITHM_TIE_TA_VEHICLE"),
    TietaAlgos_鸟("TietaAlgos_鸟", "DETECTION_ALGORITHM_TIE_TA_BIRD"),


    ;

    private String code;
    private String description;

    public String  getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    AlgEnum(String code, String description) {

        this.code = code;
        this.description = description;
    }

    public static String getStrByCode(String code) {
        if (code == null) {
            throw null;
        }
        for (AlgEnum value : AlgEnum.values()) {
            if (value.getCode() .equals( code))
                return value.getDescription();
        }
        return null;
    }
}
