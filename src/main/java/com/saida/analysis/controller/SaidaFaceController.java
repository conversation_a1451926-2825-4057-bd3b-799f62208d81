package com.saida.analysis.controller;

import com.alibaba.fastjson.JSON;
import com.saida.analysis.config.SaiDaAlgConfig;
import com.saida.analysis.dto.SaidaRepose;
import com.saida.analysis.dto.SaidaRequest;
import com.saida.analysis.util.DtoResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
@RestController
@RequestMapping("saidaFace")
public class SaidaFaceController {

    /**
     * {
     * }
     */
    @Data
    public static class GetFaceFeatureReq {
        private String traceId;
        // 设备的唯一标识
        private String deviceId;
        // 图片的base64
        private String base64Image;
        // 说明：为N_DET_FACE时，只会输出图上所有人脸框，为N_EXT_FACE时，会输出图上所有人脸框+对应的人脸特征值
        private String algCode;
    }

    @Data
    public static class GetFaceFeatureResp {
        //
        private SaidaRepose saidaRepose;
    }


    private static final RestTemplate saidaRestTemplate = createRestTemplate(2000, 2000);


    // 创建 RestTemplate，并配置超时
    public static RestTemplate createRestTemplate(int connectTimeout, int readTimeout) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout); // 连接超时（毫秒）
        factory.setReadTimeout(readTimeout);       // 数据读取超时（毫秒）
        return new RestTemplate(factory);
    }

    @Autowired(required = false)
    private SaiDaAlgConfig saiDaAlgConfig;


    private static final HttpHeaders headers = new HttpHeaders();

    /**
     *
     */
    @PostMapping("/getFaceFeature")
    public DtoResult<GetFaceFeatureResp> getFaceFeature(@RequestBody GetFaceFeatureReq getFaceFeatureReq) {
        long start = System.currentTimeMillis();
        log.info("getFaceFeature: traceId:{} algCode:{} devId:{},imgLen:{}", getFaceFeatureReq.getTraceId(), getFaceFeatureReq.getAlgCode(), getFaceFeatureReq.getDeviceId(), getFaceFeatureReq.getBase64Image().length());
        SaidaRequest saidaRequest = SaidaRequest.builder()
                .ipc_code(getFaceFeatureReq.getDeviceId())
                .algo_code(getFaceFeatureReq.getAlgCode())
                .pic_url(getFaceFeatureReq.getBase64Image())
                .build();
        log.info("getFaceFeature traceId:{} 开始识别 deviceId:{} alg:{}",getFaceFeatureReq.getTraceId(), getFaceFeatureReq.getDeviceId(), getFaceFeatureReq.getAlgCode());
        if (saiDaAlgConfig == null || !saiDaAlgConfig.getEnable()) {
            return DtoResult.error("请先配置saida-alg");
        }
        SaidaRepose saidaRepose = null;
        // 获取或创建异步 gRPC 通道
        try {
            // 创建请求体，这里是一个 JSON 字符串或一个对象
            String requestBody = JSON.toJSONString(saidaRequest);
            // 创建 HttpEntity，封装请求头和请求体
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            // 发起 POST 请求
            ResponseEntity<String> response = saidaRestTemplate
                    .exchange(saiDaAlgConfig.getReqAddress(),
                            HttpMethod.POST, entity, String.class);
            // 获取响应状态码
            int statusCode = response.getStatusCodeValue();
            if (statusCode == 200) {
                String resStr = "";
                resStr = response.getBody();
                if (StringUtils.isNotBlank(resStr)) {
                    saidaRepose = JSON.parseObject(resStr, SaidaRepose.class);
                    log.info("getFaceFeature traceId:{} 算法分析成功 cost:{}ms deviceId:{} alg:{} ,msg.len:{}",getFaceFeatureReq.getTraceId(), System.currentTimeMillis() - start, getFaceFeatureReq.getDeviceId(), getFaceFeatureReq.getAlgCode(), resStr.length());
                } else {
                    log.info("getFaceFeature traceId:{}  算法分析失败 resStr is null deviceId:{} alg:{} ",getFaceFeatureReq.getTraceId(), getFaceFeatureReq.getDeviceId(), getFaceFeatureReq.getAlgCode());
                }
            }
        } catch (Exception e) {
            log.info("getFaceFeature traceId:{}   算法分析失败 出现特殊的异常 deviceId:{} alg:{} ,msg:{}",getFaceFeatureReq.getTraceId(), getFaceFeatureReq.getDeviceId(), getFaceFeatureReq.getAlgCode(), e.getMessage(), e);
        }
        if (saidaRepose == null) {
            return DtoResult.error("识别失败");
        }
        if (saidaRepose.getCode() != 0) {
            return DtoResult.error("识别失败 消息：" + saidaRepose.getMsg());
        }
        GetFaceFeatureResp getFaceFeatureResp = new GetFaceFeatureResp();
        getFaceFeatureResp.setSaidaRepose(saidaRepose);
        return DtoResult.ok(getFaceFeatureResp);
    }

    public static void main(String[] args) {
        SaiDaAlgConfig saiDaAlgConfig = new SaiDaAlgConfig();
        saiDaAlgConfig.setReqAddress("http://*********:18989");
        String url = "/Users/<USER>/Downloads/人员.jpg";
        // 从文件里面读取base64
        String base64 = "";
        try {
            byte[] bytes = Files.readAllBytes(Paths.get(url));
            base64 = new String(org.apache.commons.codec.binary.Base64.encodeBase64(bytes));
        } catch (Exception e) {
            e.printStackTrace();
        }
        SaidaRequest saidaRequest = SaidaRequest.builder()
                .ipc_code("123")
                .algo_code("N_EXT_FACE")
                .pic_url(base64)
                .build();
        SaidaRepose saidaRepose = null;
        // 获取或创建异步 gRPC 通道
        try {
            // 创建请求体，这里是一个 JSON 字符串或一个对象
            String requestBody = JSON.toJSONString(saidaRequest);
            // 创建 HttpEntity，封装请求头和请求体
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            // 发起 POST 请求
            ResponseEntity<String> response = saidaRestTemplate
                    .exchange(saiDaAlgConfig.getReqAddress(),
                            HttpMethod.POST, entity, String.class);
            // 获取响应状态码
            int statusCode = response.getStatusCodeValue();
            if (statusCode == 200) {
                String resStr = "";
                resStr = response.getBody();
                if (StringUtils.isNotBlank(resStr)) {
                    saidaRepose = JSON.parseObject(resStr, SaidaRepose.class);
                }
                log.info("getFaceFeature 算法分析成功 ,msg:{}", resStr);
            }
        } catch (Exception e) {
            log.info("getFaceFeature 算法分析失败 出现特殊的异常,msg:{}", e.getMessage(), e);
        }
    }
}
