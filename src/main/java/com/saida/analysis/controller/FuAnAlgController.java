package com.saida.analysis.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.analysis.dto.AnalysisDto;
import com.saida.analysis.dto.GrpcAlgorithmResultDto;
import com.saida.analysis.dto.TaskDto;
import com.saida.analysis.mqListener.VideoStreamMessageListener;
import com.saida.analysis.service.AnalysisService;
import com.saida.analysis.service.FileService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("fuAnAlg")
public class FuAnAlgController {

    /**
     * {
     * “result”: “{\”appType\”:\”weijian_gt\”,\”picUrl\”:\”http://192.168.154.12:1111/cvrpic20?ZGlkPTAxMDBHNjQyODU5NDQ0OEZFNDI4M0Q4NzI0QjYyODk5MTZCQUQzOTQyNTY0MzAwMDAwMDA0ODYwMDAwMDAwMDAmYmlkPTQ4NiZwaWQ9ODM1NjgxNDY4JnB0aW1lPTE2NDI4NjcyOTk=\”,\”collectTime\”:1642838512410,\”analyzeAreas\”:[{\”polygonId\”:\”********************************\”,\”polygon\”:\”[[2,5],[2,1431],[2557,1436],[2555,354],[1094,241]]\”,\”recogSmartTypes\”:["电动车","摩托车","汽车"]}],\”imgWidth\”:2560,\”imgHeight\”:1440,\”finishedTime\”:\”1642838572000\”,\”errorCode\”:0,\”taskId\”:\”temp_244081debe7f437393657403112d8cf7\”,\”intelligentResultTypes\”:[”摩托车”],\”violationAreas\”:[{\”confidence\”:97,\”intelligentResultType\”:”摩托车”,\”xmin\”:870,\”xmax\”:1470,\”ymin\”:746,\”ymax\”:959,\”polygonId\”:\”********************************\”}]}”
     * }
     */
    @Data
    public static class FuAnAlgDto {
        //结果json字符串，关键字段如下
        //violationArea – 描述检测出违规区域信息
        //confidence – 确认度[0,100]
        //intelligentResultType – 违规类型
        private String result;
        //透传字符串
        private String extra;
    }

    @Data
    public static class Result {
        // 不知道干嘛的 weijian_gt
        private String appType;
        // 看起来是原图地址
        private String picUrl;
        // 一个时间戳 1642838512410
        private Long collectTime;
        // 传入的分析框
        private List<AnalyzeArea> analyzeAreas;
        // 图片宽
        private Integer imgWidth;
        // 图片高
        private Integer imgHeight;
        // 也是一个时间戳 1642838572000
        private Long finishedTime;
        // 无意义 0
        private Integer errorCode;
        // 一个任务id标识
        private String taskId;
        //  识别到的算法类型文本 例如 摩托车
        private List<String> intelligentResultTypes;
        // 识别到的算法集合
        private List<ViolationArea> violationAreas;
    }

    @Data
    public static class AnalyzeArea {
        private String polygonId;
        private List<List<Integer>> polygon;
        private List<String> recogSmartTypes;
    }

    @Data
    public static class ViolationArea {
        // 信任度 0-100
        private Integer confidence;
        // 算法类型 例如 摩托车
        private String intelligentResultType;
        // 4个点位
        private Integer xmin;
        private Integer xmax;
        private Integer ymin;
        private Integer ymax;
        // 区域类型
        private String polygonId;
    }

    @Resource
    private AnalysisService analysisService;
    @Resource
    private FileService fileService;


    @PostMapping("/doAnalysis")
    public JSONObject doAnalysis(@RequestBody FuAnAlgDto fuAnAlgDto) {
        log.info("fuAnAlgDto:{}", JSON.toJSONString(fuAnAlgDto));
        JSONObject object = new JSONObject();
        object.put("code", -1);
        Result result = JSONObject.parseObject(fuAnAlgDto.getResult(), Result.class);
        log.info("fuAnAlgDto-result:{}", JSON.toJSONString(result));
        if (result == null || result.getPicUrl() == null) {
            object.put("msg", "没什么参数的请求、忽略");
            return object;
        }
        JSONObject jsonObject = JSONObject.parseObject(fuAnAlgDto.getExtra());
        Long deviceId = 0L;
        try {
            deviceId = jsonObject.getLong("deviceId");
            String timePrefix = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd/HH/mm");
            JSONObject analysisDto = jsonObject.getJSONObject("analysisDto");
            AnalysisDto analysisDtoBean = analysisDto.toJavaObject(AnalysisDto.class);
            analysisDtoBean.setAlgorithmsResultTime(System.currentTimeMillis());
            log.info("deviceId:{} -> 算法分析耗时->{} {}个结果", deviceId,
                    analysisDtoBean.getDoAnalysisThreadStartTime() - System.currentTimeMillis(), result.getViolationAreas().size());
            String imageUrl = fileService.copyObject(jsonObject.getString("ossObjKey"), "alarm/t3/" + timePrefix + "/" + IdWorker.getId() + ".jpg");
            TaskDto taskDto = VideoStreamMessageListener.getDeviceTasksMap().get(deviceId);
            if (taskDto == null) {
                log.error("taskDto is null :{}", deviceId);
                return object;
            }
            analysisDtoBean.setImageUrl(imageUrl);
            analysisDtoBean.setTaskDto(taskDto);
            List<ViolationArea> violationAreas = result.getViolationAreas();
            if (violationAreas != null && !violationAreas.isEmpty()) {
                List<GrpcAlgorithmResultDto> algorithmsResult = new ArrayList<>();
                for (ViolationArea violationArea : violationAreas) {
                    GrpcAlgorithmResultDto resultDto = new GrpcAlgorithmResultDto();
                    resultDto.setLabelName(violationArea.getIntelligentResultType());
                    resultDto.setProb((double) violationArea.getConfidence() / 100);
                    resultDto.setMinx(violationArea.getXmin());
                    resultDto.setMiny(violationArea.getYmin());
                    resultDto.setMaxx(violationArea.getXmax());
                    resultDto.setMaxy(violationArea.getYmax());
                    algorithmsResult.add(resultDto);
                }
                analysisDtoBean.setAlgorithmsResult(algorithmsResult);
                List<GrpcAlgorithmResultDto> filteredResults = AnalysisService.calculationResults(analysisDtoBean, jsonObject.getLong("taskId"));
                if (!filteredResults.isEmpty()) {
                    analysisService.alarmMessageToMq(analysisDtoBean, jsonObject.getLong("taskId"), filteredResults);
                }
            }
        } catch (Exception e) {
            log.error("doAnalysis error:", e);
        } finally {
            fileService.deleteObject(jsonObject.getString("ossObjKey"));
        }

        object.put("code", 0);
        return object;
    }
}
