package com.saida.analysis.algHall;

import com.saida.analysis.algHall.saidaPlayer.Message;
import com.saida.analysis.algHall.saidaPlayer.MessageTypes;
import com.saida.analysis.algHall.saidaPlayer.PSMuxer;
import com.saida.analysis.service.FileService;
import com.saida.analysis.util.SpringUtil;
import com.saida.services.system.pb.OpenCommonEnum;
import com.saida.services.system.pb.OpenStreamMessage;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 告警+录制房间
 */
@Slf4j
public class AlarmRecordRoom {
    private final Long deviceId;

    /**
     * 播放回复
     */
    private final OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer;
    /**
     * 媒体信息
     */
    private OpenStreamMessage.StreamMediaInfo mediaInfo;
    /**
     * 播放线程
     */
    private final Thread playThread = new Thread(this::play);

    /**
     * 第一针视频时间
     */
    private long firstCurrentTimestamp = -1;
    /**
     * 最大时间限制（单位：秒）
     */
    @Setter
    private int recordTime = -1;
    /**
     * 存储数据的链表
     */
    private final LinkedList<PsData> list = new LinkedList<>();
    /**
     * 存储正在录制的时间
     */
    private final List<Long> recordingTimeList = Collections.synchronizedList(new ArrayList<>());

    AlarmRecordRoom(Long deviceId, OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer) {
        this.deviceId = deviceId;
        this.streamReplyForPlayer = streamReplyForPlayer;
        playThread.setName("algRoom-" + deviceId);
        playThread.start();
    }


    private OutputStream output;

    private void play() {
        String addr = streamReplyForPlayer.getAddr().replace("60.107", "30.219");
        log.info("开始播放 地址:{}", addr);
        // 播放
        String[] split = addr.split(":");
        try (Socket socket = new Socket(split[0], Integer.parseInt(split[1]))) {
            output = socket.getOutputStream();
            InputStream input = socket.getInputStream();
            // 发送握手消息
            output.write(streamReplyForPlayer.getHandshakeData().toByteArray());
            log.info("发送握手消息");
            // 循环读取服务器响应
            while (true) {
                // 先读取12字节头部
                byte[] header = new byte[12];
                readFully(input, header);
                Message.Header headerObj = Message.Header.newHeader(header);
                // 继续读取后续内容
                byte[] body = new byte[headerObj.contentLength];
                readFully(input, body);
                switch (headerObj.type) {
                    case MessageTypes.MajorTypeMedia:
                        PsData psData = PsData.builder()
                                .timestamp(headerObj.timestamp / 90)
                                .header(headerObj)
                                .body(body)
                                .build();
                        // 音视频包
                        switch (headerObj.subType) {
                            case Message.Header.MEDIA_TYPE_RAW_VIDEO_KEY_FRAME:
                                psData.setKeyFrame(true);
                                psData.setDataType(1);
                                log.info("收到关键帧");
                                break;
                            case Message.Header.MEDIA_TYPE_PROGRAM_STREAM:
                                log.info("收到视频数据? 不要这个数据");
                                continue;
                            case Message.Header.MEDIA_TYPE_RAW_VIDEO:
                                psData.setDataType(1);
                                break;
                            case Message.Header.MEDIA_TYPE_RAW_AUDIO:
                                psData.setDataType(2);
                                break;
                        }
                        if (recordTime != -1) {
                            addMediaData(psData);
                        }
                        break;
                    case MessageTypes.MajorTypeMediaControl:
                    case MessageTypes.MajorTypeHandshake:
                    case MessageTypes.MajorTypeSignaling:
                    case MessageTypes.MajorTypeAlarm:
                    case MessageTypes.MajorTypeData:
                    case MessageTypes.MajorTypeFile:
                        break;
                    case MessageTypes.MajorTypeMediaAddonData:
                        OpenStreamMessage.StreamMediaInfo.Builder builder = OpenStreamMessage.StreamMediaInfo.newBuilder();
                        ByteBuffer bb = ByteBuffer.wrap(body);
                        bb.order(ByteOrder.LITTLE_ENDIAN);
                        // 读取固定字段
                        builder.setAudioFormat(OpenCommonEnum.AudioFormat.forNumber(bb.getInt()));
                        builder.setVideoFormat(OpenCommonEnum.VideoFormat.forNumber(bb.getInt()));
                        builder.setAudioSampleRate(bb.getInt());
                        builder.setAudioChannels(bb.get() & 0xFF);
                        int AddonDataLength = bb.get() & 0xFF;
                        int AudioCodecDescLength = bb.get() & 0xFF;
                        int VideoCodecDescLength = bb.get() & 0xFF;
                        if (AudioCodecDescLength > 0) {
                            byte[] audioDescBytes = new byte[AudioCodecDescLength];
                            bb.get(audioDescBytes);
                            builder.setAudioCodecDesc(new String(audioDescBytes));
                        }
                        if (VideoCodecDescLength > 0) {
                            byte[] videoDescBytes = new byte[VideoCodecDescLength];
                            bb.get(videoDescBytes);
                            builder.setVideoCodecDesc(new String(videoDescBytes));
                        }
                        mediaInfo = builder.build();
                        log.info("接收到媒体信息:{}", mediaInfo);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("播放失败:{}", e.getMessage(), e);
        } finally {
            log.info("不知道什么原因播放结束了 从新拉起播放线程 不过要等待个1s");
            try {
                Thread.sleep(1000);
            } catch (Exception ignored) {
            }
            playThread.start();
        }
    }


    private void addMediaData(PsData psData) {
        if (firstCurrentTimestamp == -1) {
            firstCurrentTimestamp = System.currentTimeMillis();
        }
        // 当前时间点
        long currentTimestamp = psData.timestamp;
        // 1. 添加新的音视频数据到 list 中
        list.add(psData);
        log.info("添加数据 暂存长度:{},类型:{},currentTimestamp:{}", list.size(), psData.dataType, currentTimestamp);
        // 清理过期的ps数据
        cleanOldData(currentTimestamp);
        // 处理时间点数据
        checkRecordingTimePoints(currentTimestamp);
    }

    private void cleanOldData(long currentTimestamp) {
        // 目标是找到一个位置，从该位置开始满足两个条件：
        // 1. 是视频关键帧
        // 2. 从该帧到 currentTimestamp 的跨度 >= 5000ms
        Iterator<PsData> iterator = list.iterator();
        int startIndex = -1;
        long timestampThreshold = currentTimestamp - (recordTime * 1000L);
        int index = 0;

        while (iterator.hasNext()) {
            PsData data = iterator.next();
            if (data.dataType == 1 && data.isKeyFrame && data.timestamp <= timestampThreshold) {
                // 找到了一个合适的关键帧
                startIndex = index;
            }
            index++;
        }

        if (startIndex >= 0) {
            // 删除 startIndex 之前的数据
            for (int i = 0; i < startIndex; i++) {
                list.poll(); // 移除旧数据
            }
        } else {
            // 没有找到满足条件的关键帧，就保留最早一个关键帧开始的所有数据
            // 否则可能视频数据无法解码
            while (!list.isEmpty()) {
                PsData head = list.peek();
                if (head.dataType == 1 && head.isKeyFrame) {
                    break;
                } else {
                    list.poll();
                }
            }
        }
    }


    private void checkRecordingTimePoints(long currentTimestamp) {
        // 遍历 recordingTimeList，如果当前时间戳大于时间点+5秒，生成文件并移除该时间点
        Iterator<Long> iterator = recordingTimeList.iterator();
        while (iterator.hasNext()) {
            Long timePoint = iterator.next();
            if (currentTimestamp > timePoint + (recordTime * 1000L)) {
                log.info("生成文件：{}", timePoint);
                // 在这里做你想要的业务操作
                processDataForTimePoint(timePoint);
                log.info("文件生成完成：{}", timePoint);
                // 移除时间点
                iterator.remove();
            }
        }
    }

    private void processDataForTimePoint(long timePoint) {
        // 处理list中的ps数据
        List<PsData> psDataToProcess = new ArrayList<>(list);
        // 在此处调用你的业务逻辑来处理psDataToProcess
        log.info("处理数据：{}个ps数据，时间点：{}", psDataToProcess.size(), timePoint);
        if (psDataToProcess.isEmpty()) {
            log.info("上传文件为空");
            return;
        }
        try {
            long startTime = psDataToProcess.get(0).timestamp;
            long endTime = psDataToProcess.get(psDataToProcess.size() - 1).timestamp;
            // 持续秒数
            int duration = (int) ((endTime - startTime) / 1000);
            String id = (firstCurrentTimestamp + startTime) / 1000 + "_" + duration;
            log.info("上传文件编号:{},数量：{} ,时间:{}-{}", id, psDataToProcess.size(), startTime, endTime);
            uploadToS3(psDataToProcess, id);
        } catch (IOException e) {
            log.error("告警触发上传失败:{}", e.getMessage(), e);
        }
    }

    void addRecordingTimePoint(long currentTimestamp) {
        log.info("加入时间点：{}", currentTimestamp);
        // 如果 recordingTimeList 为空，加入当前时间戳
        if (recordingTimeList.isEmpty()) {
            recordingTimeList.add(currentTimestamp);
        }
    }


    /**
     * 工具方法：确保完全读取到指定长度的数据
     */
    private static void readFully(InputStream in, byte[] buffer) throws Exception {
        int bytesRead = 0;
        while (bytesRead < buffer.length) {
            int result = in.read(buffer, bytesRead, buffer.length - bytesRead);
            if (result == -1) {
                throw new RuntimeException("Stream closed before reading fully ? 读取失败了 我被T了");
            }
            bytesRead += result;
        }
    }

    private FileService fileService;

    public void uploadToS3(List<PsData> frameList, String idx) throws IOException {
        String psKey = "video/ps/" + deviceId + "/" + idx + ".ps";
        String idxKey = "video/ps/" + deviceId + "/" + idx + ".idx";
        // 1. 拼接 PS 数据
        byte[] psData = buildPSFile(frameList);
        // 2. 生成 idx 数据
        String idxData = buildIdxData(frameList, psData.length);
        if (fileService == null) {
            fileService = SpringUtil.getBean(FileService.class);
        }
        // 3. 上传 ps 文件
        fileService.upload(psData, psKey);
        // 3. 上传 ps 文件
        fileService.upload(idxData.getBytes(StandardCharsets.UTF_8), idxKey);
    }

    public byte[] buildPSFile(List<PsData> frameList) throws IOException {
        int audioFormat = 0;
        switch (mediaInfo.getAudioFormat()) {
            case AUDIO_FORMAT_G711A: // PCMA
                audioFormat = 0x90;
                break;
            case AUDIO_FORMAT_G711U: // PCMU
                audioFormat = 0x91;
                break;
            case AUDIO_FORMAT_G722_1: // G722.1
                audioFormat = 0x92;
                break;
            case AUDIO_FORMAT_G723_1: // G723.1
                audioFormat = 0x93;
                break;
            case AUDIO_FORMAT_G729: // G729
                audioFormat = 0x99;
                break;
            case AUDIO_FORMAT_AAC: // AAC
                audioFormat = 0x0F;
                break;
            default:
                break;
        }
        int videoFormat = mediaInfo.getVideoFormat() == OpenCommonEnum.VideoFormat.VIDEO_FORMAT_AVC ? 0x1B : 0x24;
        PSMuxer muxer = new PSMuxer(audioFormat, videoFormat);
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        for (PsData frame : frameList) {
            output.write(addLengthHeader(muxer.mux(frame)));
        }
        return output.toByteArray();
    }


    public static byte[] addLengthHeader(byte[] body) {
        // 创建4字节长度头部
        ByteBuffer lengthBuffer = ByteBuffer.allocate(4);
        lengthBuffer.order(ByteOrder.LITTLE_ENDIAN); // 设置为小端字节序
        lengthBuffer.putInt(body.length);
        // 合并长度和数据
        byte[] lengthBytes = lengthBuffer.array();
        byte[] combinedData = new byte[lengthBytes.length + body.length];
        System.arraycopy(lengthBytes, 0, combinedData, 0, lengthBytes.length);
        System.arraycopy(body, 0, combinedData, lengthBytes.length, body.length);
        return combinedData;
    }

    private String buildIdxData(List<PsData> frameList, long totalFileLength) {
        StringBuilder sb = new StringBuilder();
        sb.append("1.0.0\n");
//        sb.append("##v avc1.64001f\n");
        sb.append(String.format("##v %s ", mediaInfo.getVideoCodecDesc()));
        sb.append("\n");
//        sb.append("##a PCMA 8000 1\n");
        String audioCodecDesc = getAudioCodecDesc();
        sb.append(String.format("##a %s ", audioCodecDesc));
        sb.append("\n");
        sb.append("\n");

        long offset = 0;
        Set<Long> writtenSeconds = new HashSet<>();
        long firstSeconds = -1;
        for (PsData frame : frameList) {
            if (frame.dataType == 1 && frame.isKeyFrame) { // 视频关键帧
                long seconds = frame.timestamp / 1000;
                if (firstSeconds == -1) {
                    firstSeconds = seconds;
                    seconds = 0;
                } else {
                    seconds = seconds - firstSeconds;
                }
                if (!writtenSeconds.contains(seconds)) {
                    sb.append("@").append(seconds).append("-").append(offset).append("\n");
                    writtenSeconds.add(seconds);
                }
            }
            offset += frame.body.length;
        }
        sb.append("##fileLen:").append(totalFileLength).append("\n");
        return sb.toString();
    }

    private String getAudioCodecDesc() {
        String audioCodecDesc = mediaInfo.getAudioCodecDesc();
        if (StringUtils.isBlank(audioCodecDesc)) {
            switch (mediaInfo.getAudioFormat()) {
                case AUDIO_FORMAT_SVAC:
                    audioCodecDesc = String.format("%s %d %d", "SVAC", mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
                    break;
                case AUDIO_FORMAT_AAC:
                    audioCodecDesc = String.format("%s %d %d", "AAC", mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
                    break;
                case AUDIO_FORMAT_G711A:
                    // 就是 PCMA
                    audioCodecDesc = String.format("%s %d %d", "PCMA", mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
                    break;
                case AUDIO_FORMAT_G711U:
                    // 就是 PCMU
                    audioCodecDesc = String.format("%s %d %d", "PCMU", mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
                    break;
                case AUDIO_FORMAT_G722_1:
                    audioCodecDesc = String.format("%s %d %d", "G722.1", mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
                    break;
                case AUDIO_FORMAT_G723_1:
                    audioCodecDesc = String.format("%s %d %d", "G723.1", mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
                    break;
                case AUDIO_FORMAT_G729:
                    audioCodecDesc = String.format("%s %d %d", "G729", mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
                    break;
            }
        } else {
            audioCodecDesc = String.format("%s %d %d", mediaInfo.getAudioCodecDesc(), mediaInfo.getAudioSampleRate(), mediaInfo.getAudioChannels());
        }
        return audioCodecDesc;
    }

}
