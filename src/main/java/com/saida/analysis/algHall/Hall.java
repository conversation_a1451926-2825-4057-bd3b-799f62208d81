package com.saida.analysis.algHall;

import com.saida.services.system.pb.OpenStreamMessage;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 算法房间大厅
 */
public class Hall {

    /**
     * 告警+录制房间
     * key：设备id+通道id
     */
    private static final ConcurrentHashMap<Long, AlarmRecordRoom> recordRooms = new ConcurrentHashMap<>();


    public static void addRoom(Long deviceId, OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer) {
        recordRooms.put(deviceId, new AlarmRecordRoom(deviceId, streamReplyForPlayer));
    }

    public static void removeRoom(Long deviceId) {
        recordRooms.remove(deviceId);
    }

    public static void addRecordingTimePoint(Long deviceId, long currentTimestamp) {
        recordRooms.get(deviceId).addRecordingTimePoint(currentTimestamp);
    }

    public static void setRecordTime(Long deviceId, int recordTime) {
        recordRooms.get(deviceId).setRecordTime(recordTime);
    }
}
