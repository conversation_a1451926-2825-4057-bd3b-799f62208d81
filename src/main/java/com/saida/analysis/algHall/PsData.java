package com.saida.analysis.algHall;

import com.saida.analysis.algHall.saidaPlayer.Message;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PsData {
    // 1视频 2音频
    public int dataType;
    // 是否关键帧
    public boolean isKeyFrame = false;
    // 从0开始 单位 ms(毫秒)  正常情况下 40ms 一条数据
    public long timestamp;
    // 消息头
    public Message.Header header;
    // 消息体
    public byte[] body;
}
