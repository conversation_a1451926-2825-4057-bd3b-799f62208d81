package com.saida.analysis.algHall.saidaPlayer;


import com.saida.analysis.algHall.PsData;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;


public class PSMuxer {
    private static final String VERSION = "1.0.0";
    private static final int PACK_HEADER_LENGTH = 14;
    private static final int AV_SYSTEM_HEADER_LENGTH = 18;
    private static final int VIDEO_ONLY_SYSTEM_HEADER_LENGTH = 15;
    private static final int AV_MAP_HEADER_LENGTH = 51;
    private static final int VIDEO_ONLY_MAP_HEADER_LENGTH = 47;
    private static final int PES_PACKET_HEADER_LENGTH = 14;

    private static final int[] AAC_SAMPLE_RATE_TABLE = {
            96000, 88820, 64000, 48000, 44100, 32000, 24000, 22050, 16000,
            12000, 11025, 8000, 7350, 0, 0, 0
    };

    private static final byte[] PACK_HEADER = {
            0x00, 0x00, 0x01, (byte) 0xBA,
            0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x04, (byte) 0xD9,
            (byte) 0x8B, 0x00
    };

    private static final byte[] SYSTEM_HEADER_WITH_AUDIO = {
            0x00, 0x00, 0x01, (byte) 0xBB,
            0x00, 0x0C, (byte) 0x82, 0x6C,
            (byte) 0xC5, 0x04, 0x21, (byte) 0xFF,
            (byte) 0xE0, (byte) 0xE1, 0x00, (byte) 0xC0,
            (byte) 0xC0, 0x20
    };

    private static final byte[] SYSTEM_HEADER_WITHOUT_AUDIO = {
            0x00, 0x00, 0x01, (byte) 0xBB,
            0x00, 0x09, (byte) 0x82, 0x6C,
            (byte) 0xC5, 0x00, 0x21, (byte) 0xFF,
            (byte) 0xE0, (byte) 0xE1, 0x00
    };

    private static final byte[] MAP_WITH_AUDIO = {
            0x00, 0x00, 0x01, (byte) 0xBC,
            0x00, 0x2D, (byte) 0x81, 0x01,
            0x00, 0x1B, 0x70, 0x79,
            0x73, (byte) 0xE4, (byte) 0xB9, (byte) 0x9F,
            (byte) 0xE4, (byte) 0xB8, (byte) 0x8D, (byte) 0xE7,
            (byte) 0x9F, (byte) 0xA5, (byte) 0xE9, (byte) 0x81,
            (byte) 0x93, (byte) 0xE8, (byte) 0xBF, (byte) 0x99,
            (byte) 0xE6, (byte) 0x98, (byte) 0xAF, (byte) 0xE5,
            (byte) 0xB9, (byte) 0xB2, (byte) 0xE5, (byte) 0x98,
            (byte) 0x9B, 0x00, 0x08, 0x00,
            (byte) 0xE0, 0x00, 0x00, 0x00,
            (byte) 0xC0, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00
    };

    private static final byte[] MAP_WITHOUT_AUDIO = {
            0x00, 0x00, 0x01, (byte) 0xBC,
            0x00, 0x29, (byte) 0x81, 0x01,
            0x00, 0x1B, 0x70, 0x79,
            0x73, (byte) 0xE4, (byte) 0xB9, (byte) 0x9F,
            (byte) 0xE4, (byte) 0xB8, (byte) 0x8D, (byte) 0xE7,
            (byte) 0x9F, (byte) 0xA5, (byte) 0xE9, (byte) 0x81,
            (byte) 0x93, (byte) 0xE8, (byte) 0xBF, (byte) 0x99,
            (byte) 0xE6, (byte) 0x98, (byte) 0xAF, (byte) 0xE5,
            (byte) 0xB9, (byte) 0xB2, (byte) 0xE5, (byte) 0x98,
            (byte) 0x9B, 0x00, 0x04, 0x00,
            (byte) 0xE0, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00
    };

    private static final byte[] PES_PACKET_FOR_AUDIO = {
            0x00, 0x00, 0x01, (byte) 0xC0,
            0x00, 0x00, (byte) 0x83, (byte) 0x80,
            0x05, 0x00, 0x00, 0x00,
            0x00, 0x00
    };

    private static final byte[] PES_PACKET_FOR_VIDEO = {
            0x00, 0x00, 0x01, (byte) 0xE0,
            0x00, 0x00, (byte) 0x83, (byte) 0x80,
            0x05, 0x00, 0x00, 0x00,
            0x00, 0x00
    };

    private final byte[] packHeader = PACK_HEADER.clone();
    private final byte[] systemHeaderWithAudio = SYSTEM_HEADER_WITH_AUDIO.clone();
    private final byte[] systemHeaderWithoutAudio = SYSTEM_HEADER_WITHOUT_AUDIO.clone();
    private final byte[] mapWithAudio = MAP_WITH_AUDIO.clone();
    private final byte[] mapWithoutAudio = MAP_WITHOUT_AUDIO.clone();
    private final byte[] pesPacketForAudio = PES_PACKET_FOR_AUDIO.clone();
    private final byte[] pesPacketForVideo = PES_PACKET_FOR_VIDEO.clone();
    private final byte[] pesPacketForOverflowVideo = PES_PACKET_FOR_VIDEO.clone();

    private byte audioFmt = 0;
    private byte videoFmt = 0;
//    private boolean isFirstFrame = true;

    private PSMuxer() {
    }


    public PSMuxer(int audioFormat, int videoFormat) {
        // 初始化PES包
        pesPacketForOverflowVideo[4] = (byte) 0xFF;
        pesPacketForOverflowVideo[5] = (byte) 0xFF;
        audioFmt = (byte) (audioFormat & 0xFF);
        videoFmt = (byte) (videoFormat & 0xFF);
        updateMap();
    }

    public byte[] mux(List<PsData> frameList) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        for (PsData frame : frameList) {
            if (frame.dataType == 1 && videoFmt != 0) { // 视频
                feedVideoData(frame, baos);
            } else if (frame.dataType == 2 && audioFmt != 0) { // 音频
                feedAudioData(frame, baos);
            }
        }
        return baos.toByteArray();
    }

    public byte[] mux(PsData psData) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        if (psData.dataType == 1 && videoFmt != 0) { // 视频
            feedVideoData(psData, baos);
        } else if (psData.dataType == 2 && audioFmt != 0) { // 音频
            feedAudioData(psData, baos);
        }
        return baos.toByteArray();
    }

    private void feedAudioData(PsData frame, ByteArrayOutputStream baos) throws IOException {
        long timestamp = frame.timestamp * 90; // 毫秒转为90kHz
        updateTimestampInPackHeaderAndPESHeader(packHeader, pesPacketForAudio, timestamp);
        int pesLen = PES_PACKET_HEADER_LENGTH - 6 + frame.body.length;
        pesPacketForAudio[4] = (byte) (pesLen >> 8);
        pesPacketForAudio[5] = (byte) pesLen;
        baos.write(packHeader);
        baos.write(pesPacketForAudio);
        baos.write(frame.body);
    }

    private void feedVideoData(PsData frame, ByteArrayOutputStream baos) throws IOException {
        long timestamp = frame.timestamp * 90; // 毫秒转为90kHz
        boolean isKeyFrame = frame.isKeyFrame || keyframeCheck(frame.body);
        updateTimestampInPackHeaderAndPESHeader(packHeader, pesPacketForVideo, timestamp);
        int pesLen = PES_PACKET_HEADER_LENGTH - 6 + frame.body.length;
        if (pesLen < 65536) {
            pesPacketForVideo[4] = (byte) (pesLen >> 8);
            pesPacketForVideo[5] = (byte) pesLen;
            baos.write(packHeader);
            if (isKeyFrame) {
                baos.write(audioFmt != 0 ? systemHeaderWithAudio : systemHeaderWithoutAudio);
                baos.write(audioFmt != 0 ? mapWithAudio : mapWithoutAudio);
            }
            baos.write(pesPacketForVideo);
            baos.write(frame.body);
        } else {
            handleLargeVideoFrame(frame.body, timestamp, isKeyFrame, baos);
        }
    }

    private void handleLargeVideoFrame(byte[] rawData, long timestamp, boolean isKeyFrame, ByteArrayOutputStream baos) throws IOException {
        updateTimestampInPESHeader(pesPacketForOverflowVideo, timestamp);
        baos.write(packHeader);
        if (isKeyFrame) {
            baos.write(audioFmt != 0 ? systemHeaderWithAudio : systemHeaderWithoutAudio);
            baos.write(audioFmt != 0 ? mapWithAudio : mapWithoutAudio);
        }
        int remainingLen = rawData.length;
        int offset = 0;
        while (remainingLen + PES_PACKET_HEADER_LENGTH > 65535 + 6) {
            baos.write(pesPacketForOverflowVideo);
            baos.write(rawData, offset, 65535 - PES_PACKET_HEADER_LENGTH + 6);
            offset += 65535 - PES_PACKET_HEADER_LENGTH + 6;
            remainingLen -= 65535 - PES_PACKET_HEADER_LENGTH + 6;
        }
        if (remainingLen > 0) {
            int pesLen = PES_PACKET_HEADER_LENGTH - 6 + remainingLen;
            pesPacketForVideo[4] = (byte) (pesLen >> 8);
            pesPacketForVideo[5] = (byte) pesLen;
            baos.write(pesPacketForVideo);
            baos.write(rawData, offset, remainingLen);
        }
    }

    private void updateTimestampInPESHeader(byte[] pesHeader, long timestamp) {
        pesHeader[9] = (byte) (0b00100001 | ((timestamp >> 29) & 0b1110));
        pesHeader[10] = (byte) (timestamp >> 22);
        pesHeader[11] = (byte) (0b1 | ((timestamp >> 14) & 0b11111110));
        pesHeader[12] = (byte) (timestamp >> 7);
        pesHeader[13] = (byte) (0b1 | (timestamp << 1) & 0b11111110);
    }

    private void updateTimestampInPackHeaderAndPESHeader(byte[] packHeader, byte[] pesHeader, long timestamp) {
        packHeader[4] = (byte) (0b01000100 | ((timestamp >> 27) & 0b111000) | ((timestamp >> 28) & 0b11));
        packHeader[5] = (byte) (timestamp >> 20);
        packHeader[6] = (byte) (0b100 | ((timestamp >> 12) & 0b11111000) | ((timestamp >> 13) & 0b11));
        packHeader[7] = (byte) (timestamp >> 5);
        packHeader[8] = (byte) (0b100 | ((timestamp << 3) & 0b11111000));
        updateTimestampInPESHeader(pesHeader, timestamp);
    }

    private void updateMap() {
        if (audioFmt != 0) {
            mapWithAudio[39] = videoFmt;
            mapWithAudio[43] = audioFmt;
            int crc = crc32ForProgramStream(mapWithAudio, AV_MAP_HEADER_LENGTH - 4);
            mapWithAudio[47] = (byte) (crc >> 24);
            mapWithAudio[48] = (byte) (crc >> 16);
            mapWithAudio[49] = (byte) (crc >> 8);
            mapWithAudio[50] = (byte) crc;
        } else {
            mapWithoutAudio[39] = videoFmt;
            int crc = crc32ForProgramStream(mapWithoutAudio, VIDEO_ONLY_MAP_HEADER_LENGTH - 4);
            mapWithoutAudio[43] = (byte) (crc >> 24);
            mapWithoutAudio[44] = (byte) (crc >> 16);
            mapWithoutAudio[45] = (byte) (crc >> 8);
            mapWithoutAudio[46] = (byte) crc;
        }
    }

    private boolean keyframeCheck(byte[] videoRawData) {
        if (videoRawData.length < 6) {
            return false;
        }
        if (videoFmt == (byte) 0x1B) {
            byte nalUType = (byte) (videoRawData[4] & 0x1F);
            return nalUType == 5 || nalUType == 7 || nalUType == 8;
        }
        if (videoFmt == (byte) 0x24) {
            byte nalUType = (byte) ((videoRawData[4] & 0x7E) >> 1);
            return nalUType == 32 || nalUType == 33 || nalUType == 34 || nalUType == 19 || nalUType == 20;
        }
        return false;
    }


    private int crc32ForProgramStream(byte[] data, int length) {
        java.util.zip.CRC32 crc32 = new java.util.zip.CRC32();
        crc32.update(data, 0, length);
        return (int) crc32.getValue();
    }

}

