package com.saida.analysis.algHall.saidaPlayer;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class Message {
    public static class Header {

        // 类型定义
        public static final byte SYNC = (byte) 0xE1;
        public static final byte TYPE_COMMAND = 1;
        public static final byte TYPE_DATA = 2;
        public static final byte TYPE_MEDIA = (byte) 0xFF;
        public static final byte TYPE_MEDIA_ADDON_DATA = (byte) 0xFB;
        public static final byte TYPE_MEDIA_CONTROL = (byte) 0xFE;
        public static final byte TYPE_FILE = (byte) 0xFD;
        public static final byte TYPE_ALARM = (byte) 0xFC;
        public static final byte TYPE_WEBRTC = (byte) 0xFA;               // 新增对应
        public static final byte TYPE_AI_ANALYSIS = (byte) 0xF9;          // 新增对应
        public static final byte TYPE_CONFU_EXEC = (byte) 0xcc;           // 新增对应

        // 子类型定义 - 命令类型（Signaling）
        public static final short COMMAND_TYPE_HANDSHAKE = (short) 0x200;
        public static final short COMMAND_TYPE_CHANNEL_ONLINE = (short) 0x201;
        public static final short COMMAND_TYPE_CHANNEL_OFFLINE = (short) 0x202;
        public static final short COMMAND_TYPE_UPLOAD_CHANNEL = (short) 0x203;
        public static final short COMMAND_TYPE_PLAY = (short) 0x204;
        public static final short COMMAND_TYPE_STOP_PLAY = (short) 0x205;
        public static final short COMMAND_TYPE_FILE_DOWNLOAD = (short) 0x206;
        public static final short COMMAND_TYPE_NEW_WEBRTC_CONNECTION = (short) 0x207; // 新增
        public static final short COMMAND_TYPE_VOICE_CALL = (short) 0x101;

        // 子类型定义 - 数据类型
        public static final short DATA_TYPE_OSD_DATA = 1;
        public static final short DATA_TYPE_AI_RESULT = 2;
        public static final short DATA_TYPE_JPEG_SNAP = 3; // 新增
        public static final short DATA_TYPE_PNG_SNAP = 4;  // 新增

        // 子类型定义 - 媒体类型
        public static final short MEDIA_TYPE_RAW_AUDIO = 1;
        public static final short MEDIA_TYPE_RAW_VIDEO = 2;
        public static final short MEDIA_TYPE_PROGRAM_STREAM = 3;
        public static final short MEDIA_TYPE_RAW_VIDEO_KEY_FRAME = 4;

        // 子类型定义 - 媒体控制类型
        public static final short MEDIA_CONTROL_TYPE_SPEED = 1;
        public static final short MEDIA_CONTROL_TYPE_JUMP = 2;
        public static final short MEDIA_CONTROL_TYPE_PAUSE = 3;
        public static final short MEDIA_CONTROL_TYPE_CONTINUE = 4;
        public static final short MEDIA_CONTROL_TYPE_KEY_FRAME = 5;

        // 子类型定义 - 媒体附加数据类型
        public static final short MEDIA_ADDON_DATA_TYPE_MEDIA_INFO_PROTOBUF_DATA = 1;
        public static final short MEDIA_ADDON_DATA_TYPE_QUANTUM_CRYPTOGRAPHY_KEY_ID = 2;
        public static final short MEDIA_ADDON_DATA_TYPE_AI_RESULT = 3; // 新增

        // 子类型定义 - WebRTC
        public static final short WEBRTC_TYPE_ICE_SERVERS = 1;     // 新增
        public static final short WEBRTC_TYPE_ICE_CANDIDATE = 2;   // 新增
        public static final short WEBRTC_TYPE_SDP = 3;             // 新增

        // 子类型定义 - AI Analysis
        public static final short AI_ANALYSIS_TYPE_CONFU_JOIN_ROOM = 1;
        public static final short AI_ANALYSIS_TYPE_CONFU_EXEC_LOGS_STDOUT = 2;
        public static final short AI_ANALYSIS_TYPE_CONFU_EXEC_LOGS_STDERR = 3;
        public static final short AI_ANALYSIS_TYPE_AI_RESULT_AND_NEXT_PLAN = 4;
        public static final short AI_ANALYSIS_TYPE_JPEG_FILE_PATH = 5;
        public static final short AI_ANALYSIS_TYPE_RESULT = (short) 0xff00; // 广播型消息，新加

        // 子类型定义 - Confu Exec
        public static final short CONFU_EXEC_TYPE_CONFIG = 1;                      // 配置消息
        public static final short CONFU_EXEC_TYPE_EXIT = 2;                        // 退出消息（友好退出，1秒后强制退出）
        public static final short CONFU_EXEC_TYPE_STANDARD_IO = 3;                // 进程间通信输入（STDIN发送，带CRC32）
        public static final short CONFU_EXEC_TYPE_STANDARD_IO_OUT = 4;            // 进程间通信输出（STDOUT读取，带CRC32）
        public static final short CONFU_EXEC_TYPE_IMAGE_HAS_BEEN_WROTE = 5;       // Confu已经把图片写好了
        public static final short CONFU_EXEC_TYPE_START_PUBLISH_STREAM = 6;       // 开始推流
        public static final short CONFU_EXEC_TYPE_STOP_PUBLISH_STREAM = 7;        // 停止推流
        public static final short CONFU_EXEC_TYPE_PUBLISH_STATUS_REPORT = 8;      // 推流状态上报
        public static final short CONFU_EXEC_TYPE_FUNC_CALL_TIME_COST_REPORT = 9;
        public static final short CONFU_EXEC_TYPE_AI_SET_ALGORITHM_EXTENSION_INFO = 10;  // 设置算法额外信息
        public static final short CONFU_EXEC_TYPE_AI_CLEAR_ALGORITHM_EXTENSION_INFO = 11; // 清除算法额外信息




        public final byte  type;
        public final short subType;
        public final int timestamp;
        public final int contentLength;

        private Header(byte type, short subType, int timestamp, int contentLength) {
            this.type = type;
            this.subType = subType;
            this.timestamp = timestamp;
            this.contentLength = contentLength;
        }

        public static Header newHeader(byte[] headerData) {
            byte type = headerData[1];
            short subType = (short) ((headerData[2] & 0xff) | ((headerData[3] & 0xff) << 8));
            int timestamp =
                    (headerData[4] & 0xff) | ((headerData[5] & 0xff) << 8) | ((headerData[6] & 0xff) << 16) | ((headerData[7] & 0xff) << 24);
            int contentLength =
                    (headerData[8] & 0xff) | ((headerData[9] & 0xff) << 8) | ((headerData[10] & 0xff) << 16) | ((headerData[11] & 0xff) << 24);
            return new Header(type, subType, timestamp, contentLength);
        }

        public static Header buildHeader(byte type, short subType, int timestamp, int contentLength){
            return new Header(type, subType, timestamp, contentLength);
        }

        public byte[] toBytes() {
            ByteBuffer buffer = ByteBuffer.allocate(12)
                                          .order(ByteOrder.LITTLE_ENDIAN);

            buffer.put((byte) 0xe1);
            buffer.put(type);
            buffer.put((byte) (subType & 0xff));
            buffer.put((byte) (subType >> 8));

            buffer.put((byte) (timestamp & 0xff));
            buffer.put((byte) ((timestamp >> 8) & 0xff));
            buffer.put((byte) ((timestamp >> 16) & 0xff));
            buffer.put((byte) ((timestamp >> 24) & 0xff));

            buffer.put((byte) (contentLength & 0xff));
            buffer.put((byte) ((contentLength >> 8) & 0xff));
            buffer.put((byte) ((contentLength >> 16) & 0xff));
            buffer.put((byte) ((contentLength >> 24) & 0xff));
            return buffer.array();
        }

        public static int readContentLengthFromHeaderData(byte[] data) {
            return (data[8] & 0xff) | ((data[9] & 0xff) << 8) | ((data[10] & 0xff) << 16) | ((data[11] & 0xff) << 24);
        }
    }
}
