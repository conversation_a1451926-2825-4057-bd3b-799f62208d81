package com.saida.analysis.confu.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 清除算法额外信息
 * 对应C结构体: CONFU_AI_CLEAR_ALGORITHME_EXTENSION_INFO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfuAIClearAlgorithmExtensionInfo {
    
    /**
     * 算法枚举
     */
    private int algorithmEnum;
    
    /**
     * 序列化为字节数组
     * 格式: [algorithmEnum(4字节)]
     * 使用小端序
     * @return 序列化后的字节数组
     * @throws IOException 序列化异常
     */
    public byte[] toBytes() throws IOException {
        ByteBuffer buffer = ByteBuffer.allocate(4)
                .order(ByteOrder.LITTLE_ENDIAN);
        
        buffer.putInt(algorithmEnum);
        
        return buffer.array();
    }
    
    /**
     * 从字节数组反序列化
     * @param data 字节数组
     * @return 反序列化后的对象
     * @throws IOException 反序列化异常
     */
    public static ConfuAIClearAlgorithmExtensionInfo fromBytes(byte[] data) throws IOException {
        if (data.length < 4) {
            throw new IOException("数据太短，无法反序列化 ConfuAIClearAlgorithmExtensionInfo，需要4字节");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN);
        
        ConfuAIClearAlgorithmExtensionInfo info = new ConfuAIClearAlgorithmExtensionInfo();
        info.algorithmEnum = buffer.getInt();
        
        return info;
    }
    
    /**
     * 获取序列化后的总长度
     * @return 总长度（字节）
     */
    public int getSerializedLength() {
        return 4; // 只有algorithmEnum，4字节
    }
    
    /**
     * 验证数据有效性
     * @return 是否有效
     */
    public boolean isValid() {
        return true; // 只要algorithmEnum是int类型就有效
    }
    
    @Override
    public String toString() {
        return "ConfuAIClearAlgorithmExtensionInfo{" +
                "algorithmEnum=" + algorithmEnum +
                '}';
    }
}
