package com.saida.analysis.confu.dto;

import com.saida.analysis.mq.vlinker.VLinkerMqTemplate;
import com.saida.analysis.service.FileService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Data
@Component
public class SpringRoom {

    @Value("${vLinker.algDrawUrl:''}")
    private String algDrawUrl;
    @Resource
    private VLinkerMqTemplate vLinkerMqTemplate;
    @Resource
    private FileService fileService;
}
