package com.saida.analysis.confu.dto;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Getter
public class TimestampCalculator {
    private long firstTimestamp;  // 第一次系统时间戳（System.currentTimeMillis()）
    private long firstTimestampByStream;  // 第一次流媒体时间戳
    private long lastTimestamp;   // 上一次时间戳
    private boolean isInitialized; // 是否已初始化

    // 常量：2^30（回环检测阈值）和 2^32（回环校正值）
    private static final long THRESHOLD = 1L << 30; // 1<<30
    private static final long WRAP_VALUE = 1L << 32; // 1<<32

    /**
     * 构造函数，不初始化时间戳
     */
    public TimestampCalculator() {
        this.isInitialized = false;
    }

    /**
     * 处理新的时间戳，返回校正后的真实时间（毫秒）
     * @param currentTimestamp 当前时间戳（32位，单位：毫秒）
     * @return 校正后的真实时间（毫秒）
     */
    public synchronized long processTimestamp(long currentTimestamp) {
        if (!isInitialized) {
            // 首次调用，设置第一次时间戳
            this.firstTimestamp = System.currentTimeMillis();
            this.firstTimestampByStream = currentTimestamp;
            this.lastTimestamp = currentTimestamp;
            this.isInitialized = true;
            return firstTimestamp;
        }

        // 检查是否发生回环
        if (currentTimestamp - lastTimestamp < -THRESHOLD) {
            // 检测到回环，调整第一次流媒体时间戳
            log.info("时间回环已发生，已调整第一次流媒体时间戳");
            firstTimestampByStream -= WRAP_VALUE;
        }

        // 更新上一次时间戳
        lastTimestamp = currentTimestamp;

        // 计算校正后的真实时间（毫秒）
        return firstTimestamp + (currentTimestamp - firstTimestampByStream) / 90;
    }

    // 测试代码
    public static void main(String[] args) throws InterruptedException {
        // 创建实例
        TimestampCalculator wrapper = new TimestampCalculator();

        // 设置时间格式为 HH:mm:ss
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        sdf.setTimeZone(TimeZone.getTimeZone("UTC")); // 使用 UTC 时间避免时区影响

        // 模拟流媒体时间戳序列（32位时间戳，递增并可能回环，单位：毫秒）
        long[] timestamps = {
                1000L,        // 初始
                2000L,        // 正常递增
                3000L,        // 正常递增
                4294966296L,  // 接近 2^32 (4294967296)
                500L,          // 发生回环
                2000L,        // 恢复正常递增
                3000L,        // 递增正常
                4294966296L,  // 接近 2^32 (4294967296)
                500L,          // 发生回环
                2000L,        // 恢复正常递增
                3000L,        // 递增正常
                4294966296L,  // 接近 2^32 (4294967296)
                500L,          // 发生回环
                2000L,
        };

        for (long ts : timestamps) {
            long correctedTime = wrapper.processTimestamp(ts);
            String formattedTime = sdf.format(new Date(correctedTime));
            System.out.printf("当前时间戳: %d, 校正后真实时间: %s, 第一次系统时间: %d, 第一次流媒体时间: %d, 上次时间戳: %d%n",
                    ts, formattedTime, wrapper.getFirstTimestamp(), wrapper.getFirstTimestampByStream(), wrapper.getLastTimestamp());
            // 模拟时间间隔
            Thread.sleep(100);
        }
    }
}