package com.saida.analysis.confu.dto;

import com.saida.analysis.dto.GrpcAlgorithmResultDto;
import com.saida.analysis.dto.TaskDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfuAnalysisDto {
    // 本地图片地址
    private String imgUrl;
    // 图片网络地址
    private String imageUrl;
    // 图片字节
    private byte[] imageBytes;
    // 上一个图片字节
    private byte[] lastImageBytes;
    private List<GrpcAlgorithmResultDto> algorithmsResult;
    // 抽帧时间
    private Long timestamp;
    // doAnalysisThread 时间
    private Long doAnalysisThreadStartTime;
    // algorithmsResultTime
    private Long algorithmsResultTime;
    // 图片宽高
    private int w = 0;
    private int h = 0;
}
