package com.saida.analysis.confu.dto;

import lombok.Data;

import java.util.List;

/**
 * 需要广播给用户的结构体
 */
@Data
public class DataToBroadcast {
    // 示例json
    // {"boxThickness":2,"businessAlarm":true,"bxDtoList":[{"boxPoints":[{"x":1272,"y":563},{"x":1365,"y":563},{"x":1365,"y":751},{"x":1272,"y":751}],"labelName":"人体","labelPoint":{"x":1272,"y":563},"prob":0.7620676159858704,"showTime":900},{"boxPoints":[{"x":1012,"y":487},{"x":1085,"y":487},{"x":1085,"y":655},{"x":1012,"y":655}],"labelName":"人体","labelPoint":{"x":1012,"y":487},"prob":0.7541905045509338,"showTime":900},{"boxPoints":[{"x":1203,"y":554},{"x":1293,"y":554},{"x":1293,"y":720},{"x":1203,"y":720}],"labelName":"人体","labelPoint":{"x":1203,"y":554},"prob":0.6451190710067749,"showTime":900}],"fontColor":"#00ff00","fontSize":12,"timestamp":185813184}
    /**
     * 是否业务告警
     */
    private boolean businessAlarm;
    /**
     * 头部的时间戳
     */
    private long timestamp;
    /**
     * 绘制的颜色
     */
    private String fontColor;
    /**
     * 边框厚度
     */
    private int boxThickness;
    /**
     * 字体大小
     */
    private int fontSize;
    /**
     * 框的信息
     */
    private List<BoxDto> bxDtoList;

    @Data
    public static class BoxDto {
        /**
         * 显示的帧数量
         */
        private int showFrameCount;
        /**
         * 标签的坐标
         */
        private Point labelPoint;
        /**
         * 标签的名字
         */
        private String labelName;
        /**
         * 概率
         */
        private double prob;
        /**
         * 框的点
         */
        private List<Point> boxPoints;

        @Data
        public static class Point {
            private int x;
            private int y;
        }
    }

}
