package com.saida.analysis.confu.dto;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

public class ConfuImageHasBeenWrote {
    public long imageFileIndex;
    public long imageFilePathLength;
    public String imageFilePath;

    public static ConfuImageHasBeenWrote fromBytes(byte[] data) {
        ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN);

        ConfuImageHasBeenWrote out = new ConfuImageHasBeenWrote();

        out.imageFileIndex = Integer.toUnsignedLong(buffer.getInt());      // uint32
        out.imageFilePathLength = Integer.toUnsignedLong(buffer.getInt()); // uint32

        int pathLen = (int) out.imageFilePathLength;
        byte[] pathBytes = new byte[pathLen];
        buffer.get(pathBytes);
        out.imageFilePath = new String(pathBytes, StandardCharsets.UTF_8);

        return out;
    }
}

