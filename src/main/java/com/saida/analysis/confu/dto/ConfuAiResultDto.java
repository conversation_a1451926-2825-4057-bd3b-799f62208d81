package com.saida.analysis.confu.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * AI算法识别响应
 * 比如是这个结构体
 */
@Data
public class ConfuAiResultDto {

    private Integer code;
    private String msg;
    private DataDto data;

    @Data
    public static class DataDto {
        private Integer algorithm_num;
        private String algorithm_name;
        private List<DataListDto> detections;
        // 累计进入总数
        private Integer in_num;
        // 累计离开总数
        private Integer out_num;
    }

    @Data
    public static class DataListDto {
        private List<Double> box_xywh;
        private Integer class_id_enum_val;
        private String class_name;
        //置信度0-1
        private Double confidence;
        // 车牌号
        private String plate_number;
        // 车牌单双行 0 位置 1单 2双
        private Integer plate_line;
        // 返回的区域/线条id
        private Double roi_line_id;
        // 轨迹点数量
        private Integer traj_len;
        // 轨迹点
        private List<TrajDto> traj;
        //-1：未知状态；0：正常状态；1：奔跑状态；2：徘徊状态；3：逗留状态
        private Integer abd_evt_type;
        //0：未知事件；1：进入事件；2：离开事件
        private Integer cpc_evt_type;

        public List<Double> getBox_xywh() {
            return box_xywh == null ? new ArrayList<>() : box_xywh;
        }

        public Integer getClass_id_enum_val() {
            return class_id_enum_val == null ? 0 : class_id_enum_val;
        }

        public String getClass_name() {
            return class_name == null ? "" : class_name;
        }

        public Double getConfidence() {
            return confidence == null ? 0.0 : confidence;
        }
    }

    @Data
    public static class TrajDto {
        private Integer x;
        private Integer y;
    }
}
