package com.saida.analysis.confu.dto;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

public class ConfStreamPublishingStatusReport {

    public int result; // 对应 Go 中的 ConfStreamPublishingStatus（uint32）
    //const (
    //	ConfStreamPublishingStatusSuccess ConfStreamPublishingStatus = iota
    //	ConfStreamPublishingStatusFailedForDialing
    //	ConfStreamPublishingStatusFailedForAlreadyPublished
    //)
    public static final int ConfStreamPublishingStatusSuccess = 0;
    public static final int ConfStreamPublishingStatusFailedForDialing = 1;
    public static final int ConfStreamPublishingStatusFailedForAlreadyPublished = 2;
    public int descriptionLength;
    public String description;

    @Override
    public String toString() {
        return "ConfStreamPublishingStatusReport{" +
                "result=" + result +
                ", description='" + description + '\'' +
                '}';
    }

    public static ConfStreamPublishingStatusReport fromBytes(byte[] data) throws IOException {
        if (data.length < 8) {
            throw new IOException("数据太短，无法反序列化 ConfStreamPublishingStatusReport");
        }
        ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN);
        ConfStreamPublishingStatusReport report = new ConfStreamPublishingStatusReport();
        report.result = buffer.getInt();              // 小端序读取
        report.descriptionLength = buffer.getInt();   // 小端序读取
        if (data.length < 8 + report.descriptionLength) {
            throw new IOException("description 长度字段不合法，数据不足");
        }
        byte[] descBytes = new byte[report.descriptionLength];
        buffer.get(descBytes);
        report.description = new String(descBytes, StandardCharsets.UTF_8);
        return report;
    }

}
