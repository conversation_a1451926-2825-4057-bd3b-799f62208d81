package com.saida.analysis.confu.dto;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

public class ConfuConfig {
    public int frameCountToCache;
    public int imageOutputDirPathLength;
    public int aiSoPathLength;
    public int aiConfigJSONFilePathLength;
    public int sourceAddressLength;
    public int sourceHandshakeDataLength;

    public String imageOutputDirPath;
    public String aiSoPath;
    public String aiConfigJSONFilePath;
    public String sourceAddress;
    public byte[] sourceHandshakeData;

    @Override
    public String toString() {
        return "ConfuConfig{" +
                "frameCountToCache=" + frameCountToCache +
                ", imageOutputDirPath='" + imageOutputDirPath + '\'' +
                ", aiSoPath='" + aiSoPath + '\'' +
                ", aiConfigJSONFilePath='" + aiConfigJSONFilePath + '\'' +
                ", sourceAddress='" + sourceAddress + '\'' +
                ", sourceHandshakeData=" + Arrays.toString(sourceHandshakeData) +
                '}';
    }

    public byte[] toBytes() throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        writeInt(out, frameCountToCache);
        writeInt(out, imageOutputDirPathLength);
        writeInt(out, aiSoPathLength);
        writeInt(out, aiConfigJSONFilePathLength);
        writeInt(out, sourceAddressLength);
        writeInt(out, sourceHandshakeDataLength);

        out.write(imageOutputDirPath.getBytes(StandardCharsets.UTF_8));
        out.write(aiSoPath.getBytes(StandardCharsets.UTF_8));
        out.write(aiConfigJSONFilePath.getBytes(StandardCharsets.UTF_8));
        out.write(sourceAddress.getBytes(StandardCharsets.UTF_8));
        out.write(sourceHandshakeData);

        return out.toByteArray();
    }

    private void writeInt(OutputStream os, int value) throws IOException {
        os.write(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(value).array());
    }

}
