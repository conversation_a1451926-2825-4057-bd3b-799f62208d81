package com.saida.analysis.confu.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfuStartPublishingStreamRequest {

    public int destinationAddressLength;
    public int destinationHandshakeDataLength;

    public String destinationAddress;
    public byte[] destinationHandshakeData;

    @Override
    public String toString() {
        return "ConfuStartPublishingStreamRequest{" +
                "destinationAddressLength=" + destinationAddressLength +
                ", destinationAddress='" + destinationAddress + '\'' +
                ", destinationHandshakeData=" + Arrays.toString(destinationHandshakeData) +
                '}';
    }
    public byte[] toBytes() throws IOException {
        byte[] addressBytes = destinationAddress.getBytes(StandardCharsets.UTF_8);
        byte[] handshakeBytes = destinationHandshakeData != null ? destinationHandshakeData : new byte[0];

        int totalLength = 4 + 4 + addressBytes.length + handshakeBytes.length;

        ByteBuffer buffer = ByteBuffer.allocate(totalLength)
                .order(ByteOrder.LITTLE_ENDIAN);

        buffer.putInt(addressBytes.length);
        buffer.putInt(handshakeBytes.length);
        buffer.put(addressBytes);
        buffer.put(handshakeBytes);

        return buffer.array();
    }

}
