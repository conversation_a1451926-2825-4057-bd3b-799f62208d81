package com.saida.analysis.confu.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConfuAlgMinEnum {
    /**
     * alg-saida-label-name:
     *     labels:
     *         -1: 非法垂钓
     *         0: 人体
     *         1: 人头
     *         2: 人脸
     *         3: 非机动车/自行车
     *         4: 非机动车/电瓶车
     *         5: 非机动车/三轮车
     *         6: 机动车/摩托车
     *         7: 机动车/车辆
     *         8: 机动车/乘用车
     *         9: 机动车/工程车
     *         10: 烟雾
     *         11: 火焰
     *         12: 香烟
     *         13: 帽子/厨师帽
     *         14: 帽子/其他
     *         15: 服饰/厨师服
     *         16: 服饰/其他
     *         17: 垃圾桶口/无盖子
     *         18: 垃圾桶口/有盖子
     *         19: 垃圾桶/未溢出
     *         20: 垃圾桶/溢出
     *         21: 口罩/不规范
     *         22: 口罩/规范
     *         23: 老鼠
     *         24: 电话
     *         25: 煤气罐
     *         26: 鸟类/未知
     *         27: 鸟类/八哥
     *         28: 鸟类/凤头鸊鷉
     *         29: 鸟类/苍鹭
     *         30: 鸟类/白鹭
     *         31: 鸟类/小䴙䴘
     *         32: 鸟类/白琵鹭
     *         33: 鸟类/乌鸫
     *         34: 鸟类/喜鹊
     *         35: 鸟类/鹊鸲
     *         36: 鸟类/小嘴乌鸦
     *         37: 鸟类/灰喜鹊
     *         38: 鸟类/渔鸥
     *         39: 鸟类/白骨顶
     *         40: 鸟类/小鹀
     *         41: 鸟类/普通燕鸥
     *         42: 鸟类/麻雀
     *         43: 鸟类/环颈鸻
     *         44: 鸟类/豆雁
     *         45: 鸟类/珠颈斑鸠
     *         46: 鸟类/黑水鸡
     *         47: 鸟类/斑嘴鸭
     *         48: 鸟类/棕背伯劳
     *         49: 鸟类/大山雀
     *         50: 鸟类/白喉针尾雨燕
     *         51: 鸟类/天鹅
     *         52: 鸟类/黑尾鸥
     *         53: 鸟类/反嘴鹬
     *         54: 鸟类/燕雀
     *         55: 鸟类/普通秋沙鸭
     *         56: 鸟类/绿头鸭
     *         57: 鸟类/罗纹鸭
     *         58: 船
     *         59: 鱼竿
     *         60: 渔网
     *         61: 垃圾
     *         62: 违建
     *         63: 裸土
     *         64: 画面变化
     *         65: 摔倒
     *         66: 灭火器
     *         67: 头盔
     *         68: 衣服/自我
     *         69: 衣服/安全服
     *         70: 人头/模糊
     *         71: 衣服/模糊
     *         72: 行人
     *         73: 机动车/面包车
     *         74: 机动车/卡车
     *         75: 非机动车/三轮车/遮阳篷
     *         76: 机动车/巴士
     *         77: 漂浮物
     */

    UNKNOWN(-100, "未知"),
    ILLEGAL_FISHING(-1, "非法垂钓"),
    HUMAN(0, "人体"),
    HEAD(1, "人头"),
    FACE(2, "人脸"),
    NON_MOTOR_BIKE(3, "非机动车/自行车"),
    NON_MOTOR_E_BIKE(4, "非机动车/电瓶车"),
    NON_MOTOR_TRICYCLE(5, "非机动车/三轮车"),
    MOTORCYCLE(6, "机动车/摩托车"),
    MOTOR_VEHICLE(7, "机动车/车辆"),
    MOTOR_PASSENGER_CAR(8, "机动车/乘用车"),
    MOTOR_ENGINEERING_VEHICLE(9, "机动车/工程车"),
    SMOKE(10, "烟雾"),
    FIRE(11, "火焰"),
    CIGARETTE(12, "香烟"),
    HAT_CHEF(13, "帽子/厨师帽"),
    HAT_OTHER(14, "帽子/其他"),
    CLOTHES_CHEF(15, "服饰/厨师服"),
    CLOTHES_OTHER(16, "服饰/其他"),
    BIN_OPEN(17, "垃圾桶口/无盖子"),
    BIN_CLOSED(18, "垃圾桶口/有盖子"),
    BIN_NORMAL(19, "垃圾桶/未溢出"),
    BIN_OVERFLOW(20, "垃圾桶/溢出"),
    MASK_INCORRECT(21, "口罩/不规范"),
    MASK_CORRECT(22, "口罩/规范"),
    RAT(23, "老鼠"),
    PHONE(24, "电话"),
    GAS_TANK(25, "煤气罐"),
    BIRD_UNKNOWN(26, "鸟类/未知"),
    BIRD_STARLING(27, "鸟类/八哥"),
    BIRD_GREAT_CRESTED_GREBE(28, "鸟类/凤头鸊鷉"),
    BIRD_HERON(29, "鸟类/苍鹭"),
    BIRD_EGRET(30, "鸟类/白鹭"),
    BIRD_LITTLE_BITTERN(31, "鸟类/小䴙䴘"),
    BIRD_GREAT_WHITE_PELICAN(32, "鸟类/白琵鹭"),
    BIRD_BLACK_TURDUS(33, "鸟类/乌鸫"),
    BIRD_MAGPIE(34, "鸟类/喜鹊"),
    BIRD_RUFOUS_THRUSH(35, "鸟类/鹊鸲"),
    BIRD_SMALL_CROW(36, "鸟类/小嘴乌鸦"),
    BIRD_GRAY_MAGPIE(37, "鸟类/灰喜鹊"),
    BIRD_FISH_GULL(38, "鸟类/渔鸥"),
    BIRD_BLACK_BACKED_GULL(39, "鸟类/白骨顶"),
    BIRD_LARK(40, "鸟类/小鹀"),
    BIRD_COMMON_TERN(41, "鸟类/普通燕鸥"),
    BIRD_SPARROW(42, "鸟类/麻雀"),
    BIRD_COLLARED_PLOVER(43, "鸟类/环颈鸻"),
    BIRD_BEAN_GOOSE(44, "鸟类/豆雁"),
    BIRD_SPOTTED_DOVE(45, "鸟类/珠颈斑鸠"),
    BIRD_WATER_HEN(46, "鸟类/黑水鸡"),
    BIRD_SPOT_BILLED_DUCK(47, "鸟类/斑嘴鸭"),
    BIRD_BROWN_SHRIKE(48, "鸟类/棕背伯劳"),
    BIRD_GREAT_TIT(49, "鸟类/大山雀"),
    COMMON_FACE_FEATURE(50, "普通_人脸特征检测"),
    SWAN(51, "鸟类/天鹅"),
    BLACK_TAILED_GULL(52, "鸟类/黑尾鸥"),
    TURNSTONE(53, "鸟类/反嘴鹬"),
    CHAFFINCH(54, "鸟类/燕雀"),
    COMMON_PLAIN_POUCHARD(55, "鸟类/普通秋沙鸭"),
    GREEN_HEAD_DUCK(56, "鸟类/绿头鸭"),
    RINGED_DUCK(57, "鸟类/罗纹鸭"),
    BOAT(58, "船"),
    FISHING_ROD(59, "鱼竿"),
    FISHING_NET(60, "渔网"),
    GARBAGE(61, "垃圾"),
    ILLEGAL_BUILDING(62, "违建"),
    BARE_SOIL(63, "裸土"),
    SCENE_CHANGE(64, "画面变化"),
    FALL_DOWN(65, "摔倒"),
    FIRE_EXTINGUISHER(66, "灭火器"),
    HELMET(67, "头盔"),
    CLOTH_SELF(68, "衣服/自我"),
    CLOTH_SAFETY(69, "衣服/安全服"),
    HEAD_BLUR(70, "人头/模糊"),
    CLOTH_BLUR(71, "衣服/模糊"),
    PEDESTRIAN(72, "行人"),
    MOTOR_VAN(73, "机动车/面包车"),
    MOTOR_TRUCK(74, "机动车/卡车"),
    NON_MOTOR_TRICYCLE_CANOPY(75, "非机动车/三轮车/遮阳篷"),
    MOTOR_BUS(76, "机动车/巴士"),
    FLOATING_OBJECT(77, "漂浮物"),
    FACE_EXT(78, "人脸特征"),
    SQUIRREL(79, "松鼠"),
    HEAD_SHOULDER(80, "头肩"),
    BIRD_HIMANTOPUS(81, "鸟类/黑翅长脚鹬"),
    BIRD_CIC_BOY(82, "鸟类/东方白鹳"),
    BIRD_LITTLE_GREBE(83, "鸟类/小鸊鷉"),
    BIRD_GRAY_FLOATING(84, "鸟类/灰翅浮鸥"),
    BIRD_RED_PEWIT(85, "鸟类/红嘴鸥"),
    BIRD_POND_HERON(86, "鸟类/池鹭"),
    BIRD_YBGEM(87, "鸟类/黑尾蜡嘴雀"),
    BIRD_DUNLIN(88, "鸟类/黑腹滨鹬"),
    BIRD_CPT(89, "鸟类/中华攀雀"),
    BIRD_NIGHT_HERON(90, "鸟类/夜鹭"),
    BIRD_BARN_SWALLOW(91, "鸟类/家燕"),
    BIRD_OTD(92, "鸟类/山斑鸠"),
    BIRD_LVB(93, "鸟类/白头鹎"),
    BIRD_SLS(94, "鸟类/丝光椋鸟"),
    BIRD_BLACK_DRONGO(95, "鸟类/黑卷尾"),
    BIRD_BNO(96, "鸟类/黑枕黄鹂"),
    BIRD_CRANE_SAND(97, "鸟类/鹤鹬"),
    PLATE_BLUE(98, "车牌/蓝色"),
    PLATE_YELLOW(99, "车牌/黄色"),
    PLATE_GREEN(100, "车牌/绿色"),
    PLATE_BLACK(101, "车牌/黑色"),
    PLATE_WHITE(102, "车牌/白色"),
    PLATE_YELLOW_GREEN(103, "车牌/黄绿色"),
    PLATE_NUM(104, "车牌/号码")
    ;

    private final int algNum;
    private final String algName;

    public static ConfuAlgMinEnum getByAlgNum(int algNum) {
        for (ConfuAlgMinEnum value : ConfuAlgMinEnum.values()) {
            if (value.algNum == algNum) {
                return value;
            }
        }
        return null;
    }

}
