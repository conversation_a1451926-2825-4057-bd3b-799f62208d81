package com.saida.analysis.confu.dto;

import com.saida.analysis.dto.DcTaskDispatchDto;
import com.saida.analysis.dto.GrpcAlgorithmResultDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AlarmResDto {
    private int imageHeight;
    private int imageWidth;
    private long timestamp;
    private Map<DcTaskDispatchDto, List<GrpcAlgorithmResultDto>> results;
}
