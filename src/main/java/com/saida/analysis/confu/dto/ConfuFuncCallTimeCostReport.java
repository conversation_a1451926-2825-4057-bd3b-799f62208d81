package com.saida.analysis.confu.dto;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class ConfuFuncCallTimeCostReport {
    public long algorithmCalledCount;                  // uint32 算法调用数量
    public long algorithmResultCount;                  // uint32 算法结果数量
    public long decodeTimeCost;                  // uint64 解码耗时
    public long algorithmTimeCost;               // uint64 算法调用耗时
    public long saveImageTimeCost;               // uint64 保存图片耗时
    public long waitingIPCResponseTimeCost;      // uint64 等待IPC响应耗时

    public static ConfuFuncCallTimeCostReport fromBytes(byte[] data) {
        ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN);
        ConfuFuncCallTimeCostReport out = new ConfuFuncCallTimeCostReport();
        // Read fields (C struct uses uint32 and uint64)
        out.algorithmCalledCount = Integer.toUnsignedLong(buffer.getInt());
        out.algorithmResultCount = Integer.toUnsignedLong(buffer.getInt());
        out.decodeTimeCost = buffer.getLong();
        out.algorithmTimeCost = buffer.getLong();
        out.saveImageTimeCost = buffer.getLong();
        out.waitingIPCResponseTimeCost = buffer.getLong();
        return out;
    }

    @Override
    public String toString() {
        return "{" +
                "algorithmCalledCount=" + algorithmCalledCount +
                ", algorithmResultCount=" + algorithmResultCount +
                ", decodeTimeCost=" + formatTime(decodeTimeCost) +
                ", algorithmTimeCost=" + formatTime(algorithmTimeCost) +
                ", saveImageTimeCost=" + formatTime(saveImageTimeCost) +
                ", waitingIPCResponseTimeCost=" + formatTime(waitingIPCResponseTimeCost) +
                '}';
    }

    private String formatTime(long microseconds) {
        return String.format("%.3f ms", microseconds / 1000.0);
    }
}
