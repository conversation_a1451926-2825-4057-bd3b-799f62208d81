package com.saida.analysis.confu.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

/**
 * 设置算法额外信息
 * 对应C结构体: CONFU_AI_SET_ALGORITHME_EXTENSION_INFO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfuAISetAlgorithmExtensionInfo {
    
    /**
     * 算法枚举
     */
    private int algorithmEnum;
    
    /**
     * 额外信息内容长度
     */
    private int contentLength;
    
    /**
     * 额外信息内容
     */
    private String content;
    
    /**
     * 构造函数，自动计算内容长度
     * @param algorithmEnum 算法枚举
     * @param content 额外信息内容
     */
    public ConfuAISetAlgorithmExtensionInfo(int algorithmEnum, String content) {
        this.algorithmEnum = algorithmEnum;
        this.content = content != null ? content : "";
        this.contentLength = this.content.getBytes(StandardCharsets.UTF_8).length;
    }
    
    /**
     * 序列化为字节数组
     * 格式: [algorithmEnum(4字节)] + [contentLength(4字节)] + [content(contentLength字节)]
     * 使用小端序
     * @return 序列化后的字节数组
     * @throws IOException 序列化异常
     */
    public byte[] toBytes() throws IOException {
        byte[] contentBytes = content != null ? content.getBytes(StandardCharsets.UTF_8) : new byte[0];
        this.contentLength = contentBytes.length;
        
        int totalLength = 4 + 4 + contentLength; // algorithmEnum + contentLength + content
        
        ByteBuffer buffer = ByteBuffer.allocate(totalLength)
                .order(ByteOrder.LITTLE_ENDIAN);
        
        // 写入头部
        buffer.putInt(algorithmEnum);
        buffer.putInt(contentLength);
        
        // 写入内容
        if (contentLength > 0) {
            buffer.put(contentBytes);
        }
        
        return buffer.array();
    }
    
    /**
     * 从字节数组反序列化
     * @param data 字节数组
     * @return 反序列化后的对象
     * @throws IOException 反序列化异常
     */
    public static ConfuAISetAlgorithmExtensionInfo fromBytes(byte[] data) throws IOException {
        if (data.length < 8) {
            throw new IOException("数据太短，无法反序列化 ConfuAISetAlgorithmExtensionInfo，至少需要8字节");
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN);
        
        ConfuAISetAlgorithmExtensionInfo info = new ConfuAISetAlgorithmExtensionInfo();
        
        // 读取头部
        info.algorithmEnum = buffer.getInt();
        info.contentLength = buffer.getInt();
        
        // 验证数据长度
        if (data.length < 8 + info.contentLength) {
            throw new IOException("contentLength 字段不合法，数据不足。期望: " + 
                    (8 + info.contentLength) + " 字节，实际: " + data.length + " 字节");
        }
        
        // 读取内容
        if (info.contentLength > 0) {
            byte[] contentBytes = new byte[info.contentLength];
            buffer.get(contentBytes);
            info.content = new String(contentBytes, StandardCharsets.UTF_8);
        } else {
            info.content = "";
        }
        
        return info;
    }
    
    /**
     * 获取序列化后的总长度
     * @return 总长度（字节）
     */
    public int getSerializedLength() {
        int contentBytes = content != null ? content.getBytes(StandardCharsets.UTF_8).length : 0;
        return 8 + contentBytes; // 头部8字节 + 内容字节数
    }
    
    /**
     * 验证数据有效性
     * @return 是否有效
     */
    public boolean isValid() {
        if (content == null) {
            return contentLength == 0;
        }
        return contentLength == content.getBytes(StandardCharsets.UTF_8).length;
    }
    
    @Override
    public String toString() {
        return "ConfuAISetAlgorithmExtensionInfo{" +
                "algorithmEnum=" + algorithmEnum +
                ", contentLength=" + contentLength +
                ", content='" + content + '\'' +
                '}';
    }
}
