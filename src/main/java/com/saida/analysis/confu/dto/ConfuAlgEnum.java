package com.saida.analysis.confu.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConfuAlgEnum {

    H_DE(100, "H_DE", "zjc"),
    SD_ECN_12010001(0, "SD_ECN_12010001", "消防通道车辆占用"),
    SD_ECN_13010001(1, "SD_ECN_13010001", "区域入侵"),
    SD_ECN_11040001(2, "SD_ECN_11040001", "吸烟行为检测"),
    SD_ECN_15010001(3, "SD_ECN_15010001", "烟火检测"),
    SD_ECN_12050001(4, "SD_ECN_12050001", "电瓶车检测"),
    SD_ECN_11020001(5, "SD_ECN_11020001", "人员离岗"),
    SD_ECN_11030001(6, "SD_ECN_11030001", "未戴厨师帽"),
    SD_ECN_11030002(7, "SD_ECN_11030002", "未穿厨师服"),
    SD_ECN_11040002(8, "SD_ECN_11040002", "打电话检测"),
    SD_ECN_11030003(9, "SD_ECN_11030003", "未戴口罩"),
    SD_ECN_17010001(10, "SD_ECN_17010001", "老鼠检测"),
    SD_ECN_15020001(11, "SD_ECN_15020001", "垃圾桶溢出检测"),
    SD_ECN_15010002(12, "SD_ECN_15010002", "明火离岗"),
    SD_ECN_15020002(13, "SD_ECN_15020002", "煤气罐检测"),
    N_DET_OCCU(14, "N_DET_OCCU", "普通_消防通道占用检测"),
    N_DET_PERDUTY(15, "N_DET_PERDUTY", "普通_人员在岗"),
    N_DET_SAFETY(16, "N_DET_SAFETY", "普通_安全生产检测"),
    N_DET_CIG(17, "N_DET_CIG", "普通_吸烟"),
    N_DET_ELE(18, "N_DET_ELE", "普通_电梯中电瓶车检测"),
    N_DET_FALL(19, "N_DET_FALL", "普通_人员倒地"),
    N_DET_FIRE(20, "N_DET_FIRE", "普通_烟火检测"),
    N_DET_FIREEX(21, "N_DET_FIREEX", "普通_灭火器检测"),
    N_DET_PERCOUNT(22, "N_DET_PERCOUNT", "普通_人数统计"),
    N_DET_PHONE(23, "N_DET_PHONE", "普通_打电话"),
    H_DET_BIRD_AH(24, "H_DET_BIRD_AH", "高点_鸟类检测_安徽"),
    H_DET_CAR(25, "H_DET_CAR", "高点_车辆检测"),
    H_DET_FIRE(26, "H_DET_FIRE", "高点_烟火检测"),
    H_DET_ILLCATCH(27, "H_DET_ILLCATCH", "高点_违法捕捞"),
    H_DET_ILLCONST(28, "H_DET_ILLCONST", "高点_违法搭建"),
    H_DET_ILLFISH(29, "H_DET_ILLFISH", "高点_非法垂钓"),
    H_DET_LAND(30, "H_DET_LAND", "高点_土壤裸露"),
    H_DET_PERSON(31, "H_DET_PERSON", "高点_人员检测"),
    H_DET_SHIP(32, "H_DET_SHIP", "高点_船只检测"),
    H_DET_TRASH(33, "H_DET_TRASH", "高点_垃圾裸露"),
    U_DET_CAR(34, "U_DET_CAR", "无人机_车辆检测"),
    U_DET_FLOAT(35, "U_DET_FLOAT", "无人机_水面漂浮物检测"),
    U_DET_TRASH(36, "U_DET_TRASH", "无人机_垃圾检测"),
    U_DET_CONVEH(37, "U_DET_CONVEH", "无人机_工程车检测"),
    U_DET_PERCAR(38, "U_DET_PERCAR", "无人机_人车检测"),
    N_DET_CHANGE(39, "N_DET_CHANGE", "高点_变化检测"),
    SD_CCN_11030001(40, "SD_CCN_11030001", "未戴厨师帽"),
    SD_CCN_11030002(41, "SD_CCN_11030002", "未穿厨师服"),
    SD_CCN_11040002(42, "SD_CCN_11040002", "打电话检测"),
    SD_CCN_11030003(43, "SD_CCN_11030003", "未戴口罩"),
    SD_CCN_17010001(44, "SD_CCN_17010001", "老鼠检测"),
    SD_CCN_15020001(45, "SD_CCN_15020001", "垃圾桶溢出检测"),
    SD_CCN_15010002(46, "SD_CCN_15010002", "明火离岗"),
    SD_CCN_15020002(47, "SD_CCN_15020002", "煤气罐检测"),
    N_DET_ENGINEVEICHLE(48, "N_DET_ENGINEVEICHLE", "普通_工程车检测"),
    N_DET_FACE(49, "N_DET_FACE", "普通_人脸检测"),
    N_EXT_FACE(50, "N_EXT_FACE", "普通_人脸特征检测"),
    SD_ECN_17010002(51, "SD_ECN_17010002", "松鼠检测"),
    N_DET_PASCOUNT(52, "N_DET_PASCOUNT", "普通_客流统计"),
    N_DET_PASTATUS(53, "N_DET_PASTATUS", "普通_状态检测"),
    U_DET_SHIP(54, "U_DET_SHIP", "无人机_船只识别"),
    U_DET_FIRE(55, "U_DET_FIRE", "可见光_烟火识别"),
    N_DET_LPRD(56, "N_DET_LPRD", "普通_车牌检测"),
    N_DET_LPRC(57, "N_DET_LPRC", "普通_车牌识别"),
    N_DET_CATTLE(58, "N_DET_CATTLE", "普通_动物(牛)识别"),
    N_DET_PVN(59, "N_DET_PVN", "普通_人车非检测"),
    N_DET_HEAD_SHOULDER(60, "N_DET_HEAD_SHOULDER", "普通_头肩检测"),
    U_DET_PERSON(61, "U_DET_PERSON", "无人机_人员检测"),
    U_SEG_WATERS(62, "U_SEG_WATERS", "无人机_水域分割"),
    U_DET_PERSON_WATERS(63, "U_DET_PERSON_WATERS", "无人机_人员临水检测"),
    U_DET_PERSON_SWIM(64, "U_DET_PERSON_SWIM", "无人机_人员游泳/溺水检测"),
    U_DET_PEDLAR(65, "U_DET_PEDLAR", "无人机_游商游贩检测"),
    U_DET_CONSTSITE(66, "U_DET_CONSTSITE", "无人机_施工建筑检测"),
    U_DET_STREETSTALL(67, "U_DET_STREETSTALL", "无人机_占道经营检测"),
    U_DET_BLG_SHED(68, "U_DET_BLG_SHED", "无人机_蓝绿顶棚违建检测"),



    ;


    private final int algNum;
    private final String algCode;
    private final String algName;

    public static ConfuAlgEnum getAlgByAlgCode(String algCode) {
        for (ConfuAlgEnum confuAlgEnum : ConfuAlgEnum.values()) {
            if (confuAlgEnum.getAlgCode().equals(algCode)) {
                return confuAlgEnum;
            }
        }
        return null;
    }

    public static ConfuAlgEnum getAlgByAlgNum(int algNum) {
        for (ConfuAlgEnum confuAlgEnum : ConfuAlgEnum.values()) {
            if (confuAlgEnum.getAlgNum() == algNum) {
                return confuAlgEnum;
            }
        }
        return null;
    }

}
