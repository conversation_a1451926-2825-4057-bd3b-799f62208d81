package com.saida.analysis.confu.dto;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * conf-> java  （0）我来了
 * java-> confu  （1）用1算法
 * confu-> java  （1) 1的算法结果   1要编码么  1要给观众发消息么
 * java-> confu  （2）用1算法    1 要编码  1要给观众发消息
 * confu-> java  （2) 1的算法结果   2要编码么  2要给观众发消息么  + 1的编码
 *
 *
 *
 * 新
 * conf-> java  （0）我来了
 * java-> confu  （1）用1算法
 * confu-> java  （1) 1的算法结果   1要编码么  1要给观众发消息么 1文件写哪
 * java-> confu  （2）用1算法    1 要编码  1要给观众发消息   1文件写在xxx
 * confu-> java  （1) 1的图片写好了
 * confu-> java  （2) 2的算法结果   2要编码么  2要给观众发消息么 2文件写哪
 *
 */
public class ConfuStandardOutput {
    public long algorithmResultCount;
    public long frameIndex;
    public long frameWidth;
    public long frameHeight;
    public long timestampInStream;
    public long timestampForRealWorld;

    public long[] algorithmResultLengths;
    public byte[][] algorithmResults;

    public static ConfuStandardOutput fromBytes(byte[] data) {
        ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.LITTLE_ENDIAN);
        ConfuStandardOutput out = new ConfuStandardOutput();
        out.algorithmResultCount = Integer.toUnsignedLong(buffer.getInt()); // uint32
        out.frameIndex = Integer.toUnsignedLong(buffer.getInt());           // uint32
        out.frameWidth = Integer.toUnsignedLong(buffer.getInt());           // uint32
        out.frameHeight = Integer.toUnsignedLong(buffer.getInt());          // uint32
        out.timestampInStream = buffer.getLong();                           // uint64
        out.timestampForRealWorld = buffer.getLong();                       // uint64
        int resultCount = (int) out.algorithmResultCount;
        out.algorithmResultLengths = new long[resultCount];
        for (int i = 0; i < resultCount; i++) {
            out.algorithmResultLengths[i] = Integer.toUnsignedLong(buffer.getInt()); // uint32[]
        }
        out.algorithmResults = new byte[resultCount][];
        for (int i = 0; i < resultCount; i++) {
            int len = (int) out.algorithmResultLengths[i];
            byte[] resultBytes = new byte[len];
            buffer.get(resultBytes);
            out.algorithmResults[i] = resultBytes;
        }
        return out;
    }
}