package com.saida.analysis.confu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * AI算法识别响应
 * 比如是这个结构体
 */
@Data
public class ConfuAiConfigDto {
    // {"track_or_not":1,"detection_params":{"regions":[{"vertex_num":4,"ast_vertex":[{"x":0.0,"y":0.0},{"x":1920.0,"y":0.0},{"x":1920.0,"y":1080.0},{"x":0.0,"y":1080.0}]},{"vertex_num":4,"ast_vertex":[{"x":400.0,"y":200.0},{"x":800.0,"y":200.0},{"x":800.0,"y":600.0},{"x":400.0,"y":600.0}]}],"lines":[{"is_direct":true,"st_start":{"x":0.0,"y":0.0},"st_end":{"x":1920.0,"y":1080.0},"line_height":0.5},{"is_direct":true,"st_start":{"x":0.0,"y":1080.0},"st_end":{"x":1920.0,"y":0.0},"line_height":0.5},{"is_direct":true,"st_start":{"x":0.0,"y":540.0},"st_end":{"x":1920.0,"y":540.0},"line_height":0.5}],"sensitivity":1,"run_thr":0.5,"stay_time":250,"wander_distance_ratio":10,"stay_distance_thr":3}}
    // 0：不开启跟踪；非0：开启跟踪
    @JSONField(name = "track_or_not")
    private int trackOrNot = 0;
    @JSONField(name = "detection_params")
    private DetectionParamsDto detectionParams = new DetectionParamsDto();

    @Data
    public static class DetectionParamsDto {
        //传入的区域信息
        @JSONField(name = "regions")
        private List<RegionDto> regions = new ArrayList<>();
        //传入的线条信息
        @JSONField(name = "lines")
        private List<LineDto> lines = new ArrayList<>();
        //灵敏度，0：低；1：中；2：高
        @JSONField(name = "sensitivity")
        private int sensitivity = 1;
        //奔跑阈值 建议值：目标身宽的0.5倍
        @JSONField(name = "run_thr")
        private double runThr = 0.5;
        //逗留徘徊时间阈值（帧数）
        @JSONField(name = "stay_time")
        private int stayTime = 25;
        //徘徊判断的距离比例（累计距离/绝对距离），建议值：10
        @JSONField(name = "wander_distance_ratio")
        private int wanderDistanceRatio = 10;
        //逗留判断的距离阈值（相对于目标身宽的比例），建议值：3
        @JSONField(name = "stay_distance_thr")
        private int stayDistanceThr = 3;

    }

    @Data
    public static class RegionDto {
        //顶点数量
        @JSONField(name = "vertex_num")
        private int vertexNum = 0;
        //顶点坐标
        @JSONField(name = "ast_vertex")
        private List<CoordinateDto> astVertex = new ArrayList<>();
    }

    @Data
    public static class LineDto {
        //是否定向
        @JSONField(name = "is_direct")
        private boolean isDirect = false;
        //起点坐标
        @JSONField(name = "st_start")
        private CoordinateDto stStart;
        //终点坐标
        @JSONField(name = "st_end")
        private CoordinateDto stEnd;
        //检测区域高度
        @JSONField(name = "line_height")
        private double lineHeight = 0.5;
    }

    @Data
    public static class CoordinateDto {
        @JSONField(name = "x")
        private double x = 0.0;
        @JSONField(name = "y")
        private double y = 0.0;
    }
}
