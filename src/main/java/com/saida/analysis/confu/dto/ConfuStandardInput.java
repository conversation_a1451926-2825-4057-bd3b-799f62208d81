package com.saida.analysis.confu.dto;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

public class ConfuStandardInput {
    public long markerBitMap;
    public long algorithmCount;
    public long dataToBroadcastLength;
    public long imageFileNameLength;
    public long imageFileIndex;

    public long[] algorithmEnums;
    public float[] confidenceThreshold;
    public float[] minObjectRatio;
    public byte[] dataToBroadcast;
    public String imageFileName;

    public byte[] toBytes() {
        int capacity = getCapacity();

        ByteBuffer buffer = ByteBuffer.allocate(capacity).order(ByteOrder.LITTLE_ENDIAN);

        buffer.putLong(markerBitMap);
        buffer.putInt((int) algorithmCount);
        buffer.putInt((int) dataToBroadcastLength);
        buffer.putInt((int) imageFileNameLength);
        buffer.putInt((int) imageFileIndex);

        for (long alg : algorithmEnums) {
            buffer.putInt((int) alg);
        }

        for (float th : confidenceThreshold) {
            buffer.putFloat(th);
        }

        for (float ratio : minObjectRatio) {
            buffer.putFloat(ratio);
        }

        buffer.put(dataToBroadcast);

        if (imageFileName != null && imageFileNameLength > 0) {
            buffer.put(imageFileName.getBytes(StandardCharsets.UTF_8));
        }

        return buffer.array();
    }


    private int getCapacity() {
        int algorithmLen = (int) algorithmCount;
        int fileNameLen = 0;

        if (imageFileName != null) {
            fileNameLen = imageFileName.getBytes(StandardCharsets.UTF_8).length;
        }

        imageFileNameLength = fileNameLen; // 确保字段同步更新

        return Long.BYTES                      // markerBitMap
                + Integer.BYTES               // algorithmCount
                + Integer.BYTES               // dataToBroadcastLength
                + Integer.BYTES               // imageFileNameLength
                + Integer.BYTES               // imageFileIndex
                + algorithmLen * Integer.BYTES // algorithmEnums[]
                + algorithmLen * Float.BYTES   // confidenceThreshold[]
                + algorithmLen * Float.BYTES   // minObjectRatio[]
                + dataToBroadcast.length       // dataToBroadcast
                + fileNameLen;                 // imageFileName bytes (UTF-8)
    }

}
