package com.saida.analysis.confu.exec;

public class Bit64Switcher {

    private long bitmap = 0L;

    // 打开某一位开关
    public void turnOn(int index) {
        checkIndex(index);
        bitmap |= (1L << index);
    }

    // 关闭某一位开关
    public void turnOff(int index) {
        checkIndex(index);
        bitmap &= ~(1L << index);
    }

    // 检查某一位是否为开（1）
    public boolean isOn(int index) {
        checkIndex(index);
        return ((bitmap >> index) & 1L) != 0;
    }

    // 获取当前位图值
    public long toLong() {
        return bitmap;
    }

    // 设置位图值
    public void fromLong(long value) {
        bitmap = value;
    }

    private void checkIndex(int index) {
        if (index < 0 || index >= 64)
            throw new IndexOutOfBoundsException("Index must be in [0, 63]");
    }

    // 打印状态
    public void printAll() {
        for (int i = 0; i < 64; i++) {
            System.out.printf("Bit[%d] = %s%n", i, isOn(i) ? "ON" : "OFF");
        }
    }


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 3; i++) {
            sb.append("["+i+" = "+(isOn(i) ? "ON" : "OFF")+"] - ");
        }
        return sb.toString();
    }


    // 示例
    public static void main(String[] args) {
        Bit64Switcher switcher = new Bit64Switcher();
        switcher.turnOn(0);
        switcher.turnOn(63);
        switcher.turnOff(0);
        switcher.printAll();
        System.out.println("Final bitmap as long: " + switcher.toLong());
    }
}

