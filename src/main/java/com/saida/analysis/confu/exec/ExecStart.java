package com.saida.analysis.confu.exec;

import com.saida.analysis.algHall.saidaPlayer.Message;
import com.saida.analysis.confu.dto.ConfuStartPublishingStreamRequest;

import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

public class ExecStart {

    public static void send(OutputStream outputStream, ConfuStartPublishingStreamRequest request) throws Exception {
        request.destinationAddressLength = request.destinationAddress.getBytes(StandardCharsets.UTF_8).length;
        request.destinationHandshakeDataLength = request.destinationHandshakeData.length;
        byte[] confuPayload = request.toBytes();
        // 构造消息头
        Message.Header headerObj = Message.Header.buildHeader(Message.Header.TYPE_CONFU_EXEC,
                Message.Header.CONFU_EXEC_TYPE_START_PUBLISH_STREAM,
                (int) System.currentTimeMillis() / 1000,
                confuPayload.length);
        // 写入 header + payload 到输出流
        outputStream.write(headerObj.toBytes());
        outputStream.write(confuPayload);
        outputStream.flush();
    }
}
