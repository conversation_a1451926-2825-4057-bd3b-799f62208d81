package com.saida.analysis.confu.exec;

import cn.hutool.core.io.FileUtil;
import com.saida.analysis.algHall.saidaPlayer.Message;
import com.saida.analysis.confu.ExeConfuRoom;
import com.saida.analysis.confu.dto.AlarmResDto;
import com.saida.analysis.confu.dto.ConfuImageHasBeenWrote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ExecConfuImageHasBeenWrote {
    public int send(ExeConfuRoom exeConfuRoom, ConfuImageHasBeenWrote confuImageHasBeenWrote, Message.Header sendHeaderObj) {
        AlarmResDto alarmResDto = exeConfuRoom.getAlarmMapJson().get((int) confuImageHasBeenWrote.imageFileIndex);
        if (alarmResDto == null){
            log.error("{} alarmResDto 为空 idx:{}", exeConfuRoom.getDeviceId(),confuImageHasBeenWrote.imageFileIndex);
            return 0;
        }
        exeConfuRoom.getAlarmMapJson().remove((int) confuImageHasBeenWrote.imageFileIndex);
        if (StringUtils.isBlank(confuImageHasBeenWrote.imageFilePath)){
            log.error("{} confuImageHasBeenWrote.imageFilePath 为空", exeConfuRoom.getDeviceId());
            return -1;
        }
        byte[] imageBytes;
        try {
            imageBytes = FileUtil.readBytes(confuImageHasBeenWrote.imageFilePath);
        } catch (Exception e) {
            log.error("读取文件失败 path:{}",confuImageHasBeenWrote.imageFilePath);
            return -1;
        }
        if (imageBytes == null) {
            log.error("{} imageBytes 为空", exeConfuRoom.getDeviceId());
        }
        byte[] finalImageBytes = imageBytes;
        alarmResDto.getResults().forEach((dcTaskDispatch, grpcAlgorithmResultDtos) -> {
            dcTaskDispatch.addAlarm(finalImageBytes, alarmResDto, grpcAlgorithmResultDtos);
        });
        return 1;
    }
}
