package com.saida.analysis.confu.exec;

import com.saida.analysis.algHall.saidaPlayer.Message;
import com.saida.analysis.confu.ExeConfuRoom;
import com.saida.analysis.confu.dto.ConfuAISetAlgorithmExtensionInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
public class ExecConfuAIConfig {

    public static int send(ExeConfuRoom exeConfuRoom, int algorithmEnum, String content) {
        try {
            ConfuAISetAlgorithmExtensionInfo confuAISetAlgorithmExtensionInfo = new ConfuAISetAlgorithmExtensionInfo();
            confuAISetAlgorithmExtensionInfo.setAlgorithmEnum(algorithmEnum);
            confuAISetAlgorithmExtensionInfo.setContent(content);
            byte[] payload = confuAISetAlgorithmExtensionInfo.toBytes();
            log.info("发送算法配置信息: algorithmEnum:{} json:{}", algorithmEnum, content);
            // 构造消息头
            Message.Header headerObj = Message.Header.buildHeader(Message.Header.TYPE_CONFU_EXEC,
                    Message.Header.CONFU_EXEC_TYPE_AI_SET_ALGORITHM_EXTENSION_INFO,
                    0,
                    payload.length);
            if (exeConfuRoom.getOutputStream() != null) {
                exeConfuRoom.getOutputStream().write(headerObj.toBytes());
                exeConfuRoom.getOutputStream().write(payload);
                exeConfuRoom.getOutputStream().flush();
            }
        } catch (IOException e) {
            log.error("发送算法配置信息失败: algorithmEnum:{} json:{}", algorithmEnum, content, e);
        }
        return 1;
    }


}
