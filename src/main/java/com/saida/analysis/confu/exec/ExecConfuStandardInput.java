package com.saida.analysis.confu.exec;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.saida.analysis.algHall.saidaPlayer.Message;
import com.saida.analysis.confu.ExeConfuRoom;
import com.saida.analysis.confu.dto.*;
import com.saida.analysis.dto.AlgorithmParamConfigDto;
import com.saida.analysis.dto.DcTaskDispatchDto;
import com.saida.analysis.dto.GrpcAlgorithmResultDto;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
public class ExecConfuStandardInput {

    public int send(ExeConfuRoom exeConfuRoom, ConfuStandardOutput confuStandardOutput, Message.Header sendHeaderObj, long timestamp) throws Exception {
        ConfuStandardInput confuStandardInput = new ConfuStandardInput();
        confuStandardInput.dataToBroadcast = new byte[0];
        confuStandardInput.dataToBroadcastLength = confuStandardInput.dataToBroadcast.length;
        Bit64Switcher markerBitMap = new Bit64Switcher();
        if (exeConfuRoom.getkeyFrameOnly()) {
            markerBitMap.turnOn(0);
        }
        DataToBroadcast dataToBroadcast = new DataToBroadcast();
        Map<DcTaskDispatchDto, List<GrpcAlgorithmResultDto>> algorithmResultsMap = new HashMap<>();
        Map<DcTaskDispatchDto, List<GrpcAlgorithmResultDto>> alarmToMq = new HashMap<>();
        for (int i = 0; i < confuStandardOutput.algorithmResults.length; i++) {
            if (confuStandardOutput.algorithmResults[i].length == 0) {
                log.error("AI结果解析失败：i:{} len为0", i);
                continue;
            }
            ConfuAiResultDto confuAiResultDto = null;
            try {
                confuAiResultDto = JSON.parseObject(confuStandardOutput.algorithmResults[i], ConfuAiResultDto.class);
            } catch (Exception e) {
                log.error("AI结果解析失败：i:{} str:{} len:{} ex:{}", i, new String(confuStandardOutput.algorithmResults[i], StandardCharsets.UTF_8), confuStandardOutput.algorithmResults.length, e.getMessage());
                continue;
            }
            if (confuAiResultDto == null || confuAiResultDto.getData() == null || confuAiResultDto.getCode() != 0) {
                log.info("AI结果解析失败： len:{} algorithmResults {}", confuStandardOutput.algorithmResults.length, new String(confuStandardOutput.algorithmResults[i], StandardCharsets.UTF_8));
                continue;
            }
            DcTaskDispatchDto dcTaskDispatch = exeConfuRoom.getDcTaskDispatchMap().get(confuAiResultDto.getData().getAlgorithm_num());
            if (dcTaskDispatch == null) {
                log.info("AI结果解析失败：{} 没这个算法code？,{}", confuAiResultDto.getData().getAlgorithm_num(), confuAiResultDto);
                continue;
            }
            log.info("AI结果解析成功：{}", JSON.toJSONString(confuAiResultDto, SerializerFeature.WriteMapNullValue));
            // 是否告警？
            boolean isAlarm = dcTaskDispatch.timeOfAlarm(timestamp, true);
            List<String> split = dcTaskDispatch.getAlgorithmMinCodeList();
            List<GrpcAlgorithmResultDto> algorithmsResult = new ArrayList<>();
            Integer inNum = confuAiResultDto.getData().getIn_num();
            Integer outNum = confuAiResultDto.getData().getOut_num();
            confuAiResultDto.getData().getDetections().forEach(dataDto -> {
                GrpcAlgorithmResultDto grpcAlgorithmResultDto = new GrpcAlgorithmResultDto();
                String class_id_enum_val_str = String.valueOf(dataDto.getClass_id_enum_val());
                if (!split.isEmpty() && split.stream().noneMatch(s -> s.equals(class_id_enum_val_str))) {
                    log.info("AI结果解析失败： 小类过滤 {}->{}", class_id_enum_val_str, split);
                    return;
                }
                grpcAlgorithmResultDto.setLabel(dataDto.getClass_id_enum_val());
                grpcAlgorithmResultDto.setProb(dataDto.getConfidence());
                grpcAlgorithmResultDto.setRoi_line_id(dataDto.getRoi_line_id());
                grpcAlgorithmResultDto.setTraj_len(dataDto.getTraj_len());
                grpcAlgorithmResultDto.setTraj(dataDto.getTraj());
                grpcAlgorithmResultDto.setAbd_evt_type(dataDto.getAbd_evt_type());
                grpcAlgorithmResultDto.setCpc_evt_type(dataDto.getCpc_evt_type());
                grpcAlgorithmResultDto.setIn_num(inNum);
                grpcAlgorithmResultDto.setOut_num(outNum);
                grpcAlgorithmResultDto.setPlate_number(dataDto.getPlate_number());
                grpcAlgorithmResultDto.setPlate_line(dataDto.getPlate_line());
                List<Double> box = dataDto.getBox_xywh(); // 假设是 [x, y, w, h]
                if (box != null && box.size() >= 4) {
                    int x = box.get(0).intValue();
                    int y = box.get(1).intValue();
                    int w = box.get(2).intValue();
                    int h = box.get(3).intValue();
                    grpcAlgorithmResultDto.setProb(dataDto.getConfidence());
                    grpcAlgorithmResultDto.setMinx(x);
                    grpcAlgorithmResultDto.setMaxx(x + w);
                    grpcAlgorithmResultDto.setMiny(y);
                    grpcAlgorithmResultDto.setMaxy(y + h);
                } else {
                    log.error("AI结果解析失败：box为null 或者点位不够");
                    return;
                }
                algorithmsResult.add(grpcAlgorithmResultDto);
            });
            ConfuAnalysisDto analysisDto = new ConfuAnalysisDto();
            analysisDto.setH((int) confuStandardOutput.frameHeight);
            analysisDto.setW((int) confuStandardOutput.frameWidth);
            analysisDto.setTimestamp(timestamp);
            analysisDto.setDoAnalysisThreadStartTime(timestamp);
            analysisDto.setAlgorithmsResultTime(System.currentTimeMillis());
            analysisDto.setAlgorithmsResult(algorithmsResult);
            List<GrpcAlgorithmResultDto> results = dcTaskDispatch.calculationResults(analysisDto, exeConfuRoom.getDevicePreSetJumpMessage());
            if (!results.isEmpty()) {
                algorithmResultsMap.put(dcTaskDispatch, results);
                if (isAlarm) {
                    dataToBroadcast.setBusinessAlarm(true);
                    dcTaskDispatch.setLastAlarmTime(timestamp);
                    log.info("{} 产生告警信息 timestamp:{} results:{}", exeConfuRoom.getDeviceId(), timestamp, JSON.toJSONString(results, SerializerFeature.WriteMapNullValue));
                    alarmToMq.put(dcTaskDispatch, results);
                }
            }
        }
        if (!alarmToMq.isEmpty()) {
            AlarmResDto build = AlarmResDto.builder()
                    .timestamp(timestamp)
                    .imageWidth((int) confuStandardOutput.frameWidth)
                    .imageHeight((int) confuStandardOutput.frameHeight)
                    .results(alarmToMq).build();
            confuStandardInput.imageFileIndex = exeConfuRoom.getAlarmFileName(build);
            confuStandardInput.imageFileName = confuStandardInput.imageFileIndex + ".jpeg";
            confuStandardInput.imageFileNameLength = confuStandardInput.imageFileName.length();
            log.info("产生告警了：我需要一个图:{} timestamp:{} results:{}", confuStandardInput.imageFileName, timestamp, JSON.toJSONString(alarmToMq, SerializerFeature.WriteMapNullValue));
        }

        // 下一帧的数据
        List<Long> algEnumList = new ArrayList<>();
        for (DcTaskDispatchDto task : exeConfuRoom.getDcTaskDispatchMap().values()) {
            AlgorithmParamConfigDto config = task.getAlgorithmParamConfigDto();
            if (config == null) continue;
            // 当未开启推流(cascadeDetection=false)且处于告警冷却期，停止解析帧，直到告警间隔结束
            if (!exeConfuRoom.isCascadeDetection() && !task.timeOfAlarm(timestamp, false)) {
                // 仍在告警间隔内，不抽取本任务的下一帧
                continue;
            }
            boolean shouldExtract = shouldExtractFrame(
                    confuStandardOutput.frameIndex,
                    Boolean.TRUE.equals(config.getKeyFrameOnly()),
                    config.getDefaultFrameRate(),
                    config.getDefaultSkipFrameRate(),
                    config.getDefaultFrameExtractionTime(),
                    (int) timestamp / 1000
            );
            if (!shouldExtract) continue;
            // 构建 algorithmEnum 列表
            ConfuAlgEnum algEnum = ConfuAlgEnum.getAlgByAlgCode(task.getAlgorithmCode());
            if (algEnum != null) {
                algEnumList.add((long) algEnum.getAlgNum());
            }
        }

        // 填充 confuStandardInput
        if (!algorithmResultsMap.isEmpty()) {
            // 构造告警绘图信息
            List<DataToBroadcast.BoxDto> boxList = new ArrayList<>();
            algorithmResultsMap.forEach((dcTaskDispatch, grpcAlgorithmResultDtos) -> {
                if (grpcAlgorithmResultDtos == null) return;
                for (GrpcAlgorithmResultDto dto : grpcAlgorithmResultDtos) {
                    DataToBroadcast.BoxDto boxDto = new DataToBroadcast.BoxDto();
                    ConfuAlgMinEnum byAlgNum = ConfuAlgMinEnum.getByAlgNum(dto.getLabel());
                    if (byAlgNum == null) {
                        continue;
                    }
                    boxDto.setLabelName(byAlgNum.getAlgName());
                    boxDto.setProb(dto.getProb());
                    // 设置标签显示的位置（左上角）
                    DataToBroadcast.BoxDto.Point labelPoint = new DataToBroadcast.BoxDto.Point();
                    labelPoint.setX(dto.getMinx());
                    labelPoint.setY(dto.getMiny());
                    boxDto.setLabelPoint(labelPoint);
                    // 设置框的4个点（顺时针）
                    List<DataToBroadcast.BoxDto.Point> points = new ArrayList<>();
                    points.add(new DataToBroadcast.BoxDto.Point() {{
                        setX(dto.getMinx());
                        setY(dto.getMiny());
                    }}); // 左上
                    points.add(new DataToBroadcast.BoxDto.Point() {{
                        setX(dto.getMaxx());
                        setY(dto.getMiny());
                    }}); // 右上
                    points.add(new DataToBroadcast.BoxDto.Point() {{
                        setX(dto.getMaxx());
                        setY(dto.getMaxy());
                    }}); // 右下
                    points.add(new DataToBroadcast.BoxDto.Point() {{
                        setX(dto.getMinx());
                        setY(dto.getMaxy());
                    }}); // 左下
                    // 根据抽帧策略动态设置显示时间
                    boxDto.setShowFrameCount(dcTaskDispatch.getShowFrameCount(confuStandardOutput.frameIndex - 1));
                    boxDto.setBoxPoints(points);
                    boxList.add(boxDto);
                }
            });
            if (!boxList.isEmpty()) {
                // 设置绘图对象
                dataToBroadcast.setTimestamp(confuStandardOutput.timestampInStream);
                dataToBroadcast.setBxDtoList(boxList);
                dataToBroadcast.setFontColor("#00ff00");
                dataToBroadcast.setFontSize(12);
                dataToBroadcast.setBoxThickness(2);
                confuStandardInput.dataToBroadcast = JSON.toJSONBytes(dataToBroadcast);
                confuStandardInput.dataToBroadcastLength = confuStandardInput.dataToBroadcast.length;
                log.info("广播信息：boxSize:{} json:{}",boxList.size(), JSON.toJSONString(dataToBroadcast, SerializerFeature.WriteMapNullValue));
            }
        }
        confuStandardInput.algorithmEnums = algEnumList.stream().mapToLong(Long::longValue).toArray();
        confuStandardInput.algorithmCount = algEnumList.size();
        confuStandardInput.markerBitMap = markerBitMap.toLong();
        confuStandardInput.minObjectRatio = new float[confuStandardInput.algorithmEnums.length];
        Arrays.fill(confuStandardInput.minObjectRatio, 0.01f);
        confuStandardInput.confidenceThreshold = new float[confuStandardInput.algorithmEnums.length];
        Arrays.fill(confuStandardInput.confidenceThreshold, 0.01f);
        byte[] payload = confuStandardInput.toBytes();
        // 构造消息头
        Message.Header headerObj = Message.Header.buildHeader(Message.Header.TYPE_CONFU_EXEC,
                Message.Header.CONFU_EXEC_TYPE_STANDARD_IO,
                sendHeaderObj.timestamp,
                payload.length);
        if (exeConfuRoom.getOutputStream() != null) {
            exeConfuRoom.getOutputStream().write(headerObj.toBytes());
            exeConfuRoom.getOutputStream().write(payload);
            exeConfuRoom.getOutputStream().flush();
        }
        return 1;
    }

    /**
     * 判断当前帧是否需要抽取处理
     * @param frameIndex 当前帧索引
     * @param isKeyFrameOnly 是否关键帧模式
     * @param defaultFrameRate 非关键帧模式-识别多少帧
     * @param defaultSkipFrameRate 非关键帧模式-跳过多少帧
     * @param defaultFrameExtractionTime 关键帧模式-每N秒检测一次
     * @param currentTime 当前帧时间戳（单位：秒）
     * @return 是否需要处理
     */
    public static boolean shouldExtractFrame(
            long frameIndex,
            boolean isKeyFrameOnly,
            int defaultFrameRate,
            int defaultSkipFrameRate,
            int defaultFrameExtractionTime,
            int currentTime
    ) {
        if (isKeyFrameOnly) {
            // 关键帧模式：按 N 秒抽取
            return currentTime % defaultFrameExtractionTime == 0;
        } else {
            // 非关键帧模式：跳 N 帧，处理 M 帧
            int cycleLength = defaultSkipFrameRate + defaultFrameRate;
            long cycleIndex = frameIndex % cycleLength;
            return cycleIndex >= defaultSkipFrameRate;
        }
    }


}
