package com.saida.analysis.confu.exec;

import com.saida.analysis.algHall.saidaPlayer.Message;
import com.saida.analysis.confu.dto.ConfuConfig;

import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
public class ExecConfig {

    public static void send(OutputStream outputStream,ConfuConfig config) throws Exception {
//        config.frameCountToCache = 32;
//        config.imageOutputDirPath = "/tmp/confu/images";
        config.imageOutputDirPathLength = config.imageOutputDirPath.getBytes(StandardCharsets.UTF_8).length;
//        config.aiSoPath = "/opt/ai/libai.so";
        config.aiSoPathLength = config.aiSoPath.getBytes(StandardCharsets.UTF_8).length;
//        config.aiConfigJSONFilePath = "/opt/ai/config.json";
        config.aiConfigJSONFilePathLength = config.aiConfigJSONFilePath.getBytes(StandardCharsets.UTF_8).length;
//        config.sourceAddress = "*********:8899";
        config.sourceAddressLength = config.sourceAddress.getBytes(StandardCharsets.UTF_8).length;
//        config.sourceHandshakeData = new byte[]{0x01, 0x02, 0x03, 0x04};
        config.sourceHandshakeDataLength = config.sourceHandshakeData.length;
        byte[] confuPayload = config.toBytes();

        // 构造消息头
        Message.Header headerObj = Message.Header.buildHeader(Message.Header.TYPE_CONFU_EXEC,
                Message.Header.CONFU_EXEC_TYPE_CONFIG,
                (int) System.currentTimeMillis() / 1000,
                confuPayload.length);

        // 写入 header + payload 到输出流
        outputStream.write(headerObj.toBytes());
        outputStream.write(confuPayload);
        outputStream.flush();
        log.info("发送配置：{}", config.sourceAddress);
    }
}
