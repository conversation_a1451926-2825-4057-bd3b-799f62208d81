package com.saida.analysis.confu;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@ConfigurationProperties(prefix = "confu")
@ConditionalOnProperty(prefix = "confu", name = "enable", havingValue = "true")
@Configuration
public class ConfuSpringConfig {
    private Boolean enable;

    private String confuExePath;

    private Integer frameCountToCache;

    private String imageOutputDirPath;

    private String aiSoPath;

    private String aiConfigJSONFilePath;

    public String getImageOutputDirPath() {
        return imageOutputDirPath.endsWith("/") ? imageOutputDirPath : imageOutputDirPath + "/";
    }
}
