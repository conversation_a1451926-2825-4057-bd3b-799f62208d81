package com.saida.analysis.confu;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.saida.analysis.algHall.saidaPlayer.Message;
import com.saida.analysis.confu.dto.*;
import com.saida.analysis.confu.exec.*;
import com.saida.analysis.dto.AlgorithmParamConfigDto;
import com.saida.analysis.dto.DcTaskDispatchDto;
import com.saida.analysis.dto.PresetPointSetDetailDto;
import com.saida.analysis.entity.DcTaskDispatch;
import com.saida.analysis.mq.message.DevicePreSetJumpMessage;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.newsclub.net.unix.AFUNIXServerSocket;
import org.newsclub.net.unix.AFUNIXSocketAddress;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.net.Socket;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通过exe拉起confu程序的房间
 */
@Slf4j
@Getter
public class ExeConfuRoom {
    private final Long deviceId;
    /**
     * sdk 沟通用的对象
     */
    private Process process;
    private PushbackInputStream pushbackInputStream;
    private BufferedReader lineReader;
    private OutputStream outputStream;
    private BufferedReader processInputErr;
    private BufferedReader processInputOut;
    /**
     * 异常回调
     */
    private final RoomClosedCallback callback;
    /**
     * 是否开启关键帧
     */
    private Boolean keyFrameOnly = false;
    /**
     * 两个交互用的对象
     */
    private final ExecConfuStandardInput execConfuStandardInput;
    private final ExecConfuImageHasBeenWrote execConfuImageHasBeenWrote;
    /**
     * 吐流信息
     */
    private ConfuStartPublishingStreamRequest confuStartPublishingStreamRequest;
    /**
     * 0 初始化 1 已下发  2 已经开始
     */
    private int confuStartPublishing = 0;

    /**
     * 是否要开启推流呢
     */
    private boolean cascadeDetection = false;
    /**
     * 图片保存路径
     */
    private String imgPath;

    /**
     * 宽高
     */
    private long frameWidth;
    private long frameHeight;

    private final SpringRoom springRoom;
    /**
     *  设备预设跳转信息
     */
    private DevicePreSetJumpMessage devicePreSetJumpMessage;

    public void setDevicePreSetJumpMessage(DevicePreSetJumpMessage devicePreSetJumpMessage) {
        log.info("收到预置位跳转,重置画面参数从新计算");
        this.devicePreSetJumpMessage = devicePreSetJumpMessage;
        this.frameHeight = 0L;
        this.frameWidth = 0L;
    }

    /**
     * 第一次收到交互的时间
     */
    private final TimestampCalculator timestampCalculator = new TimestampCalculator();

    /**
     * 是否开启关键帧
     */
    public boolean getkeyFrameOnly() {
        return keyFrameOnly != null && keyFrameOnly;
    }

    // 任务集合
    @Setter
    private ConcurrentHashMap<Integer, DcTaskDispatchDto> dcTaskDispatchMap = new ConcurrentHashMap<>();


    @Getter
    private HashMap<Integer, AlarmResDto> alarmMapJson = new HashMap<>();
    private int alarmCount = 0;
    private long ipcMsgCount = 0;

    public int getAlarmFileName(AlarmResDto alarmResDto) {
        alarmCount = alarmCount + 1;
        alarmMapJson.put(alarmCount, alarmResDto);
        return alarmCount;
    }

    private final ConfuFuncCallTimeCostReport mConfuFuncCallTimeCostReport = new ConfuFuncCallTimeCostReport();

    public void addDcTaskDispatch(DcTaskDispatch dcTaskDispatch) {
        log.info("deviceId:{} 添加任务:{}", deviceId, dcTaskDispatch.getId());
        ConfuAlgEnum algEnum = ConfuAlgEnum.getAlgByAlgCode(dcTaskDispatch.getAlgorithmCode());
        if (algEnum == null) {
            log.error("deviceId:{} 添加任务失败:{} 算法不存在 code:{}", deviceId, dcTaskDispatch.getId(), dcTaskDispatch.getAlgorithmCode());
            return;
        }
        log.info("deviceId:{} 读取到算法枚举:{}->{}->{}", deviceId, algEnum.getAlgNum(),algEnum.getAlgCode(), algEnum.getAlgNum());
        DcTaskDispatchDto dcTaskDispatchDto = new DcTaskDispatchDto();
        BeanUtils.copyProperties(dcTaskDispatch, dcTaskDispatchDto);
        dcTaskDispatchDto.setSpringRoom(springRoom);
        // 解析并设置其他参数
        AlgorithmParamConfigDto config = JSON.parseObject(dcTaskDispatch.getAlgorithmParamConfig(), AlgorithmParamConfigDto.class);
        dcTaskDispatchDto.setAlgorithmParamConfigDto(config);
        dcTaskDispatchDto.init();
        if (keyFrameOnly == null) {
            keyFrameOnly = config.getKeyFrameOnly();
        } else {
            keyFrameOnly = keyFrameOnly && config.getKeyFrameOnly();
        }
        if (!cascadeDetection && Objects.equals(dcTaskDispatchDto.getCascadeDetection(), 1)) {
            cascadeDetection = true;
        }
        log.info("deviceId:{} 是否要推流呢 old:{} -> new:{}", deviceId, dcTaskDispatchDto.getCascadeDetection(), cascadeDetection);
        log.info("deviceId:{} 是否开启关键帧呢 old:{} -> new:{}", deviceId, config.getKeyFrameOnly(), keyFrameOnly);
        List<PresetPointSetDetailDto> presetPointSetDetailDtoList = JSON.parseArray(dcTaskDispatch.getPresetPointSetDetail(), PresetPointSetDetailDto.class);
        for (PresetPointSetDetailDto dto : presetPointSetDetailDtoList) {
            for (PresetPointSetDetailDto.DrawAreaDTO drawAreaDTO : dto.getDrawAreaVOList()) {
                List<PresetPointSetDetailDto.CoordinateDto> coordinates = JSON.parseArray(drawAreaDTO.getCoordinate(), PresetPointSetDetailDto.CoordinateDto.class);
                drawAreaDTO.setCoordinateList(coordinates);
            }
        }
        dcTaskDispatchDto.setPresetPointSetDetailDtoList(presetPointSetDetailDtoList);
        dcTaskDispatchMap.put(algEnum.getAlgNum(), dcTaskDispatchDto);
    }


    public ExeConfuRoom(SpringRoom springRoom, DcTaskDispatch dcTaskDispatch, String confuExePath, ConfuConfig confuConfig, RoomClosedCallback roomClosedCallback) {
        this.deviceId = dcTaskDispatch.getDeviceId();
        this.springRoom = springRoom;
        addDcTaskDispatch(dcTaskDispatch);
        this.callback = roomClosedCallback;
        this.execConfuStandardInput = new ExecConfuStandardInput();
        this.execConfuImageHasBeenWrote = new ExecConfuImageHasBeenWrote();
        try {
            imgPath = confuConfig.imageOutputDirPath;
            boolean exist = FileUtil.exist(imgPath);
            log.info("deviceId:{} imgPath:{} 文件夹存在状态:{}", deviceId, imgPath, exist);
            if (exist) {
                boolean del = FileUtil.del(imgPath);
                log.info("deviceId:{} imgPath:{} 文件夹删除成功:{}", deviceId, imgPath, del);
            }
            File mkdir = FileUtil.mkdir(imgPath);
            boolean success = mkdir.exists() && mkdir.isDirectory();
            log.info("deviceId:{} imgPath:{} 创建文件夹成功:{}", deviceId, imgPath, success);

//            stdIoClient(confuExePath);
            unixSocketClient(confuExePath, imgPath + "/confu.sock");
            log.info("deviceId:{} confu启动", deviceId);
            // 创建一个线程持续读取 Rust 程序输出
            Thread outputReaderThread = new Thread(this::readOutput);
            outputReaderThread.setName("confu{" + deviceId + "}");
            outputReaderThread.setDaemon(true);
            outputReaderThread.start();
            Thread outputReaderErrThread = new Thread(this::readOutputErr);
            outputReaderErrThread.setName("confu-err{" + deviceId + "}");
            outputReaderErrThread.setDaemon(true);
            outputReaderErrThread.start();
            Thread outputReaderOutThread = new Thread(this::readOutputOut);
            outputReaderOutThread.setName("AI:{" + deviceId + "}");
            outputReaderOutThread.setDaemon(true);
            outputReaderOutThread.start();
            ExecConfig.send(outputStream, confuConfig);
        } catch (Exception e) {
            log.error("confu 启动程序时出错", e);
        }
    }

    private void stdIoClient(String confuExePath) throws IOException {
        ProcessBuilder processBuilder = new ProcessBuilder(confuExePath);
        this.process = processBuilder.start();
        process.onExit().thenRun(() -> {
            log.error("stdIoClient deviceId:{} confu 停止 {}", deviceId, process.exitValue());
            notifyRoomClosed();
        });
        // 初始化输入/输出流
        this.pushbackInputStream = new PushbackInputStream(process.getInputStream(), 64);
        this.lineReader = new BufferedReader(new InputStreamReader(pushbackInputStream));
        this.processInputErr = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        this.outputStream = process.getOutputStream();
    }

    private void unixSocketClient(String confuExePath, String sockPath) throws Exception {
        File socketFile = new File(sockPath); // 和你传给 --ipc-unix-socket 的路径一致
        if (socketFile.exists()) {
            boolean del = FileUtil.del(socketFile);
            log.info("deviceId:{} socketPath:{} 文件夹删除成功:{}", deviceId, sockPath, del);
        }
        // 2. 创建 socket 地址
        AFUNIXSocketAddress address = AFUNIXSocketAddress.of(socketFile);
        // 3. 绑定 socket（Java 主动 bind，创建 .sock 文件）
        AFUNIXServerSocket serverSocket = AFUNIXServerSocket.bindOn(address);
        log.info("✅ Java 已创建 .sock 文件并监听，等待 C 端连接: {}", socketFile);
        ProcessBuilder processBuilder = new ProcessBuilder(
                confuExePath,
                "--ipc-unix-socket",
                sockPath
        );
        this.process = processBuilder.start();
        process.onExit()
                .thenRun(() -> {
                    log.error("unixSocketClient deviceId:{} confu 停止 {}", deviceId, process.exitValue());
                    notifyRoomClosed();
                });
        // 4. 等待 C 客户端连接
        Socket socket = serverSocket.accept();
        log.info("🤝 C 客户端已连接");
        this.pushbackInputStream = new PushbackInputStream(socket.getInputStream(), 64);
        this.lineReader = new BufferedReader(new InputStreamReader(pushbackInputStream));
        this.processInputErr = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        this.processInputOut = new BufferedReader(new InputStreamReader(process.getInputStream()));
        this.outputStream = socket.getOutputStream();


    }

    public void startPublishingStream(String addr, byte[] handshakeData) {
        // 吐流
        confuStartPublishingStreamRequest = new ConfuStartPublishingStreamRequest();
        confuStartPublishingStreamRequest.setDestinationAddress(addr);
        confuStartPublishingStreamRequest.setDestinationHandshakeData(handshakeData);
        log.info("deviceId:{} 吐流 addr:{} handshakeData:{}", deviceId, addr, Hex.encodeHexString(handshakeData));
    }


    public void readOutput() {
        try {
            while (true) {
                byte[] header = new byte[12];
                if (!readFully(pushbackInputStream, header)) {
                    log.error("❌ 嗅探失败：流中没有足够的数据头");
                    break;
                }
                int msgLength = Message.Header.readContentLengthFromHeaderData(header);
                boolean isHeaderValid = header[0] == (byte) 0xe1 &&
                        header[1] == Message.Header.TYPE_CONFU_EXEC
//                        && msgLength < 1024 * 32
                        ;
                if (msgLength > 1024 * 32) {
                    log.error("⚠️ 数据长度过长:{}", msgLength);
                }
                if (!isHeaderValid) {
                    log.info("header:{},msgLength:{}", header, msgLength);
                    pushbackInputStream.unread(header); // 回退，再读一整行
                    String badLine = lineReader.readLine();
                    log.error("⚠️ 脏数据: {}", badLine);
                    continue;
                }
                Message.Header headerObj = Message.Header.newHeader(header);
                byte[] msgBody = new byte[msgLength];
                if (msgLength > 0) {
                    if (!readFully(pushbackInputStream, msgBody)) {
                        log.error("❌ 嗅探失败：流中没有足够的数据体");
                        break;
                    }
                }
//                byte[] crc = new byte[4];
//                if (!readFully(pushbackInputStream, crc)) {
//                    log.error("❌ 读取失败：木有拿到crc");
//                    break;
//                }
//                log.info("subType:{},msgLength:{}", headerObj.subType, msgLength);
                if (headerObj.type == Message.Header.TYPE_CONFU_EXEC) {
                    switch (headerObj.subType) {
                        case Message.Header.CONFU_EXEC_TYPE_CONFIG:
                            log.info("收到回复的配置：");
                            break;
                        case Message.Header.CONFU_EXEC_TYPE_PUBLISH_STATUS_REPORT:
                            ConfStreamPublishingStatusReport confStreamPublishingStatusReport = ConfStreamPublishingStatusReport.fromBytes(msgBody);
                            log.info("confu 状态上报：{}", confStreamPublishingStatusReport);
                            confuStartPublishing = confStreamPublishingStatusReport.result == ConfStreamPublishingStatusReport.ConfStreamPublishingStatusSuccess ? 2 : 0;
                            break;
                        case Message.Header.CONFU_EXEC_TYPE_STANDARD_IO_OUT:
                            ipcMsgCount = ipcMsgCount + 1;
                            if (ipcMsgCount % 10 == 0) {
                                if (ipcMsgCount % 100 == 0 || keyFrameOnly) {
                                    log.info("✅ keyFrameOnly[{}] 吐流 {} 个消息 callTime:{}",keyFrameOnly, ipcMsgCount, mConfuFuncCallTimeCostReport);
                                    mConfuFuncCallTimeCostReport.decodeTimeCost = 0L;
                                    mConfuFuncCallTimeCostReport.algorithmTimeCost = 0L;
                                    mConfuFuncCallTimeCostReport.saveImageTimeCost = 0L;
                                    mConfuFuncCallTimeCostReport.waitingIPCResponseTimeCost = 0L;
                                    mConfuFuncCallTimeCostReport.algorithmCalledCount = 0;
                                    mConfuFuncCallTimeCostReport.algorithmResultCount = 0;
                                }
                            }
                            ConfuStandardOutput confuStandardOutput = ConfuStandardOutput.fromBytes(msgBody);
                            execConfuStandardInput.send(this, confuStandardOutput, headerObj, timestampCalculator.processTimestamp(confuStandardOutput.timestampInStream));
                            if (confuStartPublishingStreamRequest != null && cascadeDetection) {
                                if (confuStartPublishing == 0) {
                                    confuStartPublishing = 1;
                                    ExecStart.send(outputStream, confuStartPublishingStreamRequest);
                                }
                            }
                            if (this.frameWidth != confuStandardOutput.frameWidth || this.frameHeight != confuStandardOutput.frameHeight) {
                                this.frameWidth = confuStandardOutput.frameWidth;
                                this.frameHeight = confuStandardOutput.frameHeight;
                                log.info("画面分辨率发生变化 frameWidth:{} frameHeight:{}", confuStandardOutput.frameWidth, confuStandardOutput.frameHeight);
                                // 宽高改变 对应的算法任务也要修改配置
                                dcTaskDispatchMap.values()
//                                        .stream()
//                                        .filter(dcTaskDispatchDto -> !dcTaskDispatchDto.isSendConfuAiConfig())
                                        .forEach(dcTaskDispatchDto -> {
                                            ConfuAlgEnum confuAlgEnum = dcTaskDispatchDto.getConfuAlgEnum();
                                            if (confuAlgEnum != null) {
                                                dcTaskDispatchDto.updateConfuAiConfigDto(this.frameWidth, this.frameHeight);
                                                ExecConfuAIConfig.send(this, confuAlgEnum.getAlgNum(), JSON.toJSONString(dcTaskDispatchDto.getConfuAiConfigDto()));
                                            }
                                        });
                            }

                            break;
                        case Message.Header.CONFU_EXEC_TYPE_IMAGE_HAS_BEEN_WROTE:
                            // 图片写好了
                            ConfuImageHasBeenWrote confuImageHasBeenWrote = ConfuImageHasBeenWrote.fromBytes(msgBody);
                            execConfuImageHasBeenWrote.send(this, confuImageHasBeenWrote, headerObj);
                            break;
                        case Message.Header.CONFU_EXEC_TYPE_FUNC_CALL_TIME_COST_REPORT:
                            // 调用方法时长上报
                            ConfuFuncCallTimeCostReport confuFuncCallTimeCostReport = ConfuFuncCallTimeCostReport.fromBytes(msgBody);
                            this.mConfuFuncCallTimeCostReport.algorithmCalledCount += confuFuncCallTimeCostReport.algorithmCalledCount;
                            this.mConfuFuncCallTimeCostReport.algorithmResultCount += confuFuncCallTimeCostReport.algorithmResultCount;
                            this.mConfuFuncCallTimeCostReport.decodeTimeCost += confuFuncCallTimeCostReport.decodeTimeCost;
                            this.mConfuFuncCallTimeCostReport.algorithmTimeCost += confuFuncCallTimeCostReport.algorithmTimeCost;
                            this.mConfuFuncCallTimeCostReport.saveImageTimeCost += confuFuncCallTimeCostReport.saveImageTimeCost;
                            this.mConfuFuncCallTimeCostReport.waitingIPCResponseTimeCost += confuFuncCallTimeCostReport.waitingIPCResponseTimeCost;
                            break;
                    }
                }
                // 其他的头类型 不管他
            }
        } catch (Exception e) {
            log.error("confu 读取 输出时出错：{}", e.getMessage(), e);
        } finally {
            if (process.isAlive()) {
                this.process.destroyForcibly();
            }
            notifyRoomClosed();
        }
    }

    public void readOutputErr() {
        String line;
        try {
            while ((line = processInputErr.readLine()) != null) {
                if (StringUtils.isNotEmpty(line)) {
                    log.info("confu 程序输出：{}", line);
                }
            }
        } catch (IOException e) {
            log.error("confu 读取 输出时出错：{}", e.getMessage());
        }
    }

    public void readOutputOut() {
        if (processInputOut == null) {
            return;
        }
        String line;
        try {
            while ((line = processInputOut.readLine()) != null) {
                if (StringUtils.isNotEmpty(line)) {
                    log.info("AI 程序输出：{}", line);
                }
            }
        } catch (IOException e) {
            log.error("AI 读取 输出时出错：{}", e.getMessage());
        }
    }

    private synchronized void notifyRoomClosed() {
        if (callback != null) {
            callback.onRoomClosed(deviceId);
        }
    }

    /**
     * 从输入流中读取指定数量的字节，直到读满为止。
     * 如果遇到 EOF（提前结束），返回 false；否则返回 true。
     */
    public static boolean readFully(InputStream in, byte[] buffer) throws IOException {
        return readFully(in, buffer, 0, buffer.length);
    }

    /**
     * 从输入流中读取指定数量的字节，直到读满为止。
     * 如果遇到 EOF（提前结束），返回 false；否则返回 true。
     */
    public static boolean readFully(InputStream in, byte[] buffer, int offset, int length) throws IOException {
        int totalRead = 0;
        while (totalRead < length) {
            int read = in.read(buffer, offset + totalRead, length - totalRead);
            if (read == -1) {
                return false; // 提前遇到 EOF，读失败
            }
            totalRead += read;
        }
        return true;
    }

    public void close() {
//        if (threadId != 0L) {
//            lock.unlockAsync(threadId);
//        }
        try {
            if (FileUtil.exist(imgPath)) {
                boolean del = FileUtil.del(imgPath);
                log.info("deviceId:{} imgPath:{} 删除成功:{}", deviceId, imgPath, del);
            }
        } catch (Exception e) {
            log.error("confu 删除图片时出错", e);
        }
        try {
            process.destroy();
        } catch (Exception e) {
            log.error("confu 停止程序时出错", e);
        }
        try {
            process.getErrorStream().close();
        } catch (Exception e) {
            log.error("confu 停止程序时出错", e);
        }
        try {
            process.getInputStream().close();
        } catch (Exception e) {
            log.error("confu 停止程序时出错", e);
        }
    }

}
