package com.saida.analysis.confu;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.InvalidProtocolBufferException;
import com.saida.analysis.confu.dto.ConfuConfig;
import com.saida.analysis.confu.dto.SpringRoom;
import com.saida.analysis.dto.AlgorithmParamConfigDto;
import com.saida.analysis.dto.DcTaskDispatchDto;
import com.saida.analysis.dto.PresetPointSetDetailDto;
import com.saida.analysis.entity.DcTaskDispatch;
import com.saida.analysis.mq.message.DevicePreSetJumpMessage;
import com.saida.services.system.pb.OpenStreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Scanner;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 */
@Slf4j
@Component
public class ExeConfuHell {

    private static final ConcurrentHashMap<Long, ExeConfuRoom> confuRooms = new ConcurrentHashMap<>();

    @Autowired(required = false)
    private ConfuSpringConfig confuSpringConfig;

    @Resource
    private SpringRoom springRoom;

    public boolean hasRoom(Long deviceId, Long taskId) {
        ExeConfuRoom exeConfuRoom = confuRooms.get(deviceId);
        if (exeConfuRoom != null) {
            return exeConfuRoom.getDcTaskDispatchMap().values().stream().anyMatch(task -> task.getId().equals(taskId));
        }
        return false;
    }

    public boolean hasRoom(Long deviceId) {
        return confuRooms.containsKey(deviceId);
    }

    public void startPublishingStream(Long deviceId, String addr, byte[] handshakeData) {
        if (confuRooms.containsKey(deviceId)) {
            ExeConfuRoom exeConfuRoom = confuRooms.get(deviceId);
            exeConfuRoom.startPublishingStream(addr, handshakeData);
        } else {
            log.error("deviceId:{} not found confuRoom", deviceId);
        }
    }

    public void addRoom(DcTaskDispatch dcTaskDispatch, String sourceAddress, byte[] sourceHandshakeData) {
        if (confuSpringConfig == null) {
            log.error("confuSpringConfig is null  confu配置未开启！");
            return;
        }

        Long deviceId = dcTaskDispatch.getDeviceId();
        log.info("device: {} sourceAddress:{}", deviceId, sourceAddress);
        log.info("device: {} sourceHandshakeData:{}", deviceId, Hex.encodeHexString(sourceHandshakeData));
        ConfuConfig confuConfig = new ConfuConfig();
        confuConfig.frameCountToCache = confuSpringConfig.getFrameCountToCache();
        confuConfig.imageOutputDirPath = confuSpringConfig.getImageOutputDirPath() + deviceId;
        confuConfig.aiSoPath = confuSpringConfig.getAiSoPath();
        confuConfig.aiConfigJSONFilePath = confuSpringConfig.getAiConfigJSONFilePath();
        confuConfig.sourceAddress = sourceAddress;
//        confuConfig.sourceAddress = sourceAddress.replace("127.0.0.1", "**************");
        confuConfig.sourceHandshakeData = sourceHandshakeData;
        ExeConfuRoom room = new ExeConfuRoom(springRoom, dcTaskDispatch, confuSpringConfig.getConfuExePath(), confuConfig, this::removeRoom);
        confuRooms.put(deviceId, room);
    }

    public void addDcTaskDispatch(DcTaskDispatch dcTaskDispatch) {
        ExeConfuRoom exeConfuRoom = confuRooms.get(dcTaskDispatch.getDeviceId());
        if (exeConfuRoom != null) {
            exeConfuRoom.addDcTaskDispatch(dcTaskDispatch);
        }
    }

    public void setDevicePreSetJumpMessage(Long deviceId, DevicePreSetJumpMessage devicePreSetJumpMessage) {
        ExeConfuRoom exeConfuRoom = confuRooms.get(deviceId);
        long l = System.currentTimeMillis();
        log.info("[mq:device_pre_set_jump] exeConfuRoom?{} deviceId:{}，lastAlarmTime:{}", (exeConfuRoom != null), deviceId, l + (devicePreSetJumpMessage.getRotationTime() * 1000));
        if (exeConfuRoom != null) {
            exeConfuRoom.setDevicePreSetJumpMessage(devicePreSetJumpMessage);
        }
    }


    public static void main(String[] args) {
//        String pbStr = "CAESwgHhAeMAAAAAALYAAAACCl8IBhACIhQzNDAyMDAwMDAwMjAwMDAwMDAwOSoUMzQwMTAwMDAwMDEzMjAwMDAwMDEyFDM0MDEwMDAwMDAxMzEwMDAwMDAxOgnmvZjmtYvor5VCDAi1xrDBBhCmqbjYAhIEKAFIIBoPMTI3LjAuMC4xOjEyMzA2IjszMueahFBMQVlfVFlQRV9SRUFMVElNRV9NQUlO56CB5rWBOjJPZmJabTNEclREbXFGV1dUMVVxNFE9PRroBUpCQ1IyRW80WjRYUkxvcFp6bjhLWUR6VUZXSWhrYzFqWWI5clAzZFNQdXdZd0ROZG1lS0FQSHFmMVVhWlhYT0xTTThCcmpqTmxxa0VaVlB0ZTZzSGpVRjQ0UzIyY1pMT3NSUlpoZGwtVG45b3BGTFRDZzBiR2pDaXJmbm1RZms5Zjl2NTZheWJLTWw0SzFFQVQ1OG92MjB4T3RyRTcwemJFOEl0RWFnanVOTDFwZDU5UGlkTGdpbThsOUJpUlRYaGJ4SGVYZWpCdWFibVhNNjVONmpmNUxpT3RLaVdZNG9UT1N2NTNmTW1Pb3lKYkhYVTQ5RHJzaTlQQXBnS1AtY0lqb20yVFRCVXVnUVhtWElnTFBGZlhMMXBOQkdpR0pUMUlZQTNQVE9NQzhBbHRIeHU4a3ZjRFk1UVZyVEk0aTNEOVZ6VVZtQk05eGhsUFJxMXRSZHlFbWNIRXd6QnN1V0hvSzdwZ25ycG82WVV1VVlPV2dqYW15VURjTmJTQ0VMZlBQWkhKSlNhTmJBZURvODAtSlhIOEZaNU1kWFBhQ016WWtEejNMVFh5V3FHdkZzRWlneXpicFlSdE5NTDRZaEhmQ2xoQ3I1Ny1qUkltSGZVUUZHckJGeGFLRktUakMyTjNpWm1tMnRxSTlqUlFfSmR3OXo5Y2psUVlaeEpvSGowYldDQmNuT1VTdm4tUXNqVlhNWGotRi1XLWQ4ZnNhYVE2cGhGcmduNnlXSFd5TzV0cHQxc3AyQWxSR1Jya0tMVHlWbjRHSTFVbnhjcGduZXVYc2RtUVBiQjcxejJrZlkzNjdIcUVzRVZ1eGNFcV85WFZRQmRvNWo0QW5lMzJQUVRMWDUweUZwQWhLei1pU0YwRGwteVRIczVLNkRlRWxWSWE3Ny1OdzhQWGdRUkhrNGpYMjFBSEVsYkNqeG9rZmFURHpmU0N4S1RiSGk4R3lGZnJld1ptM0tlRHJVdDBlVU10TVN5Ylc4PSIhY3R5Mi12bGlua2VyLXhsLWdiLnNkY2N4LmNuOjE1NDU4";
        String pbStr = "CAESxwHhAeMAAAAAALsAAAACCl8IBhACIhQzNDAyMDAwMDAwMjAwMDAwMDAwNioUMzQwMjAwMDAwMDEzMjAwMDAxNTAyFDM0MDIwMDAwMDAxMzIwMDAwMTUwOgnmvZjmtYvor5VCDAj4prbBBhD08ZbQAhIEKAFIDhoUMTkyLjE2OC44MC4yMTE6MTIzMDYiOzE055qEUExBWV9UWVBFX1JFQUxUSU1FX01BSU7noIHmtYE6Mk9mYlptM0RyVERtcUZXV1QxVXE0UT09GvQFSkJDUjJFbzlaWTNSTG9wWnpuOEtZRHpVRldJaGtjMWpZYjlyUDNkU1B1d1l3RE5kbWVLQVBIcWYxVWFaWFhPTFNNOEJyampGbnFBRlpWUHRlNnNIalVGNDRTMjJjWkxPc1JSWmhkbC1UbjlvcEZMVENnMGJFakNpcl83d1hmbzVmOWo3N3F5YktzbDJMMUVBVDU4b3YyMHhPdHJFNzB6YkU4SXRFYWdqdWRQd3BkeDlQaWRMZ2ltOGw5QmlSVFhoYnhIZVhlakJ1YWJtWE02NU42amY1TGlPdEtpV1k0b1RPU3Y1M2ZFNFBKV01ibmpWTlUtbS1pTTZpRnBMRHZJX2txM2lCZWZtekdrTjR4d0VScXhFQ3M1VWZIbTJic3ZZVTlzb2FGSzhlS2stemhsQ3R4TDhSQVhQY3JYUDRUUEYtSUpOemJjTnlwN1M5dW5oc3g1aFdndUw0YWNTMnZiNDFZZWQzVmx0bzZjTWNob1lHM3pzMG40TTl3SkJwM19VT19aYjlnNEo1Q2U4eFhEemZrc0R1SFFrYWtrdXY3YTUxMEg4MUh0TUZzSl9ValZyaUEydGJva09vOEVUOV9FLTNfLVlyUmJpOUJ4Nm0yZjdUMTZHQlZSREttV2FZWTBsQ0lmM3FXOWo4RmxPa1czNTNyeDN2T3ZobzN4Vnl2SUN0TkJYbGFRbkVvdldGSjN0R2FuQnRUS0NtNVVzMHR5TmpoVGhlcXVidFZBSHJJOEZ6VzZ2YzNVMlYzaDFpS19Ld1VQMUVJMVJoYm5FUnM1V25yUWE2REFpVTRSRTc3WXN5YjdCT2FVR25YSVZ0N3BBUDJvcHV5b1RzczRyYVRLaVZjZ1ZqMEFxNjRxRkhVUV9EUlBlVHd4a0lzbU9EQzhkUmVhUkptZGZmR0pMT1N4WlF3UVlHQV9wcmoxdnN2YVFJX2xxUTJ5OERoYXJlWm5aUWhocjN4VDRzUkFqVW5VOXFXOW9UaEw5eW11Q3VCMk56UT09Ig8xMjcuMC4wLjE6MTU0NTg=";
        OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer = null;
        try {
            streamReplyForPlayer = OpenStreamMessage.StreamReplyForPlayer.parseFrom(Base64.getDecoder().decode(pbStr));
        } catch (InvalidProtocolBufferException e) {
            log.error("解析失败 deviceId:", e);
            return;
        }

        AlgorithmParamConfigDto config = new AlgorithmParamConfigDto();
        config.setDefaultFrameExtractionTime(1);
        config.setDefaultFrameRate(1);
        config.setSensitivity(0.1);
        config.setKeyFrameOnly(false);
        DcTaskDispatchDto dcTaskDispatch = new DcTaskDispatchDto();
        dcTaskDispatch.setAlgorithmCode("N_DET_OCCU");
        dcTaskDispatch.setId(1L);
        dcTaskDispatch.setDeviceId(123L);
        dcTaskDispatch.setAlgorithmParamConfig(JSON.toJSONString(config));
        List<PresetPointSetDetailDto> presetPointSetDetailDtoList = new ArrayList<>();
        PresetPointSetDetailDto presetPointSetDetailDto = new PresetPointSetDetailDto();
        List<PresetPointSetDetailDto.DrawAreaDTO> drawAreaVOList = new ArrayList<>();
        PresetPointSetDetailDto.DrawAreaDTO drawAreaDTO = new PresetPointSetDetailDto.DrawAreaDTO();
        drawAreaDTO.setName("saida");
        drawAreaDTO.setType(0);
        drawAreaDTO.setCheckRule(1);
        List<PresetPointSetDetailDto.CoordinateDto> coordinateList = new ArrayList<>();
        PresetPointSetDetailDto.CoordinateDto coordinate1 = new PresetPointSetDetailDto.CoordinateDto();
        coordinate1.setX(0.1);
        coordinate1.setY(0.1);
        coordinateList.add(coordinate1);
        PresetPointSetDetailDto.CoordinateDto coordinate2 = new PresetPointSetDetailDto.CoordinateDto();
        coordinate2.setX(1.0);
        coordinate2.setY(0.1);
        coordinateList.add(coordinate2);
        PresetPointSetDetailDto.CoordinateDto coordinate3 = new PresetPointSetDetailDto.CoordinateDto();
        coordinate3.setX(1.0);
        coordinate3.setY(0.1);
        coordinateList.add(coordinate3);
        PresetPointSetDetailDto.CoordinateDto coordinate4 = new PresetPointSetDetailDto.CoordinateDto();
        coordinate4.setX(1.0);
        coordinate4.setY(1.0);
        coordinateList.add(coordinate4);
        drawAreaDTO.setCoordinateList(coordinateList);
        drawAreaDTO.setCoordinate(JSON.toJSONString(coordinateList));
        drawAreaVOList.add(drawAreaDTO);
        presetPointSetDetailDto.setPresetPointId("123");
        presetPointSetDetailDto.setDrawAreaVOList(drawAreaVOList);
        presetPointSetDetailDtoList.add(presetPointSetDetailDto);
        dcTaskDispatch.setPresetPointSetDetail(JSON.toJSONString(presetPointSetDetailDtoList));

        ConfuConfig confuConfig = new ConfuConfig();
        confuConfig.frameCountToCache = 32;
        confuConfig.imageOutputDirPath = "/Users/<USER>/Documents/ideaProject/analysis-system/confuSdk/img";
        confuConfig.aiSoPath = "/aaaa";
        confuConfig.aiConfigJSONFilePath = "/Users/<USER>/Documents/ideaProject/analysis-system/confuSdk/aa.json";
//        confuConfig.sourceAddress = streamReplyForPlayer.getAddr();
        confuConfig.sourceAddress = "**************:15458";
        confuConfig.sourceHandshakeData = streamReplyForPlayer.getHandshakeData().toByteArray();
        confuRooms.put(123L, new ExeConfuRoom(null, dcTaskDispatch, "/Users/<USER>/Documents/CLionProjects/universeAI/cmake-build-minsizerel/confu", confuConfig, new RoomClosedCallback() {
            @Override
            public void onRoomClosed(Long deviceId) {
                System.out.println("onRoomClosed");
                System.exit(1);
            }
        }));

        while (true) {
            Scanner scanner = new Scanner(System.in);
            String next = scanner.next();
            if ("q".equals(next)) {
                break;
            }
        }
    }

    public void removeRoom(Long deviceId) {
        ExeConfuRoom exeConfuRoom = confuRooms.get(deviceId);
        if (exeConfuRoom != null) {
            exeConfuRoom.close();
        }
        confuRooms.remove(deviceId);
    }
}
