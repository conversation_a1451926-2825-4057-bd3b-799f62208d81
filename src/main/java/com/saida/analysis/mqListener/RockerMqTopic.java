package com.saida.analysis.mqListener;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum RockerMqTopic {

    VIDEO_STREAM_GET("video_stream_get", "获取直播流地址"),

    ANALYSIS_ALARM("analysis_alarm", "告警通知"),

    TASK_RECORD("task_record", "任务执行消息"),

    GRPC_TASK_STATUS("grpc_task_status", "Grpc任务进程信息"),

    TASK_FOR_IMG_RESP("task_for_img_resp", "图片识别结果"),

    TEMP_SNAPSHOT_RESP("temp_snapshot_resp", "临时抽帧"),

    FRAME_RATE_STATISTICS("frame_rate_statistics", "抽帧频率统计");

    public String topic;
    public String desc;


}
