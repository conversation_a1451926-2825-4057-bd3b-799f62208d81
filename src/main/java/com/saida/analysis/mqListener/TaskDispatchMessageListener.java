package com.saida.analysis.mqListener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.analysis.confu.ExeConfuHell;
import com.saida.analysis.entity.DcTaskDispatch;
import com.saida.analysis.enums.SyncActionEnum;
import com.saida.analysis.grpcSnapServerTask.GrpcSnapServerChannelConfig;
import com.saida.analysis.mapper.DcTaskDispatchMapper;
import com.saida.analysis.mq.message.VideoStreamMessage;
import com.saida.analysis.mq.vlinker.*;
import com.saida.analysis.service.AnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * 任务信息同步
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class TaskDispatchMessageListener implements VLinkerMqMessageListener {

    @Value("${rocketmq.name-server:''}")
    private String nameServer;
    @Value("${vLinker.nodeId:''}")
    private String nodeId;

    @Resource
    private VLinkerMqTemplate vLinkerMqTemplate;

    @Resource
    private DcTaskDispatchMapper dcTaskDispatchMapper;


    @Resource
    private GrpcSnapServerChannelConfig grpcSnapServerChannelConfig;


    @Resource
    private AnalysisService analysisService;

    @Resource
    private ExeConfuHell exeConfuHell;

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("task_dispatch")
                .tag(nodeId)
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        doOnMessage(messageBody);
    }

    public void doOnMessage(String message) {
        log.info("[mq:task_dispatch]接收到消息：{}", message);
        JSONObject jsonObject = JSON.parseObject(message, JSONObject.class);
        String action = jsonObject.getString("action");
        JSONObject data = jsonObject.getJSONObject("taskDispatchEntity");
        DcTaskDispatch dcTaskDispatch = data.toJavaObject(DcTaskDispatch.class);
        dcTaskDispatch.setTimePlan(jsonObject.getString("timePlan"));
//        dcTaskDispatch.setDeviceCode(jsonObject.getString("deviceCode"));
//        dcTaskDispatch.setChannelId(jsonObject.getString("channelId"));
        switch (action) {
            case SyncActionEnum.SAVE_UPDATE:
                if (jsonObject.containsKey("algorithmMappingEntity")) {
                    JSONObject algorithmMappingEntity = jsonObject.getJSONObject("algorithmMappingEntity");
                    dcTaskDispatch.setAlgorithmCode(algorithmMappingEntity.getString("code"));
                    dcTaskDispatch.setAlgorithmMinCode(algorithmMappingEntity.getString("minCode"));
                }
                DcTaskDispatch old = dcTaskDispatchMapper.selectOne(new LambdaQueryWrapper<DcTaskDispatch>()
                        .eq(DcTaskDispatch::getId, dcTaskDispatch.getId()));
                if (null == old) {
                    dcTaskDispatchMapper.insert(dcTaskDispatch);
                } else {
                    dcTaskDispatchMapper.updateById(dcTaskDispatch);
                }

                if (dcTaskDispatch.getStatus() == 1) {
                    //开启任务 由于没有rtsp地址  所以先发消息让算法回调回来
                    VideoStreamMessage videoStreamMessage = new VideoStreamMessage();
                    videoStreamMessage.setCameraId(dcTaskDispatch.getDeviceId());
                    videoStreamMessage.setReqId(UUID.randomUUID().toString());
                    videoStreamMessage.setProtocolCode("8,4");
                    videoStreamMessage.setDcTaskDispatchId(dcTaskDispatch.getId());
                    vLinkerMqTemplate.send(RockerMqTopic.VIDEO_STREAM_GET.getTopic(), videoStreamMessage);
                    // 如果房间有这个任务 那么更新这个任务
                    if (exeConfuHell.hasRoom(dcTaskDispatch.getDeviceId(), dcTaskDispatch.getId())) {
                        exeConfuHell.addDcTaskDispatch(dcTaskDispatch);
                    }
                } else {
                    // 任务关闭
                    grpcSnapServerChannelConfig.closeChannelByDeviceId(dcTaskDispatch.getDeviceId());  // 关闭设备的 gRPC 通道
                }
                break;
            case SyncActionEnum.DELETE:
                dcTaskDispatchMapper.deleteById(dcTaskDispatch.getId());
                // 关闭通道
                grpcSnapServerChannelConfig.closeChannelByDeviceId(dcTaskDispatch.getDeviceId());
                break;
            default:
                log.info("[mq:task_dispatch]未知的消息类型:{}", action);
                break;
        }
    }


}

