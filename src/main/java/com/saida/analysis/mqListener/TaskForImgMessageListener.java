package com.saida.analysis.mqListener;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.ByteString;
import com.saida.analysis.dto.GrpcAlgorithmResultDto;
import com.saida.analysis.grpcTask.GrpcChannelConfig;
import com.saida.analysis.mq.message.TaskForImgMessage;
import com.saida.analysis.mq.message.TaskForImgRespMessage;
import com.saida.analysis.mq.vlinker.*;
import com.saida.analysis.pb.TaskExchangeGrpc;
import com.saida.analysis.pb.TaskExchangeOuterClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 单图片任务
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class TaskForImgMessageListener implements VLinkerMqMessageListener {

    @Resource
    private GrpcChannelConfig grpcChannelConfig;
    @Resource
    private VLinkerMqTemplate vLinkerMqTemplate;

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("task_for_img")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        onMessage(messageBody);
    }

    public void onMessage(String message) {
        log.info("[mq:task_for_img]接收到消息：{}", message);
        TaskForImgMessage taskForImgMessage = JSON.parseObject(message, TaskForImgMessage.class);
        TaskExchangeOuterClass.DetectionAlgorithm value = null;
        try {
            value = TaskExchangeOuterClass.DetectionAlgorithm.valueOf(taskForImgMessage.getAlgorithmName());
        } catch (Exception e) {
            log.error("算法名称 {} 不存在 不开始任务", taskForImgMessage.getAlgorithmName());
            return;
        }

        BufferedImage image = downloadImage(taskForImgMessage.getUrl());
        if (image == null) {
            log.error("图片下载失败：{}", taskForImgMessage.getUrl());
            return;
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ByteString byteString = null;
        byte[] imageBytes;
        try {
            //用jpg 会有损失
            ImageIO.write(image, "png", outputStream);
            imageBytes = outputStream.toByteArray();
            byteString = ByteString.copyFrom(imageBytes);
        } catch (IOException e) {
            log.error("图片读取失败 url:{}", taskForImgMessage.getUrl());
            return;
        }
        TaskExchangeOuterClass.ImageFormat imgFormat = null;
        if (taskForImgMessage.getUrl().endsWith("png")) {
            imgFormat = TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_PNG;
        } else if (taskForImgMessage.getUrl().endsWith("jpg")) {
            imgFormat = TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_JPEG;
        } else {
            log.error("图片格式不支持：{}", taskForImgMessage.getUrl());
            return;
        }

        TaskExchangeOuterClass.ImageTask request = TaskExchangeOuterClass.ImageTask.newBuilder()
                .addAlgorithms(value)
                .setImg(byteString)
                .setImgFormat(imgFormat)
                .build();

        // 获取或创建异步 gRPC 通道
        TaskExchangeGrpc.TaskExchangeBlockingStub taskExchangeStub = grpcChannelConfig.getTaskExchangeStub();
        TaskExchangeOuterClass.OnAIResultGotReply onAIResultGotReply = taskExchangeStub.requestForImage(request);
        handleReply(onAIResultGotReply, taskForImgMessage, imageBytes);

        //临时测试返回用的
//        rocketMQEnhanceTemplate.send(RockerMqTopic.TASK_FOR_IMG_RESP.getTopic()
//                , TaskForImgRespMessage.builder()
//                        .type(1)
//                        .time(System.currentTimeMillis())
//                        .uuid(taskForImgMessage.getUuid())
//                        .algorithmName(taskForImgMessage.getAlgorithmName())
//                        .base64(base64)
//                        .url(taskForImgMessage.getUrl())
//                        .build());
    }


    private void handleReply(TaskExchangeOuterClass.OnAIResultGotReply reply
            , TaskForImgMessage taskForImgMessage, byte[] byteString) {
        List<GrpcAlgorithmResultDto> algorithmsResult = new ArrayList<>();
        //图像格式
        int fmtValue = reply.getFmtValue();
        //go返回结果
        List<TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> resultList = reply.getResultList();
        log.info("单次 go返回结果: 图像格式：{},图像数据: 太长了 只打印长度： {},时间戳:{}, resultList的size：{}"
                , fmtValue, byteString.length,
                reply.getTimestamp(), resultList.size());
        //算法结果(可能存在多个结果)
        for (TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper result : resultList) {
            List<TaskExchangeOuterClass.OnAIResultGotReply.Result> rectList = result.getRsList();
            for (TaskExchangeOuterClass.OnAIResultGotReply.Result x : rectList) {
                int label = x.getLabel();
                double prob = x.getProb();
                GrpcAlgorithmResultDto algorithmResult =  GrpcAlgorithmResultDto.builder()
                        .label(label)
                        .prob(prob)
                        .minx(x.getRect().getMinX())
                        .maxx(x.getRect().getMaxX())
                        .miny(x.getRect().getMinY())
                        .maxy(x.getRect().getMaxY())
                        .build();
//                GrpcAlgorithmResultDto algorithmResult = new GrpcAlgorithmResultDto(label, null, prob
//                        , x.getRect().getMinX(), x.getRect().getMaxX(), x.getRect().getMinY(), x.getRect().getMaxY());
                // 将生成的 AlgorithmResult 添加到结果列表中
                algorithmsResult.add(algorithmResult);
            }
        }


        if (!algorithmsResult.isEmpty()) {
            // 绘图allIndex
//            byte[] bytes = PointsUtils.drawRectangle(byteString, algorithmsResult, taskForImgMessage.getAlgorithmName(), true);
//            if (null == bytes) {
//                log.error("分析服务-绘图失败, taskId:{}", taskForImgMessage.getUuid());
//                return;
//            }
            vLinkerMqTemplate.send(RockerMqTopic.TASK_FOR_IMG_RESP.getTopic()
                    , TaskForImgRespMessage.builder()
                            .type(1)
                            .time(System.currentTimeMillis())
                            .uuid(taskForImgMessage.getUuid())
                            .algorithmName(taskForImgMessage.getAlgorithmName())
//                            .base64(Base64.getEncoder().encodeToString(bytes))
                            .url(taskForImgMessage.getUrl())
                            .build());
        } else {
            vLinkerMqTemplate.send(RockerMqTopic.TASK_FOR_IMG_RESP.getTopic()
                    , TaskForImgRespMessage.builder()
                            .type(1)
                            .time(System.currentTimeMillis())
                            .uuid(taskForImgMessage.getUuid())
                            .algorithmName(taskForImgMessage.getAlgorithmName())
                            .base64(Base64.getEncoder().encodeToString(byteString))
                            .url(taskForImgMessage.getUrl())
                            .build());
        }


    }

    public static BufferedImage downloadImage(String imgSrc) {
        if (StringUtils.isEmpty(imgSrc)) {
            log.error("图片为空：{}", imgSrc);
            return null;
        }
        try {
            URL url = new URL(imgSrc);
            return ImageIO.read(url);
        } catch (IOException e) {
            log.error("下载图片失败：{}", imgSrc, e);
            return null;
        }
    }


}

