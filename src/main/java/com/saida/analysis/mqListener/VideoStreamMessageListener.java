package com.saida.analysis.mqListener;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.InvalidProtocolBufferException;
import com.saida.analysis.config.NetworkReplace;
import com.saida.analysis.confu.ExeConfuHell;
import com.saida.analysis.dto.*;
import com.saida.analysis.entity.DcTaskDispatch;
import com.saida.analysis.grpcSnapServerTask.GrpcSnapServerChannelConfig;
import com.saida.analysis.grpcSnapServerTask.SnapServerMainTask;
import com.saida.analysis.mapper.DcTaskDispatchMapper;
import com.saida.analysis.mq.message.VideoStreamMessage;
import com.saida.analysis.mq.vlinker.*;
import com.saida.services.system.pb.OpenStreamMessage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频流地址监听更新
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class VideoStreamMessageListener implements VLinkerMqMessageListener {

    @Value("${rocketmq.name-server:''}")
    private String nameServer;
    @Value("${vLinker.nodeId:''}")
    private String nodeId;
    @Resource
    private DcTaskDispatchMapper dcTaskDispatchMapper;
    @Resource
    private SnapServerMainTask snapServerMainTask;
    @Resource
    private GrpcSnapServerChannelConfig grpcSnapServerChannelConfig;

    @Getter
    private static final HashMap<Long, TaskDto> deviceTasksMap = new HashMap<>(); // 存储正在执行的设备任务

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("video_stream")
                .tag(nodeId)
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        if (nodeId.equals(vlinkerMqMessage.getTag())) {
            doOnMessage(messageBody);
        } else {
            log.info("[mq:{}] 收到一个不是本节点的数据: {}", vlinkerMqMessage.getTag(), messageBody);
        }
    }

    @Resource
    private ExeConfuHell exeConfuHell;

    public synchronized void doOnMessage(String message) {
        VideoStreamMessage videoStreamMessage = JSON.parseObject(message, VideoStreamMessage.class);
        log.info("[mq:video_stream]解析的数据: {}", JSON.toJSONString(videoStreamMessage));
        DcTaskDispatch dcTaskDispatch = dcTaskDispatchMapper.selectById(videoStreamMessage.getDcTaskDispatchId());
        if (dcTaskDispatch == null) {
            log.info("任务设置，未查询到任务--taskId:{}", videoStreamMessage.getDcTaskDispatchId());
            return;
        }
        // 合并任务，判断设备是否已经有任务在执行
        Long deviceId = dcTaskDispatch.getDeviceId();
//        if ("RTSP".equals(videoStreamMessage.getProtocolCode())) {
//            TaskDto existingTask = deviceTasksMap.get(deviceId);
//            // 如果任务已存在，更新该任务的抽帧频率和其他参数
//            if (existingTask != null) {
//                log.info("原设备任务已经存在 合并他们 deviceId：{}", deviceId);
//                mergeTasks(existingTask, dcTaskDispatch, deviceId);
//                setTaskStopTimer(dcTaskDispatch); // 设置任务停止的定时器
//            } else {
//                log.info("原设备任务已不存在 deviceId：{}", deviceId);
//                // 如果任务不存在，创建一个新的任务
//                TaskDto taskDto = createTaskDto(dcTaskDispatch, videoStreamMessage.getUrl());
//                deviceTasksMap.put(deviceId, taskDto);
//                snapServerMainTask.doFrameExtraction(taskDto); // 启动任务
//            }
//        } else {
//
//        }


        if (exeConfuHell.hasRoom(deviceId)) {
            exeConfuHell.addDcTaskDispatch(dcTaskDispatch);
        } else {
            if ("RTSP".equals(videoStreamMessage.getProtocolCode())) {
                exeConfuHell.addRoom(dcTaskDispatch, videoStreamMessage.getUrl(), new byte[0]);
            } else {
                OpenStreamMessage.StreamReplyForPlayer streamReplyForPlayer = null;
                try {
                    streamReplyForPlayer = OpenStreamMessage.StreamReplyForPlayer.parseFrom(Base64.getDecoder().decode(videoStreamMessage.getUrl()));
                } catch (InvalidProtocolBufferException e) {
                    log.error("解析失败 deviceId:{} ", deviceId, e);
                    return;
                }
                exeConfuHell.addRoom(dcTaskDispatch, streamReplyForPlayer.getAddr(), streamReplyForPlayer.getHandshakeData().toByteArray());
            }


            if (StringUtils.isNotBlank(videoStreamMessage.getSdPushUrl())) {
                try {
                    OpenStreamMessage.StreamReplyForPublisher streamReplyForPublisher = OpenStreamMessage.StreamReplyForPublisher.parseFrom(Base64.getDecoder().decode(videoStreamMessage.getSdPushUrl()));
                    exeConfuHell.startPublishingStream(deviceId, streamReplyForPublisher.getAddr(), streamReplyForPublisher.getHandshakeData().toByteArray());
                } catch (InvalidProtocolBufferException e) {
                    log.error("解析失败 deviceId2:{} ", deviceId, e);
                }
            }
        }
    }

    private void mergeTasks(TaskDto existingTask, DcTaskDispatch newTaskDispatch, Long deviceId) {
        // 比较任务频率，选择最小的频率
        AlgorithmParamConfigDto existingConfig = existingTask.getAlgorithmParamConfigDto();
        AlgorithmParamConfigDto newConfig = JSON.parseObject(newTaskDispatch.getAlgorithmParamConfig(), AlgorithmParamConfigDto.class);
        // 识别帧
        int newFrameRate = newConfig != null ? newConfig.getDefaultFrameRate() : 1;
        int existingFrameRate = existingConfig != null ? existingConfig.getDefaultFrameRate() : 1;
        // 跳过帧
        int newSkipFrameRate = newConfig != null ? newConfig.getDefaultSkipFrameRate() : 1;
        int existingSkipFrameRate = existingConfig != null ? existingConfig.getDefaultSkipFrameRate() : 1;
        //秒
        int newFrameExtractionTime = newConfig != null ? newConfig.getDefaultFrameExtractionTime() : 1;
        int existingFrameExtractionTime = existingConfig != null ? existingConfig.getDefaultFrameExtractionTime() : 1;
        // 是否所有的都是关键帧抽帧？
        boolean keyFrameOnly = newConfig != null && newConfig.getKeyFrameOnly();
        //最大帧 最小秒
        existingTask.getAlgorithmParamConfigDto().setDefaultFrameRate(Math.max(newFrameRate, existingFrameRate));
        existingTask.getAlgorithmParamConfigDto().setDefaultSkipFrameRate(Math.max(newSkipFrameRate, existingSkipFrameRate));
        existingTask.getAlgorithmParamConfigDto().setDefaultFrameExtractionTime(Math.min(newFrameExtractionTime, existingFrameExtractionTime));
        existingTask.getAlgorithmParamConfigDto().setKeyFrameOnly(keyFrameOnly);

        List<PresetPointSetDetailDto> presetPointSetDetailDtoList = JSON.parseArray(newTaskDispatch.getPresetPointSetDetail(), PresetPointSetDetailDto.class);
        for (PresetPointSetDetailDto dto : presetPointSetDetailDtoList) {
            for (PresetPointSetDetailDto.DrawAreaDTO drawAreaDTO : dto.getDrawAreaVOList()) {
                List<PresetPointSetDetailDto.CoordinateDto> coordinates = JSON.parseArray(drawAreaDTO.getCoordinate(), PresetPointSetDetailDto.CoordinateDto.class);
                drawAreaDTO.setCoordinateList(coordinates);
            }
        }

        existingTask.getPresetPointSetDetailDtoList().put(newTaskDispatch.getId(), presetPointSetDetailDtoList);
        existingTask.getAlgorithmParamConfigDtoMap().put(newTaskDispatch.getId(), newConfig);
        DcTaskDispatchDto dcTaskDispatchDto = new DcTaskDispatchDto();
        BeanUtils.copyProperties(newTaskDispatch, dcTaskDispatchDto);
        existingTask.getDcTaskDispatchMap().put(newTaskDispatch.getId(), dcTaskDispatchDto);
        String collect = existingTask.getDcTaskDispatchMap().values().stream().map(DcTaskDispatch::getAlgorithmCode).collect(Collectors.joining(","));
        log.info("{} 合并后当前使用的算法 : {}", dcTaskDispatchDto.getId(), collect);
        log.info("合并任务成功 deviceId：{} taskMapSize:{} ", deviceId, existingTask.getDcTaskDispatchMap().size());
    }

    @Resource
    private NetworkReplace networkReplace;

    private TaskDto createTaskDto(DcTaskDispatch dcTaskDispatch, String url) {
        TaskDto taskDto = new TaskDto();
        DcTaskDispatchDto dcTaskDispatchDto = new DcTaskDispatchDto();
        BeanUtils.copyProperties(dcTaskDispatch, dcTaskDispatchDto);
        String urlTemp = url;
        if (networkReplace.getReplacement() != null && !networkReplace.getReplacement().isEmpty()) {
            for (NetworkReplace.ReplacementDto replacementDto : networkReplace.getReplacement()) {
                urlTemp = urlTemp.replace(replacementDto.getOldUrl(), replacementDto.getNewUrl());
            }
        }
        taskDto.setUrl(urlTemp);
        taskDto.setDeviceId(dcTaskDispatch.getDeviceId());
        // 解析并设置其他参数
        AlgorithmParamConfigDto config = JSON.parseObject(dcTaskDispatch.getAlgorithmParamConfig(), AlgorithmParamConfigDto.class);
        taskDto.setAlgorithmParamConfigDto(config);
        List<PresetPointSetDetailDto> presetPointSetDetailDtoList = JSON.parseArray(dcTaskDispatch.getPresetPointSetDetail(), PresetPointSetDetailDto.class);
        for (PresetPointSetDetailDto dto : presetPointSetDetailDtoList) {
            for (PresetPointSetDetailDto.DrawAreaDTO drawAreaDTO : dto.getDrawAreaVOList()) {
                List<PresetPointSetDetailDto.CoordinateDto> coordinates = JSON.parseArray(drawAreaDTO.getCoordinate(), PresetPointSetDetailDto.CoordinateDto.class);
                drawAreaDTO.setCoordinateList(coordinates);
            }
        }
        log.info("{} 当前使用的算法 : {}", dcTaskDispatchDto.getId(), dcTaskDispatch.getAlgorithmCode());
        taskDto.getPresetPointSetDetailDtoList().put(dcTaskDispatch.getId(), presetPointSetDetailDtoList);
        taskDto.getAlgorithmParamConfigDtoMap().put(dcTaskDispatch.getId(), config);
        dcTaskDispatchDto.setAlgorithmParamConfigDto(config);
        dcTaskDispatchDto.setPresetPointSetDetailDtoList(presetPointSetDetailDtoList);
        taskDto.getDcTaskDispatchMap().put(dcTaskDispatch.getId(), dcTaskDispatchDto);
        return taskDto;
    }

    @Getter
    private static final HashMap<Long, LocalTime> taskStopTimers = new HashMap<>(); // 存储设备定时任务

    @Scheduled(fixedRate = 30 * 1000)
    public void taskQueuesSize() {
        taskStopTimers.forEach((deviceId, localTime) -> {
            if (localTime.isBefore(LocalTime.now())) {
                log.info("任务 {} 已经停止", deviceId);
                grpcSnapServerChannelConfig.closeChannelByDeviceId(deviceId);
                exeConfuHell.removeRoom(deviceId);
            }
        });
    }

    private void setTaskStopTimer(DcTaskDispatch dcTaskDispatch) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        int currentWeekday = currentDateTime.getDayOfWeek().getValue();
        LocalTime currentTime = currentDateTime.toLocalTime();

        if (StringUtils.isBlank(dcTaskDispatch.getTimePlan())) {
            log.error("setTaskStopTimer 时间模板是空的 直接不做停止 taskId:{}", dcTaskDispatch.getId());
            return;
        }

        List<DcTimeDto> timeTemplates = JSON.parseArray(dcTaskDispatch.getTimePlan(), DcTimeDto.class);
        Optional<DcTimeDto> currentDayTemplate = timeTemplates.stream()
                .filter(template -> template.getWeekIndex() + 1 == currentWeekday)
                .findFirst();

        DcTimeDto dcTimeDto = currentDayTemplate.orElse(null);
        if (dcTimeDto == null) {
            log.error("setTaskStopTimer 未找到对应周的模板 taskId:{},currentWeekday:{}", dcTaskDispatch.getId(), currentWeekday);
            return;
        }

        LocalTime closestEndTime = dcTimeDto.getTimeList().stream()
                .map(dcTime -> LocalTime.parse(dcTime.getEndTime()))
                .filter(endTime -> endTime.isAfter(currentTime))
                .min(LocalTime::compareTo)
                .orElse(null);

        if (closestEndTime != null) {
            Long deviceId = dcTaskDispatch.getDeviceId();
            LocalTime existingEndTime = taskStopTimers.get(deviceId);

            if (existingEndTime != null) {
                // 如果当前设备已有任务，更新任务的结束时间
                LocalTime newEndTime = closestEndTime.isAfter(existingEndTime) ? closestEndTime : existingEndTime;
                taskStopTimers.put(deviceId, newEndTime);
                log.info("更新任务结束时间，设备ID: {}, 新的结束时间: {}", deviceId, newEndTime);
            } else {
                // 如果设备没有任务，则直接设置新的结束时间
                taskStopTimers.put(deviceId, closestEndTime);
                log.info("设置任务结束时间，设备ID: {}, 结束时间: {}", deviceId, closestEndTime);
            }
        } else {
            log.error("setTaskStopTimer 未找到最接近的结束时间 taskId:{},currentWeekday:{}", dcTaskDispatch.getId(), currentWeekday);
        }
    }

}

