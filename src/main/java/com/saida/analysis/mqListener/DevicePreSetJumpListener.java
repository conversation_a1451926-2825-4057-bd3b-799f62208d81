package com.saida.analysis.mqListener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.analysis.confu.ExeConfuHell;
import com.saida.analysis.entity.DcTaskDispatch;
import com.saida.analysis.mapper.DcTaskDispatchMapper;
import com.saida.analysis.mq.message.DevicePreSetJumpMessage;
import com.saida.analysis.mq.vlinker.*;
import com.saida.analysis.service.AnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 设备跳转预置点位
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class DevicePreSetJumpListener implements VLinkerMqMessageListener {


    @Resource
    private DcTaskDispatchMapper dcTaskDispatchMapper;

    @Override
    public VLinkerTopicConfig vLinkerTopicConfig() {
        return VLinkerTopicConfig.builder()
                .topic("device_pre_set_jump")
                .tag("*")
                .writeQueueNums(4)
                .readQueueNums(4)
                .consumeModel(VLinkerConsumeModel.CLUSTERING)
                .consumeFromWhere(VLinkerConsumeFromWhere.TIMESTAMP)
                .build();
    }

    @Override
    public void onMessage(VlinkerMqMessage vlinkerMqMessage) {
        String messageBody = new String(vlinkerMqMessage.getData(), StandardCharsets.UTF_8);
        DevicePreSetJumpMessage message = JSON.parseObject(messageBody, DevicePreSetJumpMessage.class);
        onMessage(message);
    }

    @Resource
    private ExeConfuHell exeConfuHell;

    public void onMessage(DevicePreSetJumpMessage message) {
        log.info("[mq:device_pre_set_jump]接收到消息：{}", message);
        AnalysisService.getDevicePre().put(message.getCameraId(), message);

        if (message.getRotationTime() == null || message.getRotationTime() <= 0) {
            return;
        }

        List<DcTaskDispatch> dcTaskDispatches = dcTaskDispatchMapper.selectList(new LambdaQueryWrapper<DcTaskDispatch>()
                .eq(DcTaskDispatch::getDeviceId, message.getCameraId()));
        long l = System.currentTimeMillis();
        dcTaskDispatches.forEach(dcTaskDispatch -> {
            AnalysisService.getLastTaskAlarmMap().put(dcTaskDispatch.getId(), l + (message.getRotationTime() * 1000));
            log.info("[mq:device_pre_set_jump]taskId:{}，deviceId:{}，lastAlarmTime:{}"
                    , dcTaskDispatch.getId(), dcTaskDispatch.getDeviceId(), l + (message.getRotationTime() * 1000));
        });
        exeConfuHell.setDevicePreSetJumpMessage(message.getCameraId(), message);
    }


}

