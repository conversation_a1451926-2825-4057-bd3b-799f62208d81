package com.saida.analysis.grpcSnapServerTask;


import cn.hutool.core.io.FileUtil;
import com.saida.analysis.dto.TaskDto;
import com.saida.analysis.mq.message.FrameRateStatisticsMessage;
import com.saida.analysis.mq.message.GrpcTaskStatusMessage;
import com.saida.analysis.mq.vlinker.VLinkerMqTemplate;
import com.saida.analysis.mqListener.RockerMqTopic;
import com.saida.analysis.mqListener.VideoStreamMessageListener;
import com.saida.analysis.service.AnalysisService;
import io.grpc.Grpc;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * grpc 服务端配置类
 */

@Configuration
@Slf4j
public class GrpcSnapServerChannelConfig {


    @Resource
    private VLinkerMqTemplate vLinkerMqTemplate;

    @Value("${grpc2.server.host:*********}")
    private String grpcServerHost;

    @Value("${grpc2.server.port:18777}")
    private int grpcServerPort;
    // TLS 配置
    private TlsChannelCredentials.Builder tlsBuilder;

    @Data
    private static class ManagedChannelDto {
        private ManagedChannel managedChannel;
        /**
         * N帧
         * 任务在启动的时候的数据
         */
        private Integer defaultFrameRate;

        /**
         * N秒
         * 任务在启动的时候的数据
         */
        private Integer defaultFrameExtractionTime;
    }

    // 任务 ID -> ManagedChannel 映射表
    /**
     * 本来是想用安全map的  但是不知道为啥被锁住了 所以只能用HashMap了
     */
    private static HashMap<Long, ManagedChannelDto> taskChannels = new HashMap<>();

    // 设备抽帧计数器
    @Getter
    private static final Map<Long, FrameRateStatisticsMessage.DeviceFrameDto> deviceFrameCounters = new ConcurrentHashMap<>();

    @Resource
    private VideoStreamMessageListener videoStreamMessageListener;

    public boolean isHasChannel(Long deviceId, Long taskId) {
        if (taskChannels.containsKey(deviceId)) {
            TaskDto taskDto = videoStreamMessageListener.getDeviceTasksMap().get(deviceId);
            if (taskDto != null) {
                return taskDto.getDcTaskDispatchMap().containsKey(taskId);
            }
        }
        return false;
    }

    public boolean isHasChannel(Long deviceId) {
        return taskChannels.containsKey(deviceId);
    }

    @PostConstruct
    public void init() {
        log.info("gRPC 服务配置初始化完成: {}:{}", grpcServerHost, grpcServerPort);
        try {
            this.tlsBuilder = createTlsBuilder();
        } catch (Exception e) {
            log.error("gRPC 服务配置初始化异常", e);
        }
    }

    @Value("${vLinker.nodeId:''}")
    private String nodeId;

    /**
     * 记录设备抽帧
     *
     * @param deviceId 设备ID
     */
    public void recordFrame(Long deviceId) {
        deviceFrameCounters.computeIfAbsent(deviceId, k -> new FrameRateStatisticsMessage.DeviceFrameDto())
                .getFrameCount().incrementAndGet();
    }

    /**
     * 定时统计抽帧频率并上报
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void statisticsFrameRate() {
        long currentTime = System.currentTimeMillis();
        // 发送统计数据到MQ
        if (!deviceFrameCounters.isEmpty()) {
            deviceFrameCounters.forEach((deviceId, counter) -> {
                // 抽帧频率 = 抽帧总数 / (当前时间 - 首帧时间) / 1000
                long time = (currentTime - counter.getFirstFrameTime()) / 1000;
                counter.setFrameRate((double) counter.getFrameCount().get() / (time == 0 ? 1 : time));
            });
            FrameRateStatisticsMessage message = FrameRateStatisticsMessage.builder()
                    .nodeId(nodeId)
                    .timestamp(currentTime)
                    .deviceFrameRates(deviceFrameCounters)
                    .build();
            vLinkerMqTemplate.send(RockerMqTopic.FRAME_RATE_STATISTICS.getTopic(), message);
            log.info("抽帧频率统计：{}", message);
        }
    }

    @Scheduled(cron = "0 0/5 * * * ? ")
    public void taskRecord() {
        log.info("gRPC 服务状态检查 taskChannels.size():{}", taskChannels.size());
        taskChannels.forEach((deviceId, channel) -> {
            log.info("任务 {} 的 gRPC 通道状态：isShutdown：{}，isTerminated：{}",
                    deviceId, channel.getManagedChannel().isShutdown(), channel.getManagedChannel().isTerminated());
            if (channel.getManagedChannel().isShutdown() || channel.getManagedChannel().isTerminated()) {
                closeChannelByDeviceId(deviceId);
            }
        });

        List<Long> ids = new ArrayList<>();
        taskChannels.forEach((deviceId, channel) -> {
            ids.add(deviceId);
        });

        vLinkerMqTemplate.send(RockerMqTopic.GRPC_TASK_STATUS.getTopic()
                , GrpcTaskStatusMessage.builder()
                        .time(System.currentTimeMillis())
                        .taskIds(ids)
                        .nodeId(nodeId)
                        .build());
    }

    // 获取或创建通道，如果频率不一致则重启通道
    public ManagedChannel getOrCreateChannel(Long deviceId, Integer newFrequency, Integer newFrameExtractionTime) {
        ManagedChannelDto channelDto = taskChannels.get(deviceId);

        if (channelDto != null) {
            // 检查频率是否一致
            if (!channelDto.getDefaultFrameRate().equals(newFrequency) || !channelDto.getDefaultFrameExtractionTime().equals(newFrameExtractionTime)) {
                // 频率不一致，关闭旧通道并重启
                closeChannelByDeviceId(deviceId);
                log.info("设备 {} 的任务 频率变化，重新启动 gRPC 通道", deviceId);
            }
        }

        // 创建或获取通道
        ManagedChannelDto managedChannelDto = taskChannels.computeIfAbsent(deviceId, id -> createNewChannel(deviceId));
        if (managedChannelDto == null) {
            log.error("设备 {} 的 gRPC 通道为空", deviceId);
            throw new RuntimeException("设备 " + deviceId + " 的 gRPC 通道为空 而且还创建失败了！");
        }
        ManagedChannel channel = managedChannelDto.getManagedChannel();
        // 更新频率信息
        channelDto = taskChannels.get(deviceId);
        if (channelDto != null) {
            channelDto.setDefaultFrameRate(newFrequency);
            channelDto.setDefaultFrameExtractionTime(newFrameExtractionTime);
        }
        return channel;
    }

    // 创建新的 gRPC 通道
    private ManagedChannelDto createNewChannel(Long deviceId) {
        try {
            log.info("正在创建设备 {} 的 gRPC 通道 grpc:{}:{}", deviceId, grpcServerHost, grpcServerPort);
            ManagedChannel channel = Grpc.newChannelBuilderForAddress(grpcServerHost, grpcServerPort, tlsBuilder.build())
                    .idleTimeout(30, TimeUnit.SECONDS)
                    .keepAliveWithoutCalls(true)
                    .keepAliveTime(60, TimeUnit.SECONDS)
                    .keepAliveTimeout(20, TimeUnit.SECONDS)
                    .disableRetry()
                    .maxInboundMetadataSize(32 * 1024)
                    .build();

            ManagedChannelDto managedChannelDto = new ManagedChannelDto();
            managedChannelDto.setManagedChannel(channel);
            log.info("设备 {} 的 gRPC 通道已成功创建", deviceId);
            return managedChannelDto;
        } catch (Exception e) {
            log.error("创建 gRPC 通道时发生异常: ", e);
            return null;
        }
    }

    // 创建新的 gRPC 通道
    public ManagedChannel createNewChannelTemp() {
        try {
            log.info("正在创建设备 temp 的 gRPC 通道 grpc:{}:{}", grpcServerHost, grpcServerPort);
            return Grpc.newChannelBuilderForAddress(grpcServerHost, grpcServerPort, tlsBuilder.build())
                    .idleTimeout(30, TimeUnit.SECONDS)
                    .keepAliveWithoutCalls(true)
                    .keepAliveTime(60, TimeUnit.SECONDS)
                    .keepAliveTimeout(20, TimeUnit.SECONDS)
                    .disableRetry()
                    .maxInboundMetadataSize(32 * 1024)
                    .build();
        } catch (Exception e) {
            log.error("创建 gRPC 通道时发生异常: ", e);
            return null;
        }
    }


    @Resource
    private AnalysisService analysisService;


    /**
     * 关闭设备的所有线程、缓存、grpc信息
     */
    public void closeChannelByDeviceId(Long deviceId) {
        // 关闭任务处理线程
        analysisService.shutdownByDeviceId(deviceId);
        ManagedChannelDto channelDto = taskChannels.remove(deviceId);
        if (channelDto != null) {
            ManagedChannel channel = channelDto.getManagedChannel();
            if (channel != null) {
                log.info("正在关闭设备 {} 的 gRPC 通道", deviceId);
                channel.shutdownNow();
            }
        } else {
            log.warn("设备 {} 的 gRPC 通道未找到或已关闭", deviceId);
        }
    }


    @Value("${grpc2.server.outputPath:}")
    private String outputPath;


    /**
     * 应用关闭时清理所有 gRPC 通道
     */
    @PreDestroy
    public void shutdownAllChannels() {
        log.info("应用停止，开始关闭所有任务的 gRPC 通道");
        String outputPathTemp = outputPath;
        if (!outputPathTemp.endsWith("/")) {
            outputPathTemp = outputPathTemp + "/";
        }
        if (FileUtil.exist(outputPathTemp)) {
            FileUtil.del(outputPathTemp);
        }
        taskChannels.forEach((taskId, channel) -> {
            channel.getManagedChannel().shutdownNow();
        });
        taskChannels.clear();

        taskChannels = null;
    }


    /**
     * 创建 TLS 配置
     */
    private TlsChannelCredentials.Builder createTlsBuilder() {
        TlsChannelCredentials.Builder tlsBuilder = TlsChannelCredentials.newBuilder();
        try {
            ClassPathResource resource = new ClassPathResource("algConfig/ca.crt");
            if (!resource.exists()) {
                log.error("证书文件不存在：请检查证书路径！path:{}", resource.getPath());
                throw new RuntimeException("证书文件不存在：请检查证书路径！");
            }
            tlsBuilder.trustManager(resource.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException("加载 TLS 证书失败", e);
        }
        return tlsBuilder;
    }
}
