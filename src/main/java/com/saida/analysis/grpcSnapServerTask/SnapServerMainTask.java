package com.saida.analysis.grpcSnapServerTask;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.saida.analysis.confu.ExeConfuHell;
import com.saida.analysis.dto.AlgorithmParamConfigDto;
import com.saida.analysis.dto.AnalysisDto;
import com.saida.analysis.dto.DcTimeDto;
import com.saida.analysis.dto.TaskDto;
import com.saida.analysis.entity.DcTaskDispatch;
import com.saida.analysis.mapper.DcTaskDispatchMapper;
import com.saida.analysis.mq.message.VideoStreamMessage;
import com.saida.analysis.mq.vlinker.VLinkerMqTemplate;
import com.saida.analysis.mqListener.RockerMqTopic;
import com.saida.analysis.service.AnalysisService;
import com.saida.analysis.snapServer.pb.SnapServerGrpc;
import com.saida.analysis.snapServer.pb.SnapServerOuterClass;
import com.saida.analysis.util.TimeUtil;
import io.grpc.ManagedChannel;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

@Slf4j
@Component
public class SnapServerMainTask {
    @Resource
    private GrpcSnapServerChannelConfig grpcSnapServerChannelConfig;
    @Resource
    private DcTaskDispatchMapper dcTaskDispatchMapper;
    @Resource
    private VLinkerMqTemplate vLinkerMqTemplate;
    @Resource
    private ExeConfuHell exeConfuHell;

    @Scheduled(fixedRate = 30 * 1000)
    public void startAsync() {
        // 获取当前时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        int currentWeekday = currentDateTime.getDayOfWeek().getValue();  // 获取今天是周几 (1-7, 1为周一)
        LocalTime currentTime = currentDateTime.toLocalTime();  // 获取当前时间
        // 重启已经启动的任务
        List<DcTaskDispatch> dcTaskDispatches = dcTaskDispatchMapper.selectList(new LambdaQueryWrapper<DcTaskDispatch>()
                .eq(DcTaskDispatch::getStatus, 1));
        log.info("一共有{}个启用的任务 currentTime:{}", dcTaskDispatches.size(), currentTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        dcTaskDispatches.forEach(dcTaskDispatch -> {
            //验证一下算法参数是否正确
            try {
                if (StringUtils.isBlank(dcTaskDispatch.getAlgorithmParamConfig())) {
                    log.error("算法参数格式化异常 deviceId:{}", dcTaskDispatch.getId());
                    return;
                }
                if (dcTaskDispatch.getStatus() != 1) {
                    log.error("任务未启用 deviceId:{}", dcTaskDispatch.getId());
                    return;
                }
                boolean hasChannel = grpcSnapServerChannelConfig.isHasChannel(dcTaskDispatch.getDeviceId(), dcTaskDispatch.getId());
                if (hasChannel) {
                    log.info("已经启动了任务 deviceId:{}", dcTaskDispatch.getId());
                    // 已经走了rtsp的抽帧解析了
                    return;
                }
                boolean b = exeConfuHell.hasRoom(dcTaskDispatch.getDeviceId(), dcTaskDispatch.getId());
                if (b) {
                    // 有算法房间了
                    log.info("有算法房间了 deviceId:{}", dcTaskDispatch.getId());
                    return;
                }
                if (StringUtils.isNotBlank(dcTaskDispatch.getTimePlan())) {
                    // 判断当前时间是否在时间模板中
                    boolean isWithinTimeRange = false;
                    try {
                        List<DcTimeDto> dcTimeDtos = JSON.parseArray(dcTaskDispatch.getTimePlan(), DcTimeDto.class);
                        isWithinTimeRange = TimeUtil.isCurrentTimeInTemplate(dcTimeDtos, currentWeekday, currentTime);
                    } catch (Exception e) {
                        log.error("时间模板格式化异常 deviceId:{}", dcTaskDispatch.getId(), e);
                    }
                    if (!isWithinTimeRange) {
                        log.info("当前时间不在模板中 deviceId:{}", dcTaskDispatch.getId());
                        return;  // 如果当前时间不在模板中，跳过该任务
                    }
                }
                //执行重试  这个地方 如果对方没回应 这个任务就被遗失了
                VideoStreamMessage videoStreamMessage = new VideoStreamMessage();
                videoStreamMessage.setReqId(UUID.randomUUID().toString());
                videoStreamMessage.setCameraId(dcTaskDispatch.getDeviceId());
                //3-RTMP；4-RTSP 8 SD_URL
                videoStreamMessage.setProtocolCode("8,4");
                videoStreamMessage.setDcTaskDispatchId(dcTaskDispatch.getId());
                vLinkerMqTemplate.send(RockerMqTopic.VIDEO_STREAM_GET.getTopic(), videoStreamMessage);
            } catch (Exception e) {
                log.error("算法参数格式化异常 deviceId:{}", dcTaskDispatch.getId(), e);
            }

        });
    }

    @Value("${grpc2.server.outputPath:}")
    private String outputPath;


    public void doFrameExtraction(TaskDto taskDto) {
        String urlVideo = taskDto.getUrl();
        Long deviceId = taskDto.getDeviceId();
        log.info("开始执行-任务: deviceId:{} taskDto={}", deviceId, taskDto);
        AlgorithmParamConfigDto config = taskDto.getAlgorithmParamConfigDto();
        Integer defaultFrameRate = 1;
        Integer defaultSkipFrameRate = 1;
        Integer defaultFrameExtractionTime = 1;
        Boolean keyFrameOnly = false;
        if (config != null) {
            defaultFrameRate = config.getDefaultFrameRate();
            defaultSkipFrameRate = config.getDefaultSkipFrameRate();
            defaultFrameExtractionTime = config.getDefaultFrameExtractionTime();
            keyFrameOnly = config.getKeyFrameOnly();
        }
        try {
            String outputPathTemp = outputPath;
            if (outputPathTemp.endsWith("/")) {
                outputPathTemp = outputPathTemp + deviceId;
            } else {
                outputPathTemp = outputPathTemp + "/" + deviceId;
            }
            if (FileUtil.exist(outputPathTemp)) {
                boolean del = FileUtil.del(outputPathTemp);
                log.info("deviceId:{}删除文件夹  outputPathTemp:{} del:{}", deviceId, outputPathTemp, del);
            }
            File mkdir = FileUtil.mkdir(outputPathTemp);
            boolean success = mkdir.exists() && mkdir.isDirectory();
            log.info("deviceId:{} 创建文件夹 outputPathTemp:{} success:{}", deviceId, outputPathTemp, success);
            SnapServerOuterClass.TaskModel request = SnapServerOuterClass.TaskModel.newBuilder()
                    .setKeyFrameOnly(keyFrameOnly)
                    //连续处理多少帧  1s 25帧
                    .setFrameStep(defaultFrameRate)
                    //跳过多少帧
                    .setSkipStep(defaultSkipFrameRate)//2
                    .setInputURL(urlVideo)
                    .setOutputPath(outputPathTemp)
                    .build();
            log.info("构建Grpc 数据 deviceId:{} keyFrameOnly： {} defaultFrameRate: {} ,defaultSkipFrameRate:{}, urlVideo:{}",
                    taskDto.getDeviceId(), keyFrameOnly, defaultFrameRate, defaultSkipFrameRate, urlVideo);

            // 获取或创建异步 gRPC 通道
            ManagedChannel orCreateChannel = grpcSnapServerChannelConfig.getOrCreateChannel(deviceId, defaultFrameRate, defaultFrameExtractionTime);
            SnapServerGrpc.SnapServerStub asyncStub = SnapServerGrpc.newStub(orCreateChannel);
            if (asyncStub == null) {
                log.error("任务 {} 获取异步 gRPC Stub 失败", deviceId);
                return;
            } else {
                log.info("任务 {} 获取异步 gRPC Stub 成功", deviceId);
            }

            String finalOutputPathTemp = outputPathTemp;
            asyncStub.newSnapTask(request, new StreamObserver<>() {

                @Override
                public void onNext(SnapServerOuterClass.TaskReply taskReply) {
                    handleReply(taskReply, taskDto); // 正常业务处理
                }

                @Override
                public void onError(Throwable t) {
                    log.error("任务id {},rtspUrl:{}, gRPC 调用出错: {}", deviceId, taskDto.getUrl(), t.getMessage(), t);
                    if (FileUtil.exist(finalOutputPathTemp)) {
                        FileUtil.del(finalOutputPathTemp);
                    }
                    if (t instanceof StatusRuntimeException) {
                        StatusRuntimeException statusException = (StatusRuntimeException) t;
                        log.error("gRPC 错误状态: {}", statusException.getStatus());
                        Status status = statusException.getStatus();
                        String description = status.getDescription();
                        if (StringUtils.isNotEmpty(description) && description.contains("shutdownNow")) {
                            return;
                        }
                    }
                    try {
                        // 假设 grpcServerConfig 管理了与服务端的连接，你可以在这里执行关闭操作
                        grpcSnapServerChannelConfig.closeChannelByDeviceId(deviceId);
                        log.info("onError 任务 {} 的 gRPC 连接已成功关闭", deviceId);
                    } catch (Exception e) {
                        log.error("onError 任务 {} 在关闭 gRPC 连接时发生错误: {}", deviceId, e.getMessage(), e);
                    }
                }

                @Override
                public void onCompleted() {
                    try {
                        if (FileUtil.exist(finalOutputPathTemp)) {
                            FileUtil.del(finalOutputPathTemp);
                        }
                        // 假设 grpcServerConfig 管理了与服务端的连接，你可以在这里执行关闭操作
                        grpcSnapServerChannelConfig.closeChannelByDeviceId(deviceId);
                        log.info("onCompleted 任务 {} 的 gRPC 连接已成功关闭", deviceId);
                    } catch (Exception e) {
                        log.error("onCompleted 任务 {} 在关闭 gRPC 连接时发生错误: {}", deviceId, e.getMessage(), e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("任务 {} 在执行过程中发生异常: {}", deviceId, e.getMessage(), e);
            grpcSnapServerChannelConfig.closeChannelByDeviceId(deviceId);
        }
    }


    // 处理每次收到的响应（主要是拿到图片是算法结果）
    private void handleReply(SnapServerOuterClass.TaskReply reply, TaskDto taskDto) {
        if (reply.getType() == SnapServerOuterClass.TaskReplyType.TASK_REPLY_TYPE_DATA) {
            log.info("handleReply 任务 {} 收到一次抽帧响应，desc: {} path:{}", taskDto.getDeviceId(), reply.getDesc(), reply.getOutputPath());
            // 记录抽帧
            grpcSnapServerChannelConfig.recordFrame(taskDto.getDeviceId());
            AnalysisDto analysisDto = new AnalysisDto();
            analysisDto.setTaskDto(taskDto);
            analysisDto.setTimestamp(System.currentTimeMillis());
//            analysisDto.setImgUrl(reply.getOutputPath().replace("/tmp/","D:\\imgTemp\\"));
            analysisDto.setImgUrl(reply.getOutputPath());
            analysisService.doAnalysis(analysisDto);
        } else if (reply.getType() == SnapServerOuterClass.TaskReplyType.TASK_REPLY_TYPE_INFO_WITHOUT_ERROR) {
            log.info("handleReply 任务 {} 收到分析任务想说的话，desc: {}", taskDto.getDeviceId(), reply.getDesc());
        } else {
            log.info("handleReply 任务 {} 收到一次不知道什么吊东西，desc: {}", taskDto.getDeviceId(), reply.getType().getNumber());
        }
    }


    @Resource
    private AnalysisService analysisService;

}
