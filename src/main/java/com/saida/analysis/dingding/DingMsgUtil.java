package com.saida.analysis.dingding;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.LinkedBlockingQueue;

@Slf4j
@Component
public class DingMsgUtil {

//    public static final String CUSTOM_ROBOT_TOKEN = "8b844823342850eb4aa80fb097459e73c6344b55e1d396c76b88d103fb1e2682";
//
//    public static final String SECRET = "SEC886a3561a8f4493242718cd9c345b79fa4b964f52579193bffdbcadce78858ee";

//    public static final String CUSTOM_ROBOT_TOKEN = "40051e0d7b51796b8767cb417c39f9fa3403c62a2d3bf9cda1544fa29db028d8";
//
//    public static final String SECRET = "SECcad4d76f13e500baf1b77e425d6db39d8c8f2a1e63fcfcf279c6a050d512c451";

    @Value("${vLinker.nodeId:''}")
    private String nodeId;
    @Value("${vLinker.ding-enable:''}")
    private Boolean dingEnable;
    @Value("${vLinker.ding.secret:''}")
    private String secret;
    @Value("${vLinker.ding.token:''}")
    private String custom_robot_token;

    private static final LinkedBlockingQueue<String> queue = new LinkedBlockingQueue<>(1000);

    public static void sendMsgSync(String msg) {
        queue.add(msg);
    }

    @PostConstruct
    public void init() {
        new Thread(() -> {
            while (true) {
                try {
                    String msg = queue.take();
                    sendMsg(msg);
                } catch (InterruptedException e) {
                    log.error("InterruptedException", e);
                }
            }
        }).start();
    }

    public void sendMsg(String msg) {
        if (!dingEnable) {
            return;
        }
        try {
            msg = "nodeId:" + nodeId + "," + msg;
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");

            //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?sign=" + sign + "&timestamp=" + timestamp);
            OapiRobotSendRequest req = new OapiRobotSendRequest();
            /**
             * 发送文本消息
             */
            //定义文本内容
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            text.setContent(msg);
            //定义 @ 对象
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            //设置消息类型
            req.setMsgtype("text");
            req.setText(text);
            req.setAt(at);
            OapiRobotSendResponse rsp = client.execute(req, custom_robot_token);
        } catch (Exception e) {
            log.error("sendMsg error", e);
        }
    }
}
