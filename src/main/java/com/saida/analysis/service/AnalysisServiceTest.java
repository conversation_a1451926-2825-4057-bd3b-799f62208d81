package com.saida.analysis.service;

import com.alibaba.fastjson.JSON;
import com.saida.analysis.dto.GrpcAlgorithmResultDto;
import com.saida.analysis.dto.PresetPointSetDetailDto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AnalysisServiceTest {
    /**
     * {"abd_evt_type":0,"cpc_evt_type":0,"in_num":0,"label":80,"maxx":1633,"maxy":717,"minx":1472,"miny":532,"out_num":0,"prob":0.9353494644165039,"roi_line_id":0.0,"traj":[{"x":1552,"y":624}],"traj_len":1}
     *
     *
     * [{
     * 		"x": 0.07566585956416465,
     * 		"y": 0.9577616357277374
     *        }, {
     * 		"x": 0.9570217917675545,
     * 		"y": 0.34221146085552867
     *    }, {
     * 		"x": 0.5856667959814323,
     * 		"y": 0.9636904947701838
     *    }, {
     * 		"x": 0.44702085535028674,
     * 		"y": 0.3362826018130822
     *    }]
     */

    public static void main(String[] args) {
        String a = "[{\n" +
                "\t\t\"x\": 0.07566585956416465,\n" +
                "\t\t\"y\": 0.9577616357277374\n" +
                "\t}, {\n" +
                "\t\t\"x\": 0.9570217917675545,\n" +
                "\t\t\"y\": 0.34221146085552867\n" +
                "\t}, {\n" +
                "\t\t\"x\": 0.5856667959814323,\n" +
                "\t\t\"y\": 0.9636904947701838\n" +
                "\t}, {\n" +
                "\t\t\"x\": 0.44702085535028674,\n" +
                "\t\t\"y\": 0.3362826018130822\n" +
                "\t}]";
        List<GrpcAlgorithmResultDto> results = new ArrayList<>();
        GrpcAlgorithmResultDto result1 = new GrpcAlgorithmResultDto();
        result1.setMinx(1472);
        result1.setMiny(532);
        result1.setMaxx(1633);
        result1.setMaxy(717);
        results.add(result1);

        List<PresetPointSetDetailDto.DrawAreaDTO> banLineAreas = new ArrayList<>();
        PresetPointSetDetailDto.DrawAreaDTO drawAreaDTO = new PresetPointSetDetailDto.DrawAreaDTO();
        drawAreaDTO.setName("saida");
        drawAreaDTO.setType(2);
        drawAreaDTO.setCheckRule(1);
        List<PresetPointSetDetailDto.CoordinateDto> coordinateList = JSON.parseArray(a, PresetPointSetDetailDto.CoordinateDto.class);
        drawAreaDTO.setCoordinateList(coordinateList);
        banLineAreas.add(drawAreaDTO);
        List<GrpcAlgorithmResultDto> grpcAlgorithmResultDtos = AnalysisService.filterResults(results, banLineAreas, 1920, 1080);
        System.out.println(grpcAlgorithmResultDtos.size());
    }

    public static void testBanLineArea() {
        int imageWidth = 1920;
        int imageHeight = 1080;

        // 拌线区域点（归一化）
        List<double[]> normPoints = Arrays.asList(
                new double[]{0.07566585956416465, 0.9577616357277374},
                new double[]{0.9570217917675545, 0.34221146085552867},
                new double[]{0.5856667959814323, 0.9636904947701838},
                new double[]{0.44702085535028674, 0.3362826018130822}
        );

        // 转换为像素坐标
        double[][] points = new double[4][2];
        for (int i = 0; i < 4; i++) {
            points[i][0] = normPoints.get(i)[0] * imageWidth;
            points[i][1] = normPoints.get(i)[1] * imageHeight;
        }

        // 计算宽高向量
        double[] wVec = {(points[1][0] - points[0][0]) / 2.0, (points[1][1] - points[0][1]) / 2.0};
        double[] hVec = {(points[3][0] - points[2][0]) / 2.0, (points[3][1] - points[2][1]) / 2.0};

        // 计算中心点
        double[] center = new double[2];
        for (int i = 0; i < 4; i++) {
            center[0] += points[i][0];
            center[1] += points[i][1];
        }
        center[0] /= 4.0;
        center[1] /= 4.0;

        // 构造矩形四角
        double[][] polygon = new double[4][2];
        polygon[0] = new double[]{center[0] - wVec[0] - hVec[0], center[1] - wVec[1] - hVec[1]};
        polygon[1] = new double[]{center[0] + wVec[0] - hVec[0], center[1] + wVec[1] - hVec[1]};
        polygon[2] = new double[]{center[0] + wVec[0] + hVec[0], center[1] + wVec[1] + hVec[1]};
        polygon[3] = new double[]{center[0] - wVec[0] + hVec[0], center[1] - wVec[1] + hVec[1]};

        // 目标框
        int minx = 1472, miny = 532, maxx = 1633, maxy = 717;
        int[][] rectPoints = {
                {minx, miny},
                {minx, maxy},
                {maxx, miny},
                {maxx, maxy}
        };

        // 判断是否任意一个角在多边形内
        boolean anyInside = false;
        for (int[] pt : rectPoints) {
            if (pointInPolygon(pt[0], pt[1], polygon)) {
                anyInside = true;
                break;
            }
        }

        System.out.println("是否在拌线区域内: " + anyInside);
    }

    // 点在多边形内（射线法）
    private static boolean pointInPolygon(double x, double y, double[][] polygon) {
        int n = polygon.length;
        boolean inside = false;
        for (int i = 0, j = n - 1; i < n; j = i++) {
            double xi = polygon[i][0], yi = polygon[i][1];
            double xj = polygon[j][0], yj = polygon[j][1];

            boolean intersect = ((yi > y) != (yj > y)) &&
                    (x < (xj - xi) * (y - yi) / ((yj - yi) + 1e-10) + xi);
            if (intersect) {
                inside = !inside;
            }
        }
        return inside;
    }

}
