package com.saida.analysis.service;

import com.alibaba.fastjson.JSON;
import com.saida.analysis.config.FuAnAlgConfig;
import com.saida.analysis.config.OssConfig;
import com.saida.analysis.util.ContentTypeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.*;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@ConditionalOnProperty(prefix = "oss", name = "enable", havingValue = "true")
@Component
public class FileService {

    @Autowired
    private OssConfig ossConfig;

    private S3Client s3Client;

    @Autowired(required = false)
    private FuAnAlgConfig fuAnAlgConfig;

    @PostConstruct
    public void init() {
        if (fuAnAlgConfig != null) {
            // 创建一个S3Rule实例，用于设置桶的生命周期规则
            S3Rule r = new S3Rule();
            r.setPrefix(fuAnAlgConfig.getCloudStoragePrefix());
            r.setId(r.getPrefix());
            r.setExpirationInDays(1);
            try {
                setBucketLifecycleConfiguration(r);
            } catch (Exception e) {
                log.error("setBucketLifecycleConfiguration -> {}", e.getMessage());
            }
        }
    }


    public static void main(String[] args) throws Exception {
        String url = "/Users/<USER>/Downloads/1876535370527608834.jpg";
        File file = new File(url);
        S3Client s3Client = S3Client.builder()
                .endpointOverride(new URI("https://eos.anhui-2.cmecloud.cn"))
                .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create("DWFE9XC8GZVCQU06VTYJ", "hq8wGstVbuR2NQCMTOuFCe6TeoUhgw3yamx6PNf5")))
                .serviceConfiguration(S3Configuration.Builder::pathStyleAccessEnabled)
                .forcePathStyle(true)
                .region(Region.of("anhui2"))
                .build();

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] bytes = new byte[(int) file.length()];
            fis.read(bytes); // 读取文件内容到字节数组
            // 上传 Object
            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket("vlinker-open")
                    .key("aaaa/aaa.jpg") // 目标S3上的路径和文件名
                    .acl(ObjectCannedACL.PUBLIC_READ)
                    .build();
            PutObjectResponse putObjectResponse = s3Client.putObject(request, RequestBody.fromBytes(bytes));
            log.info("s3上传结果：putObjectResponse:{}", JSON.toJSON(putObjectResponse));
        } catch (IOException e) {
            log.error("upload -> {}", e.getMessage(), e);
        }
    }

    public String upload(byte[] imageBytes, String ossObjKey) {
        try {
            if (s3Client == null) {
                s3Client = getS3Client(ossConfig.getAccessKey(), ossConfig.getSecretKey()
                        , ossConfig.getEndpoint(), ossConfig.getRegion());
            }
            String contentType = ContentTypeUtil.getMimeType(ossObjKey);
            // 上传 Object
            PutObjectRequest request = PutObjectRequest.builder()
                    .contentType(contentType)
                    .bucket(ossConfig.getBucketDefaultName())
                    .key(ossObjKey) // 目标S3上的路径和文件名
                    .acl(ObjectCannedACL.PUBLIC_READ)
                    .build();
            s3Client.putObject(request, RequestBody.fromBytes(imageBytes));
            String s = ossConfig.getReturnPoint() + ossConfig.getBucketDefaultName() + "/" + ossObjKey;
            log.info("upload -> {}", s);
            return s;
        } catch (Exception e) {
            log.error("upload -> {}", e.getMessage(), e);
            throw new RuntimeException("上传失败");
        }
    }


    public String copyObject(String ossObjKeyOld, String ossObjKeyNew) {
        try {
            if (s3Client == null) {
                s3Client = getS3Client(ossConfig.getAccessKey(), ossConfig.getSecretKey()
                        , ossConfig.getEndpoint(), ossConfig.getRegion());
            }
            String contentType = ContentTypeUtil.getMimeType(ossObjKeyOld);
            // 上传 Object
            CopyObjectRequest request = CopyObjectRequest.builder()
                    .sourceBucket(ossConfig.getBucketDefaultName())
                    .sourceKey(ossObjKeyOld)
                    .destinationBucket(ossConfig.getBucketDefaultName())
                    .destinationKey(ossObjKeyNew)
                    .contentType(contentType)
                    .acl(ObjectCannedACL.PUBLIC_READ)
                    .build();
            s3Client.copyObject(request);
            return ossConfig.getReturnPoint() + ossConfig.getBucketDefaultName() + "/" + ossObjKeyNew;
        } catch (Exception e) {
            log.error("upload -> {}", e.getMessage(), e);
            throw new RuntimeException("上传失败");
        }
    }


    public S3Client getS3Client(String accessKey, String secretKey, String endPoint, String region) {
        try {
            return S3Client.builder()
                    .endpointOverride(new URI(endPoint))
                    .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey)))
                    .serviceConfiguration(S3Configuration.Builder::pathStyleAccessEnabled)
                    .forcePathStyle(true)
                    .region(Region.of(region))
                    .build();
        } catch (URISyntaxException e) {
            log.error("getS3Client -> {}", e.getMessage(), e);
            throw new RuntimeException("创建云存链接失败！");
        }
    }

    @Data
    public static class S3Rule {
        private String id;
        private String prefix;
        private String status;
        private int expirationInDays = -1;
    }

    public void deleteObject(String ossObjKey) {
        try {
            if (s3Client == null) {
                s3Client = getS3Client(ossConfig.getAccessKey(), ossConfig.getSecretKey()
                        , ossConfig.getEndpoint(), ossConfig.getRegion());
            }
            // 上传 Object
            DeleteObjectRequest request = DeleteObjectRequest.builder()
                    .bucket(ossConfig.getBucketDefaultName())
                    .key(ossObjKey) // 目标S3上的路径和文件名
                    .build();
            s3Client.deleteObject(request);
        } catch (Exception e) {
            log.error("deleteObject -> {}", e.getMessage(), e);
            throw new RuntimeException("deleteObject失败");
        }

    }


    public void setBucketLifecycleConfiguration(S3Rule s3Rule) {
        if (s3Client == null) {
            s3Client = getS3Client(ossConfig.getAccessKey(), ossConfig.getSecretKey()
                    , ossConfig.getEndpoint(), ossConfig.getRegion());
        }
        Map<String, LifecycleRule> rules = new HashMap<>();
        try {
            GetBucketLifecycleConfigurationRequest lifecycleRequest = GetBucketLifecycleConfigurationRequest.builder()
                    .bucket(ossConfig.getBucketDefaultName())  // 填写存储桶的名称
                    .build();
            GetBucketLifecycleConfigurationResponse lifecycleResponse = s3Client.getBucketLifecycleConfiguration(lifecycleRequest);
            log.info("获取桶的生命周期 ossBean:{} getBucketLifecycleConfiguration -> {}", ossConfig, JSON.toJSON(lifecycleResponse));
            if (!lifecycleResponse.rules().isEmpty()) {
                lifecycleResponse.rules().forEach(rule -> {
                    rules.put(rule.filter().prefix(), rule);
                });
            }
        } catch (Exception e) {
            log.info("获取桶的生命周期 ossBean:{} getBucketLifecycleConfiguration -> {}", ossConfig, e.getMessage());
        }
        LifecycleRule rule = LifecycleRule.builder()
                .id(s3Rule.getId())
                .filter(LifecycleRuleFilter.fromPrefix(s3Rule.getPrefix()))
                .status("Enabled")
                .expiration(LifecycleExpiration.builder()
                        .days(s3Rule.getExpirationInDays())
                        .build())
                .build();

        rules.put(s3Rule.getPrefix(), rule);

        BucketLifecycleConfiguration configuration1 = BucketLifecycleConfiguration.builder()
                .rules(rules.values())
                .build();
        PutBucketLifecycleConfigurationRequest request = PutBucketLifecycleConfigurationRequest.builder()
                .bucket(ossConfig.getBucketDefaultName())
                .lifecycleConfiguration(configuration1)
                .build();
        try {
            log.info("设置生命周期 req:{} request:{}", s3Rule, JSON.toJSONString(request));
            PutBucketLifecycleConfigurationResponse putBucketLifecycleConfigurationResponse = s3Client.putBucketLifecycleConfiguration(request);
            log.info("设置生命周期 req:{} putBucketLifecycleConfigurationResponse: {}", s3Rule, JSON.toJSONString(putBucketLifecycleConfigurationResponse.sdkHttpResponse()));
        } catch (Exception e) {
            log.error("设置生命周期失敗 req:{} putBucketLifecycleConfigurationResponse: {}", s3Rule, e.getMessage(), e);
        }

        try {
            PutBucketCorsRequest bucketCorsRequest = PutBucketCorsRequest
                    .builder()
                    .corsConfiguration(
                            CORSConfiguration.builder()
                                    .corsRules(
                                            CORSRule.builder()
                                                    .allowedOrigins(Collections.singletonList("*"))
                                                    .allowedMethods(Arrays.asList("GET", "PUT", "POST", "DELETE", "HEAD"))
                                                    .allowedHeaders(Collections.singletonList("*"))
                                                    .maxAgeSeconds(3000)
                                                    .build())
                                    .build())
                    .bucket(ossConfig.getBucketDefaultName())
                    .build();
            PutBucketCorsResponse putBucketCorsResponse = s3Client.putBucketCors(bucketCorsRequest);
            log.info("设置桶的Cors req:{} putBucketCorsResponse: {}", s3Rule, putBucketCorsResponse);
        } catch (Exception e) {
            log.error("设置桶的Cors req:{} putBucketCorsResponse -> {}", s3Rule, e.getMessage());
        }
    }

    /**
     * 重试下载文件
     */
    public byte[] downloadFileAsByteArrayByCount(String fileUrl, int count) {
        for (int i = 0; i < count; i++) {
            byte[] bytes = downloadFileAsByteArray(fileUrl);
            if (bytes != null) {
                return bytes;
            }
        }
        return null;
    }


    public byte[] downloadFileAsByteArray(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            return null;
        }
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        InputStream is = null;
        BufferedInputStream bis = null;
        try {
            URL url = new URL(fileUrl);// 使用 URLConnection 并设置超时时间
            URLConnection connection = url.openConnection();
            // 设置连接超时（单位：毫秒）
            connection.setConnectTimeout(5 * 1000);
            // 设置读取超时（单位：毫秒）
            connection.setReadTimeout(5 * 1000);
            is = connection.getInputStream();
            bis = new BufferedInputStream(is);

            byte[] data = new byte[1024];
            int count;
            while ((count = bis.read(data, 0, 1024)) != -1) {
                buffer.write(data, 0, count);
            }
            return buffer.toByteArray();
        } catch (IOException e) {
            log.error("下载文件失败 rul:{},msg:{}", fileUrl, e.getMessage());
            return null;
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    log.error("Error closing BufferedInputStream", e);
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream", e);
                }
            }
            try {
                buffer.close();
            } catch (IOException e) {
                log.error("Error closing ByteArrayOutputStream", e);
            }
        }
    }

}

