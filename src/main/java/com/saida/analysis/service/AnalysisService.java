package com.saida.analysis.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.analysis.config.FuAnAlgConfig;
import com.saida.analysis.config.SaiDaAlgConfig;
import com.saida.analysis.dingding.DingMsgUtil;
import com.saida.analysis.dto.*;
import com.saida.analysis.grpcSnapServerTask.GrpcSnapServerChannelConfig;
import com.saida.analysis.mq.message.AlarmMessage;
import com.saida.analysis.mq.message.DevicePreSetJumpMessage;
import com.saida.analysis.mq.vlinker.VLinkerMqTemplate;
import com.saida.analysis.mqListener.RockerMqTopic;
import com.saida.analysis.mqListener.VideoStreamMessageListener;
import com.saida.analysis.util.IllegalFishingDetector;
import com.saida.analysis.util.OverlayFilteringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AnalysisService {
    @Resource
    private VLinkerMqTemplate vLinkerMqTemplate;

    //设备的预置点位状态 key: 设备id+通道id
    @Getter
    private final static ConcurrentHashMap<Long, DevicePreSetJumpMessage> devicePre = new ConcurrentHashMap<>();

    //如果预置点跳转  强制设置上次告警时间 + 转动时间
    //上一次任务的告警时间
    // key : 任务id
    @Getter
    private final static ConcurrentHashMap<Long, Long> lastTaskAlarmMap = new ConcurrentHashMap<>();
    //分析的线程
    private static ConcurrentHashMap<Long, Thread> taskThreads = new ConcurrentHashMap<>();
    //分析结果队列
    private static ConcurrentHashMap<Long, BlockingQueue<AnalysisDto>> taskQueues = new ConcurrentHashMap<>();

    @Resource
    private DingMsgUtil dingMsgUtil;

    @Scheduled(cron = "0 0/5 * * * ? ")
    public void taskQueuesSize() {
        HashMap<Long, Integer> taskIdSizeMap = new HashMap<>();
        taskQueues.forEach((taskId, blockingQueue) -> {
            if (taskThreads.containsKey(taskId)) {
                int size = blockingQueue.size();
                if (size > 100) {
                    taskIdSizeMap.put(taskId, size);
                }
            } else {
                taskQueues.remove(taskId);
            }
        });
        if (!CollectionUtils.isEmpty(taskIdSizeMap)) {
            StringBuilder sb = new StringBuilder();
            taskIdSizeMap.forEach((taskId, size) -> {
                sb.append("任务 ").append(taskId).append(" 队列长度为 ").append(size).append("，");
            });
            dingMsgUtil.sendMsg(sb.toString());
        }
    }


    @PreDestroy
    public void shutdown() {
        // 清空队列
        taskQueues.values().forEach(Collection::clear);
        // 清空队列
        taskQueues.clear();
        taskQueues = null;
        // 中断所有任务线程
        taskThreads.values().forEach(Thread::interrupt);
        taskThreads.clear();
        taskThreads = null;
    }

    public void shutdownByDeviceId(Long deviceId) {
        //删掉缓存
        VideoStreamMessageListener.getDeviceTasksMap().remove(deviceId);
        //删掉缓存
        GrpcSnapServerChannelConfig.getDeviceFrameCounters().remove(deviceId);
        //关掉时间计划关闭的那个线程
        VideoStreamMessageListener.getTaskStopTimers().remove(deviceId);
        Thread thread = taskThreads.get(deviceId);
        if (thread != null) {
            thread.interrupt();
            taskThreads.remove(deviceId);
        }
        BlockingQueue<AnalysisDto> blockingQueue = taskQueues.get(deviceId);
        if (blockingQueue != null) {
            while (!blockingQueue.isEmpty()) {
                blockingQueue.poll();
            }
            taskQueues.remove(deviceId);
        }

        log.info("设备 {} 关闭 关闭队列、关闭分析线程、关闭任务计划线程、删除设备-任务对象缓存", deviceId);
    }


    /**
     * 分析任务
     */
    public void doAnalysis(AnalysisDto analysisDto) {
        TaskDto taskDto = analysisDto.getTaskDto();
        Long deviceId = taskDto.getDeviceId();
        // 获取对应taskId的队列，如果不存在则创建
        BlockingQueue<AnalysisDto> analysisDtos = taskQueues.computeIfAbsent(deviceId, k -> new LinkedBlockingQueue<>());
        // 为每个taskId创建并启动一个处理线程，如果该线程尚未创建
        Thread threadTemp = taskThreads.computeIfAbsent(deviceId, k -> {
            Thread thread = null;
            try {
                thread = new Thread(() -> processTaskQueue(k));
                thread.setName(deviceId + "-analysis-thread");
                thread.start();
                log.info("任务线程 创建线程成功 taskId:{}", deviceId);
            } catch (Exception e) {
                log.error("队列消费监控 创建线程失败 taskId:{}", deviceId, e);
                return null;
            }
            return thread;
        });
        if (threadTemp == null || threadTemp.isInterrupted()) {
            taskThreads.remove(deviceId);
            log.error("任务线程 threadTemp为null 或者已结束 taskId:{}", deviceId);
            return;
        }
        boolean shouldReturn = true;
        for (DcTaskDispatchDto dcTaskDispatch : taskDto.getDcTaskDispatchMap().values()) {
            Integer alarmDuration = 1;
            if (taskDto.getAlgorithmParamConfigDto() != null) {
                alarmDuration = taskDto.getAlgorithmParamConfigDto().getAlarmDuration();
            }
            // 如果没有找到该任务的最后告警时间，跳过当前任务的告警判断
            if (lastTaskAlarmMap.containsKey(dcTaskDispatch.getId())) {
                long currentTime = analysisDto.getTimestamp() - lastTaskAlarmMap.get(dcTaskDispatch.getId());
                if (currentTime >= (alarmDuration * 1000)) {
                    shouldReturn = false;  // 至少有一个任务达到了告警时间
                    break;  // 不再继续遍历，跳出循环
                } else {
                    log.info("doAnalysis deviceId:{} 任务 {} 告警时间未达到[{},{}]，跳过当前处理", deviceId, dcTaskDispatch.getId(), currentTime, alarmDuration);
                }
            } else {
                // 如果当前是首次告警，记录任务的当前时间
                // 注意这里不会影响后续任务的识别过程，避免在此处直接修改状态
                shouldReturn = false;  // 只要有一个任务开始告警，应该继续后续处理
            }
        }
        if (shouldReturn) {
            log.info("doAnalysis deviceId:{} 所有算法任务都未到达告警时间，跳过当前处理", deviceId);
            if (FileUtil.exist(analysisDto.getImgUrl())) {
                FileUtil.del(analysisDto.getImgUrl());
            }
            return;  // 如果所有任务都未达到告警时间，就跳过处理
        }
        if (analysisDtos.size() > 100) {
            log.info("doAnalysis 队列消费监控 deviceId：{} 队列已满，跳过当前任务", deviceId);
            if (FileUtil.exist(analysisDto.getImgUrl())) {
                FileUtil.del(analysisDto.getImgUrl());
            }
            return;
        }
        analysisDtos.add(analysisDto);
        log.info("doAnalysis 队列消费监控 deviceId：{} 队列剩余数量：{}", deviceId, analysisDtos.size());
    }

    @Value("${grpc2.server.outputPath:}")
    private String outputPath;

    private void processTaskQueue(Long taskId) {
        try {
            while (!Thread.currentThread().isInterrupted()) {
                if (!taskQueues.containsKey(taskId)) {
                    return;
                }
                // 从队列中获取并处理分析结果
                AnalysisDto analysisDto = taskQueues.get(taskId).take();
                long l = System.currentTimeMillis();
                MDC.put("uuid", IdWorker.get32UUID());
                doAnalysisThread(analysisDto);
                log.info("processTaskQueue 队列消费监控 处理任务：{} 开始时间:{},抽帧时间:{}, 耗时：{}ms",
                        taskId, l, analysisDto.getTimestamp(), System.currentTimeMillis() - l);
            }
        } catch (Exception e) {
            log.error("processTaskQueue 队列消费监控 任务 {} 线程中断", taskId, e);
            String outputPathTemp = outputPath;
            if (outputPathTemp.endsWith("/")) {
                outputPathTemp = outputPathTemp + taskId;
            } else {
                outputPathTemp = outputPathTemp + "/" + taskId;
            }
            if (FileUtil.exist(outputPathTemp)) {
                FileUtil.del(outputPathTemp);
            }
            taskThreads.remove(taskId);
            taskQueues.remove(taskId);
            Thread.currentThread().interrupt();
        }
    }

    // 定义正则表达式，匹配最后的数字
    private static final Pattern pattern = Pattern.compile("(\\d+)-(\\d+)_(\\d+)\\.w=(\\d+)\\.h=(\\d+)\\.jpeg$");

    @Value("${vLinker.algDrawUrl:''}")
    private String algDrawUrl;

    /**
     * N_DET_HGE
     * 暂存上一张抽帧图片
     */
    private static final Map<Long, byte[]> lastTaskImgMap = new ConcurrentHashMap<>();

    public void doAnalysisThread(AnalysisDto analysisDto) {
        long startTime = System.currentTimeMillis();
        analysisDto.setDoAnalysisThreadStartTime(startTime);
        TaskDto taskDto = analysisDto.getTaskDto();
        Long deviceId = taskDto.getDeviceId();
        log.info("doAnalysisThread 开始任务 deviceId:{} imgUrl:{}", deviceId, analysisDto.getImgUrl());
        if (!FileUtil.exist(analysisDto.getImgUrl())) {
            log.error("doAnalysisThread 文件不存在了？？？任务 deviceId:{} imgUrl:{}", deviceId,analysisDto.getImgUrl());
            return;
        }
        byte[] imageBytes = FileUtil.readBytes(analysisDto.getImgUrl());
        if (imageBytes == null) {
            log.error("doAnalysisThread imageBytes 为空  不开始任务 deviceId:{}", deviceId);
            return;
        }
        analysisDto.setImageBytes(imageBytes);
        try {
            boolean del = FileUtil.del(analysisDto.getImgUrl());
            if (!del) {
                log.error("doAnalysisThread 文件删除失败  deviceId:{} imgUrl:{}", deviceId, analysisDto.getImgUrl());
            }
        } catch (Exception e) {
            log.error("doAnalysisThread 文件删除失败 而且出现了异常  deviceId:{} imgUrl:{}", deviceId, analysisDto.getImgUrl(), e);
        }
        // 如果所有算法任务都没有达到告警时间就返回，不再继续调用
        // 需要启动算法的任务
        List<DcTaskDispatchDto> taskTempList = new ArrayList<>();
        for (DcTaskDispatchDto dcTaskDispatch : taskDto.getDcTaskDispatchMap().values()) {
            Integer alarmDuration = 1;
            if (taskDto.getAlgorithmParamConfigDto() != null) {
                alarmDuration = taskDto.getAlgorithmParamConfigDto().getAlarmDuration();
            }
            // 如果没有找到该任务的最后告警时间，跳过当前任务的告警判断
            if (lastTaskAlarmMap.containsKey(dcTaskDispatch.getId())) {
                Long lastAlarmByDeviceId = lastTaskAlarmMap.get(dcTaskDispatch.getId());
                long currentTime = analysisDto.getTimestamp() - lastAlarmByDeviceId;
                if (currentTime >= (alarmDuration * 1000)) {
                    taskTempList.add(dcTaskDispatch);
                } else {
                    log.info("doAnalysisThread 队列消费监控 deviceId：{} 任务：{} 没有达到告警时间，跳过当前任务 抽帧时间:{} 上次告警时间;{} 间隔:{} 任务配置的间隔:{}",
                            deviceId, dcTaskDispatch.getId(), analysisDto.getTimestamp(), lastAlarmByDeviceId, currentTime, (alarmDuration));
                }
            } else {
                // 如果当前是首次告警，记录任务的当前时间
                // 注意这里不会影响后续任务的识别过程，避免在此处直接修改状态
                taskTempList.add(dcTaskDispatch);
            }
        }
        log.info("doAnalysisThread 队列消费监控 deviceId：{} taskDto.getDcTaskDispatchMap({})->{}",
                deviceId, taskDto.getDcTaskDispatchMap().size(), taskTempList.size());
        // 定义正则表达式，匹配最后的数字
        int w = 0;
        int h = 0;
        Matcher matcher = pattern.matcher(analysisDto.getImgUrl());
        try {
            if (matcher.find()) {
                String number = matcher.group(3);
                int keyIndex = Integer.parseInt(number);
                w = Integer.parseInt(matcher.group(4));
                h = Integer.parseInt(matcher.group(5));
                taskTempList = taskTempList.stream().filter(dcTaskDispatch -> {
                    AlgorithmParamConfigDto config = taskDto.getAlgorithmParamConfigDtoMap().get(dcTaskDispatch.getId());
                    if (config.getKeyFrameOnly()) {
                        return true;
                    }
                    // (frameIndex % (skip + extract)) + 1 <= extract
                    boolean b = (keyIndex % (config.getDefaultSkipFrameRate() + config.getDefaultFrameRate())) + 1 <= config.getDefaultFrameRate();
                    log.info("doAnalysisThread 队列消费监控 deviceId：{} 跳帧 跳帧数:{} 抽帧数:{} keyIndex:{} 结果:{}",
                            deviceId, config.getDefaultSkipFrameRate(), config.getDefaultFrameRate(), keyIndex, b);
                    return b;
//                    return keyIndex %
//                            ((config.getDefaultFrameExtractionTime() * 25 - config.getDefaultFrameRate()) + config.getDefaultFrameRate())
//                            < config.getDefaultFrameRate();
                }).collect(Collectors.toList());
            }
        } catch (NumberFormatException e) {
            log.error("doAnalysisThread 文件名格式不正确 matcher.find()error  deviceId:{} imgUrl:{}", deviceId, analysisDto.getImgUrl());
            return;
        }
        if (w == 0 || h == 0) {
            log.error("doAnalysisThread 文件名格式不正确 w == 0 || h == 0  deviceId:{} imgUrl:{}", deviceId, analysisDto.getImgUrl());
            return;
        }
        analysisDto.setW(w);
        analysisDto.setH(h);
        if (taskTempList.isEmpty()) {
            log.info("doAnalysisThread deviceId:{} 所有算法任务都未到达告警时间，跳过当前处理 taskTempList是空的", deviceId);
            return;  // 如果所有任务都未达到告警时间，就跳过处理
        }
        // 需要启动算法的任务 key 算法类型 1 saida算法（grpc） 2 赋安算法 http

        Map<Integer, List<DcTaskDispatchDto>> algSendMap = new HashMap<>();
        for (DcTaskDispatchDto dcTaskDispatch : taskTempList) {
            String algorithmCode = dcTaskDispatch.getAlgorithmCode();
            // 构建 gRPC 请求
            try {
                if (algorithmCode.startsWith("fuan")) {
                    algSendMap.computeIfAbsent(2, key -> new ArrayList<>()).add(dcTaskDispatch);
                } else {
                    algSendMap.computeIfAbsent(1, key -> new ArrayList<>()).add(dcTaskDispatch);
                }
            } catch (Exception e) {
                log.error("doAnalysisThread 算法名称 {} 不存在  不开始任务 deviceId:{}", algorithmCode, deviceId);
            }
        }
        if (algSendMap.isEmpty()) {
            log.error("doAnalysisThread 算法名称 algSendMap为空  不开始任务 deviceId:{}", deviceId);
            return;
        }

        if (algSendMap.containsKey(1)) {
            doSaidaAlgFunV2(analysisDto, algSendMap.getOrDefault(1, new ArrayList<>()));
        } else if (algSendMap.containsKey(2)) {
            doFuAnAlgFun(analysisDto, algSendMap.getOrDefault(2, new ArrayList<>()));
        }
    }

    @Autowired(required = false)
    private FuAnAlgConfig fuAnAlgConfig;

    // 创建 RestTemplate 实例
    private static final RestTemplate fuanRestTemplate = createRestTemplate(5000, 5000);


    private static final HttpHeaders headers = new HttpHeaders();

    static {
        headers.set("Content-Type", "application/json");
    }

    private void doFuAnAlgFun(AnalysisDto analysisDto, List<DcTaskDispatchDto> algorithList) {
        if (fuAnAlgConfig == null) {
            log.error("doFuAnAlgFun fuAnAlgConfig 为空  不开始任务 deviceId:{}", analysisDto.getTaskDto().getDeviceId());
            return;
        }
        Long deviceId = analysisDto.getTaskDto().getDeviceId();
        for (DcTaskDispatchDto dcTaskDispatch : algorithList) {
            List<String> algMinList = new ArrayList<>();
            if (StringUtils.isNotBlank(dcTaskDispatch.getAlgorithmMinCode())) {
                for (String string : dcTaskDispatch.getAlgorithmMinCode().split(",")) {
                    algMinList.add(string.replace("fuan-", ""));
                }
            }
            if (algMinList.isEmpty()) {
                log.error("doFuAnAlgFun 算法名称 为空  不开始任务 deviceId:{}", deviceId);
                return;
            }
            String timePrefix = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd/HH/mm");
            String ossObjKey = fuAnAlgConfig.getCloudStoragePrefix() + "/" + timePrefix + "/" + IdWorker.getId() + ".jpg";
            String imageUrl = fileService.upload(analysisDto.getImageBytes(), ossObjKey);
            JSONObject extra = new JSONObject();
            extra.put("ossObjKey", ossObjKey);
            //减少一些http网络请求内存
            analysisDto.setImageBytes(new byte[]{});
            analysisDto.setTaskDto(null);
            extra.put("analysisDto", analysisDto);
            extra.put("deviceId", deviceId);
            extra.put("taskId", dcTaskDispatch.getId());
            extra.put("imageUrl", imageUrl);
            List<FuAnRequest.AiAnalyzeAreasDto> areasDtoList = new ArrayList<>();
            areasDtoList.add(FuAnRequest.AiAnalyzeAreasDto
                    .builder()
                    .polygonId("-1")
                    .analyzeTypes(algMinList)
                    .build());
            String replace = dcTaskDispatch.getAlgorithmCode().replace("fuan-", "");
            FuAnRequest fuAnRequest = FuAnRequest
                    .builder()
                    .picUrl(imageUrl)
                    .algorithmType(replace)
                    .extra(extra.toJSONString())
                    .httpCallbackUrl(fuAnAlgConfig.getCallbackAddress() + fuAnAlgConfig.getCallbackFun())
                    .aiAnalyzeAreas(areasDtoList)
                    .build();
            // 设置请求头
            // 创建请求体，这里是一个 JSON 字符串或一个对象
            String requestBody = JSON.toJSONString(fuAnRequest);
            // 创建 HttpEntity，封装请求头和请求体
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            // 发起 POST 请求
            ResponseEntity<String> response = fuanRestTemplate
                    .exchange(fuAnAlgConfig.getReqAddress() + fuAnAlgConfig.getSubmitAITaskUri(),
                            HttpMethod.POST, entity, String.class);
            // 获取响应状态码
            String resStr = "";
            int statusCode = response.getStatusCodeValue();
            if (statusCode == 200) {
                resStr = response.getBody();
                JSONObject jsonObject = JSONObject.parseObject(resStr);
                if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 0) {
                    //成功发送
                    log.info("doFuAnAlgFun fuAnAlgFun 发送成功 deviceId:{} requestBody:{},resStr:{}", deviceId, requestBody, resStr);
                    return;
                }
            }
            log.info("doFuAnAlgFun fuAnAlgFun 发送失败！ deviceId:{} requestBody:{},resStr:{}", deviceId, requestBody, resStr);
            fileService.deleteObject(ossObjKey);
        }
    }

    @Autowired(required = false)
    private SaiDaAlgConfig saiDaAlgConfig;

    // 创建 RestTemplate，并配置超时
    public static RestTemplate createRestTemplate(int connectTimeout, int readTimeout) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout); // 连接超时（毫秒）
        factory.setReadTimeout(readTimeout);       // 数据读取超时（毫秒）
        return new RestTemplate(factory);
    }

    private static final RestTemplate saidaRestTemplate = createRestTemplate(2000, 2000);

    /**
     * 赛达v2
     */
    private void doSaidaAlgFunV2(AnalysisDto analysisDto, List<DcTaskDispatchDto> taskTempList) {
        String picUrl = Base64.getEncoder().encodeToString(analysisDto.getImageBytes());
        String timePrefix = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd/HH/mm");
        log.info("doSaidaAlgFunV2 开始识别 deviceId:{} taskTempList.size:{} imgUrl:{}", analysisDto.getTaskDto().getDeviceId(), taskTempList.size(), analysisDto.getImgUrl());
        String imageUrl = null;
        for (DcTaskDispatchDto dcTaskDispatch : taskTempList) {
            String alg_code = dcTaskDispatch.getAlgorithmCode();
            if ("N_DET_CHANGE".equals(dcTaskDispatch.getAlgorithmCode())) {
                log.info("doSaidaAlgFunV2 N_DET_CHANGE 记录本次图片 并且设置上次图片");
                analysisDto.setLastImageBytes(lastTaskImgMap.get(analysisDto.getTaskDto().getDeviceId()));
                lastTaskImgMap.put(analysisDto.getTaskDto().getDeviceId(), analysisDto.getImageBytes());
            }
            SaidaRequest saidaRequest = SaidaRequest.builder()
                    .ipc_code(String.valueOf(analysisDto.getTaskDto().getDeviceId()))
                    .algo_code(alg_code)
                    .pic_url(picUrl)
                    .regions(dcTaskDispatch.getRegions(analysisDto.getW(), analysisDto.getH()))
                    .build();
            log.info("doSaidaAlgFunV2 开始识别 deviceId:{} alg:{} imgUrl:{}", analysisDto.getTaskDto().getDeviceId(), alg_code, analysisDto.getImgUrl());
            long algorithmsStartTime = System.currentTimeMillis();
            SaidaRepose saidaRepose = null;
            // 获取或创建异步 gRPC 通道
            try {
                // 创建请求体，这里是一个 JSON 字符串或一个对象
                String requestBody = JSON.toJSONString(saidaRequest);
                // 创建 HttpEntity，封装请求头和请求体
                HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
                // 发起 POST 请求
                ResponseEntity<String> response = saidaRestTemplate
                        .exchange(saiDaAlgConfig.getReqAddress(),
                                HttpMethod.POST, entity, String.class);
                // 获取响应状态码
                int statusCode = response.getStatusCodeValue();
                if (statusCode == 200) {
                    String resStr = "";
                    resStr = response.getBody();
                    if (StringUtils.isNotBlank(resStr)) {
                        saidaRepose = JSON.parseObject(resStr, SaidaRepose.class);
                    }
                    log.info("doSaidaAlgFunV2 算法分析成功 deviceId:{} alg:{} ,msg:{}", analysisDto.getTaskDto().getDeviceId(), alg_code, resStr);
                }
            } catch (Exception e) {
                log.info("doSaidaAlgFunV2 算法分析失败 出现特殊的异常 deviceId:{} alg:{} ,msg:{}", analysisDto.getTaskDto().getDeviceId(), alg_code, e.getMessage(), e);
                continue;
            }
            long algorithmsResultTime = System.currentTimeMillis();
            analysisDto.setAlgorithmsResultTime(algorithmsResultTime);
            if (saidaRepose == null) {
                log.info("doSaidaAlgFunV2 算法分析失败 deviceId:{} ,msg:saidaRepose is null , alg:{}", analysisDto.getTaskDto().getDeviceId(), alg_code);
                continue;
            }
            if (saidaRepose.getCode() != 0) {
                log.info("doSaidaAlgFunV2 算法分析失败 deviceId:{} alg:{} ,msg:{} ", analysisDto.getTaskDto().getDeviceId(), alg_code, saidaRepose.getMsg());
                continue;
            }
            if (saidaRepose.getData() == null || saidaRepose.getData().isEmpty()) {
                log.info("doSaidaAlgFunV2 算法分析失败 getData是空的 deviceId:{} alg:{},msg:{}", analysisDto.getTaskDto().getDeviceId(), alg_code, saidaRepose.getMsg());
                continue;
            }
            List<GrpcAlgorithmResultDto> algorithmsResult = new ArrayList<>();
            List<String> split = null;
            if (StringUtils.isNotBlank(dcTaskDispatch.getAlgorithmMinCode())) {
                split = Arrays.stream(dcTaskDispatch.getAlgorithmMinCode().split(",")).collect(Collectors.toList());
            }
            for (SaidaRepose.DataDto dataDto : saidaRepose.getData()) {
                GrpcAlgorithmResultDto grpcAlgorithmResultDto = new GrpcAlgorithmResultDto();
                if (split != null && !split.isEmpty() && split.stream().noneMatch(s -> s.equals(String.valueOf(dataDto.getType_id())))) {
                    continue;
                }
                grpcAlgorithmResultDto.setLabel(dataDto.getType_id());
                grpcAlgorithmResultDto.setProb(dataDto.getProb());
                grpcAlgorithmResultDto.setMinx(dataDto.getRect().getX().intValue());
                grpcAlgorithmResultDto.setMaxx(dataDto.getRect().getX().intValue() + dataDto.getRect().getW().intValue());
                grpcAlgorithmResultDto.setMiny(dataDto.getRect().getY().intValue());
                grpcAlgorithmResultDto.setMaxy(dataDto.getRect().getY().intValue() + dataDto.getRect().getH().intValue());
                algorithmsResult.add(grpcAlgorithmResultDto);
            }
            if (algorithmsResult.isEmpty()) {
                log.info("doSaidaAlgFunV2 算法分析失败 可能是被小类过滤了 deviceId:{} alg:{} ,msg:{} ", analysisDto.getTaskDto().getDeviceId(), alg_code, "没有识别到目标");
                continue;
            }
            log.info("doAnalysisThread 算法分析成功 deviceId:{} alg:{},{}个结果返回 耗时：{}", analysisDto.getTaskDto().getDeviceId(), alg_code, saidaRepose.getData().size(),
                    algorithmsResultTime - algorithmsStartTime);
            if (StringUtils.isBlank(imageUrl)) {
                imageUrl = fileService.upload(analysisDto.getImageBytes(), "alarm/t3/" + timePrefix + "/" + IdWorker.getId() + ".jpg");
                analysisDto.setImageUrl(imageUrl);
            }
            analysisDto.setAlgorithmsResult(algorithmsResult);
            List<GrpcAlgorithmResultDto> filteredResults;
            if ("SD_ECN_11020001".equals(alg_code)){
                // 离岗检测不会有数据
                log.info("doSaidaAlgFunV2 离岗检测触发 deviceId:{} alg:{} ,msg:{} ", analysisDto.getTaskDto().getDeviceId(), alg_code, "离岗检测触发");
                filteredResults = algorithmsResult;
            }else {
                filteredResults = calculationResults(analysisDto, dcTaskDispatch.getId());
            }
            if (filteredResults.isEmpty()) {
                log.info("doSaidaAlgFunV2 告警结果为空 deviceId:{} alg:{} ,msg:{} ", analysisDto.getTaskDto().getDeviceId(), alg_code, "没有识别到目标");
            } else {
                alarmMessageToMq(analysisDto, dcTaskDispatch.getId(), filteredResults);
            }
        }
    }

    /**
     * 计算分析后的结果 是否告警
     */
    public static List<GrpcAlgorithmResultDto> calculationResults(AnalysisDto analysisDto, Long taskId) {
        List<GrpcAlgorithmResultDto> algorithmsResult = new ArrayList<>(analysisDto.getAlgorithmsResult());
        // 抽帧时间
        if (analysisDto.getTimestamp() < 1 || algorithmsResult.isEmpty()) {
            log.error("calculationResults 分析服务-参数异常，extractionFrameTime：{}, base64Image_isEmpty:{}, algorithmsResultArr_isEmpty:{}"
                    , analysisDto.getTimestamp(), false, algorithmsResult.isEmpty());
            return new ArrayList<>();
        }
        //过滤信任度的数据
        algorithmsResult = algorithmsResult.stream()
                .filter(e -> Double.compare(e.getProb() * 100, analysisDto.getTaskDto().getAlgorithmParamConfigDto().getSensitivity()) >= 0)
                .collect(Collectors.toList());
        if (algorithmsResult.isEmpty()) {
            log.info("calculationResults 队列消费监控 识别结果为空[因为置信度过滤结束] deviceId:{} algorithmsResultTime:{}", analysisDto.getTaskDto().getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        List<PresetPointSetDetailDto> presetPointSetDetailDtoList = analysisDto.getTaskDto().getPresetPointSetDetailDtoList().get(taskId);
        if (presetPointSetDetailDtoList == null || presetPointSetDetailDtoList.isEmpty()) {
            log.info("calculationResults 队列消费监控 电子围栏1为空[结束] deviceId:{} algorithmsResultTime:{}", analysisDto.getTaskDto().getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        // 电子围栏配置
        PresetPointSetDetailDto presetPointSetDetailDto = presetPointSetDetailDtoList.get(0);
        DevicePreSetJumpMessage devicePreSetJumpMessage = devicePre.get(analysisDto.getTaskDto().getDeviceId());
        if (devicePreSetJumpMessage != null) {
            Map<String, PresetPointSetDetailDto> collect = presetPointSetDetailDtoList.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getPresetPointId()))
                    .collect(Collectors.toMap(PresetPointSetDetailDto::getPresetPointId, Function.identity(), (v1, v2) -> v1));
            presetPointSetDetailDto = collect.get(String.valueOf(devicePreSetJumpMessage.getIndex()));
        }
        if (presetPointSetDetailDto == null) {
            log.info("calculationResults 队列消费监控 电子围栏2为空[结束] deviceId:{} algorithmsResultTime:{}", analysisDto.getTaskDto().getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        List<PresetPointSetDetailDto.DrawAreaDTO> drawAreaVOList = presetPointSetDetailDto.getDrawAreaVOList();
        if (CollectionUtils.isEmpty(drawAreaVOList)) {
            log.info("calculationResults 队列消费监控 电子围栏3为空[结束] deviceId:{} algorithmsResultTime:{}", analysisDto.getTaskDto().getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        List<GrpcAlgorithmResultDto> filteredResults = filterResults(algorithmsResult, drawAreaVOList, analysisDto.getW(), analysisDto.getH());
        if (filteredResults.isEmpty()) {
            log.info("calculationResults 队列消费监控 结果都不在框中[结束] deviceId:{} taskId:{} algorithmsResultTime:{}," +
                            ",algorithmsResult:{},drawAreaVOList:{}"
                    , analysisDto.getTaskDto().getDeviceId()
                    , taskId
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime()
                    , JSON.toJSONString(algorithmsResult), JSON.toJSONString(drawAreaVOList));
            return new ArrayList<>();
        }
        DcTaskDispatchDto dcTaskDispatch = analysisDto.getTaskDto().getDcTaskDispatchMap().get(taskId);
        if (dcTaskDispatch == null) {
            log.error("calculationResults 分析服务-参数异常，taskId：{} 拿不到任务对象", taskId);
            return new ArrayList<>();
        }
        //非法垂钓过滤
        if ("H_DET_ILLFISH".equals(dcTaskDispatch.getAlgorithmCode())) {
            filteredResults = IllegalFishingDetector.detectIllegalFishing(filteredResults, analysisDto.getW(), analysisDto.getH());
        }
        // 过滤叠框的数据
        filteredResults = OverlayFilteringUtil.filterResults(filteredResults);
        if (filteredResults.isEmpty()) {
            log.info("calculationResults 队列消费监控 H_DET_ILLFISH 处理后结果都不在框中[结束] deviceId:{} algorithmsResultTime:{},"
                    , analysisDto.getTaskDto().getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        return filteredResults;
    }

    public void alarmMessageToMq(AnalysisDto analysisDto, Long taskId, List<GrpcAlgorithmResultDto> filteredResults) {
        DcTaskDispatchDto dcTaskDispatch = analysisDto.getTaskDto().getDcTaskDispatchMap().get(taskId);
        if (dcTaskDispatch == null) {
            log.error("alarmMessageToMq 分析服务-参数异常，taskId：{} 拿不到任务对象", taskId);
            return;
        }
        long id = IdWorker.getId();
        long imageTime = System.currentTimeMillis();
        AlarmMessage alarmMessage = new AlarmMessage();
        alarmMessage.setTime(analysisDto.getTimestamp());
        alarmMessage.setAlgorithmCode(dcTaskDispatch.getAlgorithmCode());
        alarmMessage.setAlgorithmMinCode(dcTaskDispatch.getAlgorithmMinCode());
        alarmMessage.setAnalysisImage(algDrawUrl + "prob/" + id + ".jpg");
        if ("N_DET_CHANGE".equals(dcTaskDispatch.getAlgorithmCode())) {
            if (analysisDto.getLastImageBytes() != null && analysisDto.getLastImageBytes().length > 0) {
                log.info("alarmMessageToMq N_DET_CHANGE 算法，上传上一帧图片");
                String timePrefix = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd/HH/mm");
                String temp = fileService.upload(analysisDto.getLastImageBytes(), "alarm/t3/" + timePrefix + "/" + IdWorker.getId() + ".jpg");
                alarmMessage.setAnalysisImage(alarmMessage.getAnalysisImage() + "," + temp);
                log.info("当前帧: {} len:{}", System.identityHashCode(analysisDto.getImageBytes()), analysisDto.getImageBytes().length);
                log.info("上一帧: {} len:{}", System.identityHashCode(analysisDto.getLastImageBytes()), analysisDto.getLastImageBytes().length);
            } else {
                log.info("alarmMessageToMq N_DET_CHANGE 算法，上一帧图片为空");
                return;
            }
        }
        alarmMessage.setImage(analysisDto.getImageUrl());
        alarmMessage.setAnalysisImageNotProb(algDrawUrl + "notProb/" + id + ".jpg");
        alarmMessage.setHeight(analysisDto.getH());
        alarmMessage.setWidth(analysisDto.getW());
        alarmMessage.setDeviceId(analysisDto.getTaskDto().getDeviceId());
        alarmMessage.setAlgorithmsResult(filteredResults);
        alarmMessage.setTaskId(dcTaskDispatch.getId());
        alarmMessage.setMsgId(id);
        Long lastAlarm = lastTaskAlarmMap.get(dcTaskDispatch.getId());
        if (lastAlarm != null) {
            long currentTime = analysisDto.getTimestamp() - lastAlarm;
            int alarmDuration = 1;
            if (dcTaskDispatch.getAlgorithmParamConfigDto() != null) {
                alarmDuration = dcTaskDispatch.getAlgorithmParamConfigDto().getAlarmDuration();
            } else {
                log.error("alarmMessageToMq 队列消费监控 告警间隔异常，taskId：{} 拿不到任务对象 getAlgorithmParamConfigDto 为空", JSON.toJSONString(dcTaskDispatch));
            }
            if (currentTime < alarmDuration * 1000L) {
                log.info("alarmMessageToMq 队列消费监控 告警间隔小于{}秒[结束] algorithmsResultTime:{}"
                        , alarmDuration, analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
                return;
            }
        }

        vLinkerMqTemplate.asyncSendToQueue(RockerMqTopic.ANALYSIS_ALARM.getTopic(), alarmMessage);
        long endTime = System.currentTimeMillis();
        log.info("calculationResults 队列消费监控 deviceId:{} algorithmsResultTime:{}  ,imageTime:{},endTime:{}"
                , analysisDto.getTaskDto().getDeviceId()
                , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime()
                , imageTime - analysisDto.getDoAnalysisThreadStartTime()
                , endTime - analysisDto.getDoAnalysisThreadStartTime());
        lastTaskAlarmMap.put(dcTaskDispatch.getId(), analysisDto.getTimestamp());
    }

    @Resource
    private FileService fileService;

    public static void main(String[] args) {
//        System.out.println((6004 % (1500 + 1)) + 1 <= 1);
//        System.out.println((6005 % (1500 + 1)) + 1 <= 1);
//        System.out.println((7505 % (1500 + 1)) + 1 <= 1);
//        System.out.println((6004 % (1500 + 1)) >= 1500);
//        System.out.println((7505 % (1500 + 1)) >= 1500);
//        System.out.println((9006 % (1500 + 1)) >= 1500);

        // 1. 模拟任务ID列表
        List<Integer> taskTempList = Arrays.asList(6008, 6009, 6010, 7510, 9006, 10507, 12008);


        // 假设任务ID都用同一个配置
        AlgorithmParamConfigDto frameConfig = new AlgorithmParamConfigDto();
        frameConfig.setDefaultSkipFrameRate(1500);  // 跳过1500帧
        frameConfig.setDefaultFrameRate(2);         // 抽1帧
        frameConfig.setKeyFrameOnly(false);
        // 4. 过滤逻辑
        List<Integer> filteredList = taskTempList.stream()
                .filter(frameIndex -> {
                    if (frameConfig.getKeyFrameOnly()) {
                        return true;
                    }
                    return (frameIndex % (frameConfig.getDefaultSkipFrameRate() + frameConfig.getDefaultFrameRate())) + 1 <= frameConfig.getDefaultFrameRate();
                })
                .collect(Collectors.toList());
        // 5. 输出结果
        System.out.println("原始帧列表: " + taskTempList);
        System.out.println("保留帧列表: " + filteredList);
//        String resp = "{\"code\":0,\"msg\":\"\",\"data\":[{\"rect\":{\"x\":1374.3197021484375,\"y\":579.15533447265625,\"w\":228.9014892578125,\"h\":161.15875244140625},\"type_id\":8,\"prob\":0.93023878335952759},{\"rect\":{\"x\":873.297607421875,\"y\":655.163818359375,\"w\":416.729248046875,\"h\":282.8558349609375},\"type_id\":8,\"prob\":0.92798793315887451},{\"rect\":{\"x\":1184.164306640625,\"y\":562.3187255859375,\"w\":260.7767333984375,\"h\":153.6810302734375},\"type_id\":8,\"prob\":0.89925146102905273},{\"rect\":{\"x\":924.2794189453125,\"y\":519.747802734375,\"w\":273.5592041015625,\"h\":142.2525634765625},\"type_id\":8,\"prob\":0.895329773426056},{\"rect\":{\"x\":1688.05712890625,\"y\":713.466064453125,\"w\":231.1136474609375,\"h\":181.92529296875},\"type_id\":8,\"prob\":0.850289523601532},{\"rect\":{\"x\":1264.882080078125,\"y\":442.65948486328125,\"w\":201.86083984375,\"h\":75.10968017578125},\"type_id\":8,\"prob\":0.70266211032867432},{\"rect\":{\"x\":1260.7916259765625,\"y\":442.98638916015625,\"w\":258.44482421875,\"h\":93.12481689453125},\"type_id\":8,\"prob\":0.37324324250221252}]}";
//        List<GrpcAlgorithmResultDto> algorithmsResult = new ArrayList<>();
//
//        SaidaRepose saidaRepose = JSON.parseObject(resp, SaidaRepose.class);
//        for (SaidaRepose.DataDto dataDto : saidaRepose.getData()) {
//            GrpcAlgorithmResultDto grpcAlgorithmResultDto = new GrpcAlgorithmResultDto();
//            grpcAlgorithmResultDto.setLabel(dataDto.getType_id());
//            grpcAlgorithmResultDto.setProb(dataDto.getProb());
//            grpcAlgorithmResultDto.setMinx(dataDto.getRect().getX().intValue());
//            grpcAlgorithmResultDto.setMaxx(dataDto.getRect().getX().intValue() + dataDto.getRect().getW().intValue());
//            grpcAlgorithmResultDto.setMiny(dataDto.getRect().getY().intValue());
//            grpcAlgorithmResultDto.setMaxy(dataDto.getRect().getY().intValue() + dataDto.getRect().getH().intValue());
//            algorithmsResult.add(grpcAlgorithmResultDto);
//        }
//        String dra = "[{\"drawAreaVOList\":[{\"checkRule\":0,\"color\":\"#1990FF\",\"coordinate\":\"[{\\\"x\\\":0.37275064267352187,\\\"y\\\":0.12110825478434734},{\\\"x\\\":0.9897172236503856,\\\"y\\\":0.3496143958868895},{\\\"x\\\":0.9897172236503856,\\\"y\\\":0.9025992573550414},{\\\"x\\\":0.2095115681233933,\\\"y\\\":0.9025992573550414},{\\\"x\\\":0.11311053984575835,\\\"y\\\":0.37703513281919454},{\\\"x\\\":0.11311053984575835,\\\"y\\\":0.37475007140816907}]\",\"id\":1916321224281133058,\"name\":\"检测区0\",\"type\":0}]}]";
//        JSONArray objects = JSON.parseArray(dra);
//        JSONObject jsonObject1 = objects.getJSONObject(0);
//
//        List<PresetPointSetDetailDto.DrawAreaDTO> drawAreaVOList = jsonObject1.getJSONArray("drawAreaVOList").toJavaList(PresetPointSetDetailDto.DrawAreaDTO.class);
//        for (PresetPointSetDetailDto.DrawAreaDTO drawAreaDTO : drawAreaVOList) {
//            List<PresetPointSetDetailDto.CoordinateDto> coordinates = JSON.parseArray(drawAreaDTO.getCoordinate(), PresetPointSetDetailDto.CoordinateDto.class);
//            drawAreaDTO.setCoordinateList(coordinates);
//        }
//        List<GrpcAlgorithmResultDto> filteredResults = filterResults(algorithmsResult, drawAreaVOList, 1920, 1080);
//        System.out.println(filteredResults);
    }

    // 过滤检测结果
    public static List<GrpcAlgorithmResultDto> filterResults(
            List<GrpcAlgorithmResultDto> results,
            List<PresetPointSetDetailDto.DrawAreaDTO> drawAreas,
            int imageWidth, // 图片宽度
            int imageHeight // 图片高度
    ) {
        // 筛选出检测区、屏蔽区和拌线区
        List<PresetPointSetDetailDto.DrawAreaDTO> detectionAreas = drawAreas.stream()
                .filter(area -> area.getType() == 0) // 0 为检测区
                .collect(Collectors.toList());

        List<PresetPointSetDetailDto.DrawAreaDTO> shieldAreas = drawAreas.stream()
                .filter(area -> area.getType() == 1) // 1 为屏蔽区
                .collect(Collectors.toList());

        List<PresetPointSetDetailDto.DrawAreaDTO> banLineAreas = drawAreas.stream()
                .filter(area -> area.getType() == 2) // 2 为拌线
                .collect(Collectors.toList());

        // 单独对每个结果进行三类区域判断
        return results.stream()
                .filter(result -> {
                    boolean inDetection = isInPolygons(result, detectionAreas, imageWidth, imageHeight);
                    boolean inShield = isInPolygons(result, shieldAreas, imageWidth, imageHeight);
                    boolean inBanLine = isInBanLineRectangles(result, banLineAreas, imageWidth, imageHeight);

                    // 拌线区域不依赖前两者；但检测区/屏蔽区是互斥的
                    return inBanLine || (inDetection && !inShield);
                })
                .collect(Collectors.toList());
    }

    // 判断矩形是否在多边形内（处理检测区和屏蔽区）
    private static boolean isInPolygons(GrpcAlgorithmResultDto result, List<PresetPointSetDetailDto.DrawAreaDTO> areas, int imageWidth, int imageHeight) {
        for (PresetPointSetDetailDto.DrawAreaDTO area : areas) {
            // 获取检测规则
            Integer checkRule = area.getCheckRule();

            // 将坐标列表转换为int数组列表
            List<int[]> polygon = new ArrayList<>();
            for (PresetPointSetDetailDto.CoordinateDto point : area.getCoordinateList()) {
                int x = (int) (point.getX() * imageWidth);
                int y = (int) (point.getY() * imageHeight);
                polygon.add(new int[]{x, y});
            }

            // 矩形的四个顶点坐标
            int x1 = result.getMinx();
            int y1 = result.getMiny();
            int x2 = result.getMaxx();
            int y2 = result.getMaxy();

            if (checkRule == 0) {
                // 目标框和检测框有交集：判断矩形是否与检测框有交集
                if (isRectangleIntersecting(x1, y1, x2, y2, polygon)) {
                    return true;
                }
            } else if (checkRule == 1) {
                // 目标中心点在检测框内：判断矩形的中心点是否在多边形内
                double centerX = (x1 + x2) / 2.0; // 计算矩形的中心点X
                double centerY = (y1 + y2) / 2.0; // 计算矩形的中心点Y
                if (isPointInPolygon(centerX, centerY, polygon)) {
                    return true;
                }
            }
        }
        return false;
    }
    private static boolean isInBanLineRectangles(
            GrpcAlgorithmResultDto result,
            List<PresetPointSetDetailDto.DrawAreaDTO> banLineAreas,
            int imageWidth,
            int imageHeight
    ) {
        for (PresetPointSetDetailDto.DrawAreaDTO banLine : banLineAreas) {
            List<PresetPointSetDetailDto.CoordinateDto> pts = banLine.getCoordinateList();
            if (pts == null || pts.size() != 4) continue;

            // 四个点转为像素
            int[][] p = new int[4][2];
            for (int i = 0; i < 4; i++) {
                p[i][0] = (int) Math.round(pts.get(i).getX() * imageWidth);
                p[i][1] = (int) Math.round(pts.get(i).getY() * imageHeight);
            }

            // 宽方向：p0 -> p1
            double wx = (p[1][0] - p[0][0]) / 2.0;
            double wy = (p[1][1] - p[0][1]) / 2.0;

            // 高方向：p2 -> p3
            double hx = (p[3][0] - p[2][0]) / 2.0;
            double hy = (p[3][1] - p[2][1]) / 2.0;

            // 中心点 C
            double cx = (p[0][0] + p[1][0] + p[2][0] + p[3][0]) / 4.0;
            double cy = (p[0][1] + p[1][1] + p[2][1] + p[3][1]) / 4.0;

            // 构造四个角点（顺时针）
            double[][] polygon = new double[4][2];
            polygon[0][0] = cx - wx - hx;
            polygon[0][1] = cy - wy - hy;

            polygon[1][0] = cx + wx - hx;
            polygon[1][1] = cy + wy - hy;

            polygon[2][0] = cx + wx + hx;
            polygon[2][1] = cy + wy + hy;

            polygon[3][0] = cx - wx + hx;
            polygon[3][1] = cy - wy + hy;

            // 判断目标框的4个角是否至少一个在多边形内
            int x1 = result.getMinx(), y1 = result.getMiny();
            int x2 = result.getMaxx(), y2 = result.getMaxy();
            int[][] rectPts = new int[][]{
                    {x1, y1}, {x1, y2}, {x2, y1}, {x2, y2}
            };

            for (int[] pt : rectPts) {
                if (isPointInPolygon(pt[0], pt[1], polygon)) {
                    return true;
                }
            }
        }
        return false;
    }

    // 使用 double 点判断是否在多边形内（射线法）
    private static boolean isPointInPolygon(double x, double y, double[][] poly) {
        int n = poly.length;
        boolean inside = false;
        for (int i = 0, j = n - 1; i < n; j = i++) {
            double xi = poly[i][0], yi = poly[i][1];
            double xj = poly[j][0], yj = poly[j][1];

            boolean intersect = ((yi > y) != (yj > y)) &&
                    (x < (xj - xi) * (y - yi) / (yj - yi + 1e-8) + xi);
            if (intersect) inside = !inside;
        }
        return inside;
    }

    // 判断矩形是否与检测框有交集
    private static boolean isRectangleIntersecting(int x1, int y1, int x2, int y2, List<int[]> polygon) {
        // 矩形的四个角坐标
        int[][] rectPoints = {
                {x1, y1},
                {x1, y2},
                {x2, y1},
                {x2, y2}
        };

        // 矩形与多边形的交集判断
        for (int[] rectPoint : rectPoints) {
            if (isPointInPolygon(rectPoint[0], rectPoint[1], polygon)) {
                return true;
            }
        }
        return false;
    }

    // 判断一个点是否在多边形内
    private static boolean isPointInPolygon(double x, double y, List<int[]> polygon) {
        int n = polygon.size();
        boolean inside = false;
        for (int i = 0, j = n - 1; i < n; j = i++) {
            int[] pi = polygon.get(i);
            int[] pj = polygon.get(j);
            boolean intersect = (pi[1] > y) != (pj[1] > y) &&
                    (x < (pj[0] - pi[0]) * (y - pi[1]) / (pj[1] - pi[1]) + pi[0]);
            if (intersect) {
                inside = !inside;
            }
        }
        return inside;
    }

}
