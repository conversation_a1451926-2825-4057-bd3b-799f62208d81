package com.saida.analysis.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * {
 * "defaultFrameRate": "1",	//N帧
 * "defaultFrameExtractionTime": "10",	//N秒
 * "humNum": "1",  //聚集人数
 * "stayTime": "11",  //停留时长
 * "sensitivity": 50,  //灵敏度
 * "faultTolerantFrameRate": 50,  //容错帧率
 * "alarmDuration": "5"  //告警时长
 * }
 */
@Data
public class AlgorithmParamConfigDto implements Serializable {

    /**
     * 是否开启关键帧
     */
    private Boolean keyFrameOnly;
    /**
     * 识别N帧
     */
    private Integer defaultFrameRate;
    /**
     * 跳过N帧
     */
    private Integer defaultSkipFrameRate;
    /**
     * 识别N秒
     */
    private Integer defaultFrameExtractionTime;

    public Boolean getKeyFrameOnly() {
        if (keyFrameOnly == null) {
            return false;
        }
        return keyFrameOnly;
    }

    public Integer getDefaultFrameRate() {
        if (defaultFrameRate == null) {
            return 1;
        }
        return defaultFrameRate;
    }

    public Integer getDefaultSkipFrameRate() {
        if (defaultSkipFrameRate == null) {
            return 1;
        }
        return defaultSkipFrameRate;
    }


    public Integer getDefaultFrameExtractionTime() {
        if (defaultFrameExtractionTime == null) {
            return 1;
        }
        return defaultFrameExtractionTime;
    }

    /**
     * 聚集人数
     * 暂时不实现
     */
    private Integer humNum;

    /**
     * 停留时长
     */
    private Integer stayTime;

    /**
     * 置信度
     */
    private Double sensitivity;

    /**
     * 容错帧率
     * 暂时不实现
     */
    private Integer faultTolerantFrameRate;

    /**
     * 告警时长
     */
    private Integer alarmDuration;

    public Integer getAlarmDuration() {
        if (alarmDuration == null) {
            return 1;
        }
        return alarmDuration;
    }

    /**
     * 灵敏度
     * 暂时不实现
     */
    private Double confidence;


    /**
     * 没啥屌用
     */
    private Integer pressureTestSwitch;

}
