package com.saida.analysis.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaidaRequest {
    // 设备编号
    private String ipc_code;
    // 算法编号，逗号拼接多个
    private String algo_code;
    // 图片base64
    private String pic_url;
    // 扩展字段 不用解析回调回来就行
    private String ext;
    // 回调地址
    private String callback_url;

    private List<RegionsDto> regions;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RegionsDto {
        private Integer vertex_num;
        private Integer leave_time;
        private String region_id;
        private List<VertexDto> ast_vertex;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VertexDto {
        private Integer x;
        private Integer y;
    }
}
