package com.saida.analysis.dto;


import com.saida.analysis.confu.dto.ConfuAiResultDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GrpcAlgorithmResultDto {

    /**
     * 矩阵是从左上角的开始
     * X 坐标：从左到右增加。
     * Y 坐标：从上到下增加。
     */
    private int label;          // 标签
    private String labelName;   // 标签 名字
    private double prob;        // 概率
    // 矩形框的左上角 X 坐标
    private int minx;
    // 矩形框的右下角 X 坐标
    private int maxx;
    // 矩形框的左上角 Y 坐标
    private int miny;
    // 矩形框的右下角 Y 坐标
    private int maxy;

    // 返回的区域/线条id
    private Double roi_line_id;
    // 轨迹点数量
    private Integer traj_len;
    // 轨迹点
    private List<ConfuAiResultDto.TrajDto> traj;
    //-1：未知状态；0：正常状态；1：奔跑状态；2：徘徊状态；3：逗留状态
    private Integer abd_evt_type;
    //0：未知事件；1：进入事件；2：离开事件
    private Integer cpc_evt_type;
    // 累计进入总数
    private Integer in_num;
    // 累计离开总数
    private Integer out_num;
    // 车牌号
    private String plate_number;
    // 车牌单双行 0 位置 1单 2双
    private Integer plate_line;
}
