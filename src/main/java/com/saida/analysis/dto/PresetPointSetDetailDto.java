package com.saida.analysis.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * [{
 * "drawAreaVOList": [{
 * "checkRule": 0,
 * "color": "#1990FF",
 * "coordinate": "[{\"x\":36,\"y\":160},{\"x\":1870,\"y\":79},{\"x\":1884,\"y\":1016},{\"x\":22,\"y\":1032}]",
 * "id": 1863467852044304386,
 * "name": "检测区",
 * "type": 0
 * }]
 * }]
 */
@Data
public class PresetPointSetDetailDto implements Serializable {

    /**
     *
     */
    private List<DrawAreaDTO> drawAreaVOList;

    /**
     * 预置点位id
     */
    private String presetPointId;


    @Data
    public static class DrawAreaDTO implements Serializable {

        /**
         * 检测规则 0:目标框和检测框有交集 1:目标中心点在框内
         */
        private Integer checkRule;
        private String color;
        /**
         * 源数据
         */
        private String coordinate;
        /**
         * 坐标
         */
        private List<CoordinateDto> coordinateList;
        private String name;
        /**
         * 类型 0:检测区 1:屏蔽区 2: 拌线
         */
        private Integer type;
        /**
         * 拌线高度
         */
        private Integer mixedLineHeight;
        /**
         * 拌线宽度
         */
        private Integer mixedLineWidth;
    }

    @Data
    public static class CoordinateDto implements Serializable {
        // 0-1的百分比
        private double x;
        // 0-1的百分比
        private double y;
    }


}
