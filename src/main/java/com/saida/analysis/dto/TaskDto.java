package com.saida.analysis.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Data
public class TaskDto {

    // 任务集合
    private HashMap<Long, DcTaskDispatchDto> dcTaskDispatchMap = new HashMap<>();
    //播放地址
    private String url;
    // 设备唯一标识
    private Long deviceId;
    // 最小抽帧间隔
    private AlgorithmParamConfigDto algorithmParamConfigDto;
    //每个任务告警配置
    private Map<Long, AlgorithmParamConfigDto> algorithmParamConfigDtoMap = new HashMap<>();
    //绘制的框 key为任务id  value为绘制的框集合
    private HashMap<Long, List<PresetPointSetDetailDto>> presetPointSetDetailDtoList = new HashMap<>();

}
