package com.saida.analysis.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FuAnRequest {
    private String picUrl;
    private String extra;
    private String algorithmType;
    private String httpCallbackUrl;

    private List<AiAnalyzeAreasDto> aiAnalyzeAreas;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AiAnalyzeAreasDto{
        //分析区域Id
        //全图分析时为”-1”
        private String polygonId;
        //分析区域定义，二维数组字符串，如：
        //“[[1879,1053],[1879,10],[12,10],[12,1053]]”
        //若为null，则全图
        private String analyzePolygon;
        private List<String> analyzeTypes;
    }
}
