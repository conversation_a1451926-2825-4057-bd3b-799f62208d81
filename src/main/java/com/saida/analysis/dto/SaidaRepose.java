package com.saida.analysis.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaidaRepose {
    /**
     * {
     * 	"code": 0,
     * 	"msg": "",
     * 	"data": [{
     * 		"rect": {
     * 			"x": 702.75,
     * 			"y": 296.625,
     * 			"w": 176.25,
     * 			"h": 76.875
     *                },
     * 		"type_id": 2    * 	}, {
     * 		"rect": {
     * 			"x": 873,
     * 			"y": 228.75,
     * 			"w": 148.5,
     * 			"h": 82.875
     *        }        ,
     * 		"type_id": 2
     *    }]
     * }
     */

    private Integer code;
    private String msg;
    private List<DataDto> data;

    @Data
    public static class DataDto {
        private RectDto rect;
        private Integer type_id;
        private Double prob;

        private Integer feature_size;

        private List<Double> feature;

        @Data
        public static class RectDto {
            private Double x;
            private Double y;
            private Double w;
            private Double h;
        }
    }
}
