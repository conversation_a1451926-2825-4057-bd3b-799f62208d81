package com.saida.analysis.dto;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.saida.analysis.confu.dto.*;
import com.saida.analysis.entity.DcTaskDispatch;
import com.saida.analysis.mq.message.AlarmMessage;
import com.saida.analysis.mq.message.DevicePreSetJumpMessage;
import com.saida.analysis.mqListener.RockerMqTopic;
import com.saida.analysis.service.AnalysisService;
import com.saida.analysis.util.IllegalFishingDetector;
import com.saida.analysis.util.OverlayFilteringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Data
@Slf4j
@EqualsAndHashCode(callSuper = true)
public class DcTaskDispatchDto extends DcTaskDispatch {

    /**
     * 当前任务的告警配置
     */
    private AlgorithmParamConfigDto algorithmParamConfigDto;

    /**
     * ai配(要下发给算法程序用的)
     */
    private ConfuAiConfigDto confuAiConfigDto = new ConfuAiConfigDto();

    /**
     * 检测区域信息
     */
    private List<SaidaRequest.RegionsDto> regions;

    private Integer w;
    private Integer h;


    public List<SaidaRequest.RegionsDto> getRegions(Integer iw, Integer ih) {
        if (!Objects.equals(iw, w) || !Objects.equals(ih, h)) {
            List<SaidaRequest.RegionsDto> iregions = new ArrayList<>();
            w = iw;
            h = ih;
            List<PresetPointSetDetailDto> presetPointSetDetailDtoList = JSON.parseArray(getPresetPointSetDetail(), PresetPointSetDetailDto.class);
            int a = 1;
            for (PresetPointSetDetailDto dto : presetPointSetDetailDtoList) {
                if (a == 1) {
                    for (PresetPointSetDetailDto.DrawAreaDTO drawAreaDTO : dto.getDrawAreaVOList()) {
                        SaidaRequest.RegionsDto region = new SaidaRequest.RegionsDto();
                        List<PresetPointSetDetailDto.CoordinateDto> coordinates = JSON.parseArray(drawAreaDTO.getCoordinate(), PresetPointSetDetailDto.CoordinateDto.class);
                        drawAreaDTO.setCoordinateList(coordinates);
                        region.setLeave_time(180);
                        region.setRegion_id(drawAreaDTO.getName());
                        region.setAst_vertex(new ArrayList<>());
                        for (PresetPointSetDetailDto.CoordinateDto coordinateDTO : coordinates) {
                            region.getAst_vertex().add(
                                    new SaidaRequest.VertexDto(
                                            (int) (coordinateDTO.getX() * iw),
                                            (int) (coordinateDTO.getY() * ih)
                                    )
                            );
                        }
                        region.setVertex_num(region.getAst_vertex().size());
                        iregions.add(region);
                    }
                }
                a = a + 1;
            }
            regions = iregions;
        }
        return regions;
    }

    public void updateConfuAiConfigDto(long frameWidth, long frameHeight) {
        confuAiConfigDto.setTrackOrNot(getAutoTrace() == null ? 0 : 1);
        if (!presetPointSetDetailDtoList.isEmpty()) {
            log.info("构建需要下发的算法配置 dtoList.size:{}, frameWidth:{},frameHeight:{},size:{}", frameWidth, frameHeight, presetPointSetDetailDtoList.size(), presetPointSetDetailDtoList.get(0).getDrawAreaVOList().size());
            presetPointSetDetailDtoList.get(0).getDrawAreaVOList().forEach(drawAreaVO -> {
                //coordinateDto.getX()  和 coordinateDto.getY() 是0-1的比例
                // 1 检测区
                if (drawAreaVO.getType() == 0) {
                    ConfuAiConfigDto.RegionDto region = new ConfuAiConfigDto.RegionDto();
                    region.setVertexNum(drawAreaVO.getCoordinateList().size());
                    drawAreaVO.getCoordinateList().forEach(coordinateDto -> {
                        ConfuAiConfigDto.CoordinateDto coordinateDtoByConfu = new ConfuAiConfigDto.CoordinateDto();
                        coordinateDtoByConfu.setX(coordinateDto.getX() * frameWidth);
                        coordinateDtoByConfu.setY(coordinateDto.getY() * frameHeight);
                        region.getAstVertex().add(coordinateDtoByConfu);
                    });
                    confuAiConfigDto.getDetectionParams().getRegions().add(region);
                }
                // 2 拌线
                if (drawAreaVO.getType() == 2) {
                    ConfuAiConfigDto.LineDto lineDto = new ConfuAiConfigDto.LineDto();
                    if (drawAreaVO.getCoordinateList().size() >= 4) {
                        PresetPointSetDetailDto.CoordinateDto p3 = drawAreaVO.getCoordinateList().get(2);
                        PresetPointSetDetailDto.CoordinateDto p4 = drawAreaVO.getCoordinateList().get(3);
                        double dx = (p4.getX() - p3.getX()) * frameWidth;
                        double dy = (p4.getY() - p3.getY()) * frameHeight;
                        double realHeight = Math.sqrt(dx * dx + dy * dy);
                        lineDto.setLineHeight(realHeight);
                    }
                    if (drawAreaVO.getCoordinateList().size() >= 2) {
                        ConfuAiConfigDto.CoordinateDto stStart = new ConfuAiConfigDto.CoordinateDto();
                        stStart.setX(drawAreaVO.getCoordinateList().get(0).getX() * frameWidth);
                        stStart.setY(drawAreaVO.getCoordinateList().get(0).getY() * frameHeight);
                        lineDto.setStStart(stStart);
                        ConfuAiConfigDto.CoordinateDto stEnd = new ConfuAiConfigDto.CoordinateDto();
                        stEnd.setX(drawAreaVO.getCoordinateList().get(1).getX() * frameWidth);
                        stEnd.setY(drawAreaVO.getCoordinateList().get(1).getY() * frameHeight);
                        lineDto.setStEnd(stEnd);
                        confuAiConfigDto.getDetectionParams().getLines().add(lineDto);
                    }
                }
            });
        }
        log.info("构建需要下发的算法配置 结束 regions.size:{}", confuAiConfigDto.getDetectionParams().getRegions().size());
    }

    /**
     * 是否已经下发配置给 算法程序
     */
    private boolean sendConfuAiConfig = false;


    public AlgorithmParamConfigDto getAlgorithmParamConfigDto() {
        if (algorithmParamConfigDto == null) {
            algorithmParamConfigDto = JSON.parseObject(getAlgorithmParamConfig(), AlgorithmParamConfigDto.class);
        }
        return algorithmParamConfigDto;
    }

    private int[] frameRate;

    /**
     * 是否调用ai？
     */
    public boolean getIsUseAi(long frameIndex) {
        return frameRate[(int) (frameIndex % frameRate.length)] > 0;
    }

    /**
     * 这个算法结果要显示多少帧？
     */
    public int getShowFrameCount(long frameIndex) {
        if (algorithmParamConfigDto.getKeyFrameOnly()) {
            //关键帧模式
            return 3;
        }
        return frameRate[(int) (frameIndex % frameRate.length)];
    }


    /**
     * 初始化一些参数
     */
    public void init() {
        int skipFrameRate = algorithmParamConfigDto.getDefaultSkipFrameRate();
        int processFrameRate = algorithmParamConfigDto.getDefaultFrameRate();
        int totalLength = skipFrameRate + processFrameRate;
        frameRate = new int[totalLength];
        // 填充处理帧
        for (int i = skipFrameRate; i < totalLength - 1; i++) {
            // 如果是最后一个处理帧（周期末）
            frameRate[i] = 1;
        }
        frameRate[totalLength - 1] = 1 + skipFrameRate;
        log.info("init frameRate:{}", frameRateToFr());
    }

    public String frameRateToFr() {
        StringBuilder sb = new StringBuilder("FrameRate Array: [");
        if (frameRate != null && frameRate.length > 0) {
            for (int i = 0; i < frameRate.length; i++) {
                sb.append(frameRate[i]);
                if (i < frameRate.length - 1) {
                    sb.append(", ");
                }
            }
        }
        sb.append("]");
        return sb.toString();
    }

    int showFrameCount = 0;


    /**
     * 绘制的框集合
     */
    private List<PresetPointSetDetailDto> presetPointSetDetailDtoList;

    private Double sensitivityDouble;

    public Double getSensitivityDouble() {
        if (sensitivityDouble == null && algorithmParamConfigDto != null) {
            sensitivityDouble = algorithmParamConfigDto.getSensitivity() / 100;
        }
        return sensitivityDouble;
    }

    /**
     * 上次告警时间
     */
    private Long lastAlarmTime = 0L;

    private List<String> split;

    private ConfuAlgEnum confuAlgEnum;

    public ConfuAlgEnum getConfuAlgEnum() {
        if (confuAlgEnum == null) {
            confuAlgEnum = ConfuAlgEnum.getAlgByAlgCode(getAlgorithmCode());
        }
        return confuAlgEnum;
    }

    public List<String> getAlgorithmMinCodeList() {
        if (split != null) {
            return split;
        }
        if (StringUtils.isNotBlank(getAlgorithmMinCode())) {
            split = Arrays.stream(getAlgorithmMinCode().split(",")).collect(Collectors.toList());
        } else {
            split = new ArrayList<>();
        }
        return split;
    }


    /**
     * 是否到达告警时间
     */
    public boolean timeOfAlarm(Long timestamp, boolean isLog) {
        if (lastAlarmTime == 0) {
            return true;
        }
        long alarmDurationMs = algorithmParamConfigDto.getAlarmDuration() * 1000;
        long currentTime = timestamp - lastAlarmTime;
        if (currentTime <= alarmDurationMs) {
            if (isLog) {
                // 告警时间超过配置的告警时间
                log.info("AI结果过滤：任务没有达到告警时间，跳过当前任务 抽帧时间:{}ms 上次告警时间;{}ms 间隔:{}ms 配置的间隔:{}ms"
                        , timestamp, lastAlarmTime, currentTime, alarmDurationMs);

            }
            return false;
        }
        return true;
    }


    /**
     * 计算分析后的结果 是否告警
     */
    public List<GrpcAlgorithmResultDto> calculationResults(ConfuAnalysisDto analysisDto, DevicePreSetJumpMessage devicePreSetJumpMessage) {
        List<GrpcAlgorithmResultDto> algorithmsResult = analysisDto.getAlgorithmsResult();
        // 抽帧时间
        if (analysisDto.getTimestamp() < 1 || algorithmsResult.isEmpty()) {
            log.error("calculationResults 分析服务-参数异常，extractionFrameTime：{}, base64Image_isEmpty:{}, algorithmsResultArr_isEmpty:{}"
                    , analysisDto.getTimestamp(), false, algorithmsResult.isEmpty());
            return new ArrayList<>();
        }
        //过滤信任度的数据
        algorithmsResult = algorithmsResult.stream()
                .filter(e -> Double.compare(e.getProb(), getSensitivityDouble()) >= 0)
                .collect(Collectors.toList());
        if (presetPointSetDetailDtoList.isEmpty()) {
            log.info("calculationResults 队列消费监控 电子围栏1为空[结束] deviceId:{} algorithmsResultTime:{}", getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        // 电子围栏配置
        PresetPointSetDetailDto presetPointSetDetailDto = presetPointSetDetailDtoList.get(0);
        if (devicePreSetJumpMessage != null) {
            Map<String, PresetPointSetDetailDto> collect = presetPointSetDetailDtoList.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getPresetPointId()))
                    .collect(Collectors.toMap(PresetPointSetDetailDto::getPresetPointId, Function.identity(), (v1, v2) -> v1));
            presetPointSetDetailDto = collect.get(String.valueOf(devicePreSetJumpMessage.getIndex()));
        }
        if (presetPointSetDetailDto == null) {
            log.info("calculationResults 队列消费监控 电子围栏2为空[结束] deviceId:{} algorithmsResultTime:{}", getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        List<PresetPointSetDetailDto.DrawAreaDTO> drawAreaVOList = presetPointSetDetailDto.getDrawAreaVOList();
        if (CollectionUtils.isEmpty(drawAreaVOList)) {
            log.info("calculationResults 队列消费监控 电子围栏3为空[结束] deviceId:{} algorithmsResultTime:{}", getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        List<GrpcAlgorithmResultDto> filteredResults = AnalysisService.filterResults(algorithmsResult, drawAreaVOList, analysisDto.getW(), analysisDto.getH());
        if (filteredResults.isEmpty()) {
            log.info("calculationResults 队列消费监控 结果都不在框中[结束] deviceId:{} taskId:{} algorithmsResultTime:{}," +
                            ",algorithmsResult:{},drawAreaVOList:{}"
                    , getDeviceId()
                    , getId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime()
                    , JSON.toJSONString(algorithmsResult), JSON.toJSONString(drawAreaVOList));
            return new ArrayList<>();
        }
        //非法垂钓过滤
        if ("H_DET_ILLFISH".equals(getAlgorithmCode())) {
            filteredResults = IllegalFishingDetector.detectIllegalFishing(filteredResults, analysisDto.getW(), analysisDto.getH());
        }
        // 过滤叠框的数据
        filteredResults = OverlayFilteringUtil.filterResults(filteredResults);
        if (filteredResults.isEmpty()) {
            log.info("calculationResults 队列消费监控 H_DET_ILLFISH 处理后结果都不在框中[结束] deviceId:{} algorithmsResultTime:{},"
                    , getDeviceId()
                    , analysisDto.getAlgorithmsResultTime() - analysisDto.getDoAnalysisThreadStartTime());
            return new ArrayList<>();
        }
        return filteredResults;
    }

    ThreadPoolExecutor executor = new ThreadPoolExecutor(
            1,  // corePoolSize = 1
            1,  // maximumPoolSize = 1
            0L, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(100),  // 有界队列
            (r, executor) -> {
                log.error("{} 队列满，丢弃任务", getDeviceId());
            }
    );

    private SpringRoom springRoom;

    public void addAlarm(byte[] imageBytes, AlarmResDto alarmResDto, List<GrpcAlgorithmResultDto> results) {
        if (imageBytes == null || results == null || results.isEmpty()) {
            log.error("addAlarm 参数异常，imageBytes为空或results为空 deviceId:{}", getDeviceId());
            return;
        }
        // 将耗时操作提交到线程池异步执行
        executor.execute(() -> {
            try {
                long id = IdWorker.getId();
                String timePrefix = DateUtil.format(LocalDateTime.now(), "yyyy/MM/dd/HH/mm");
                // 上传图片
                String imageUrl = springRoom.getFileService().upload(imageBytes, "alarm/t3/" + timePrefix + "/" + id + ".jpg");
                // 构造告警消息
                AlarmMessage alarmMessage = new AlarmMessage();
                alarmMessage.setTime(alarmResDto.getTimestamp());
                alarmMessage.setAlgorithmCode(getAlgorithmCode());
                alarmMessage.setAlgorithmMinCode(getAlgorithmMinCode());
                alarmMessage.setAnalysisImage(springRoom.getAlgDrawUrl() + "prob/" + id + ".jpg");
                // 特殊处理 N_DET_CHANGE 算法
//                if ("N_DET_CHANGE".equals(getAlgorithmCode())) {
//                    byte[] lastImageBytes = AnalysisService.getLastTaskImgMap().get(getDeviceId());
//                    if (lastImageBytes != null && lastImageBytes.length > 0) {
//                        String temp = fileService.upload(lastImageBytes, "alarm/t3/" + timePrefix + "/" + IdWorker.getId() + ".jpg");
//                        alarmMessage.setAnalysisImage(alarmMessage.getAnalysisImage() + "," + temp);
//                    }
//                }
                alarmMessage.setImage(imageUrl);
                alarmMessage.setAnalysisImageNotProb(springRoom.getAlgDrawUrl() + "notProb/" + id + ".jpg");
                alarmMessage.setHeight(alarmResDto.getImageHeight());
                alarmMessage.setWidth(alarmResDto.getImageWidth());
                alarmMessage.setDeviceId(getDeviceId());
                alarmMessage.setAlgorithmsResult(results);
                alarmMessage.setTaskId(getId());
                alarmMessage.setMsgId(id);
                // 发送到MQ
                springRoom.getVLinkerMqTemplate().asyncSendToQueue(RockerMqTopic.ANALYSIS_ALARM.getTopic(), alarmMessage);
                long endTime = System.currentTimeMillis();
                log.info("addAlarm 发送告警消息完成 deviceId:{}  endTime:{} 消息id:{}",
                        getDeviceId(), endTime - alarmResDto.getTimestamp(), id);
            } catch (Exception e) {
                log.error("addAlarm 异步处理告警消息异常 deviceId:{}", getDeviceId(), e);
            }
        });
    }

}
