package com.saida.analysis.grpcTask;


import cn.hutool.core.io.FileUtil;
import com.google.protobuf.ByteString;
import com.saida.analysis.pb.TaskExchangeGrpc;
import com.saida.analysis.pb.TaskExchangeOuterClass;
import io.grpc.Grpc;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * grpc 服务端配置类
 */

@Configuration
@Slf4j
public class GrpcChannelConfig {


    public static void main(String[] args) {
        TlsChannelCredentials.Builder tlsBuilder = TlsChannelCredentials.newBuilder();
        try {
            File caFile = new File(System.getProperty("user.dir") + "/algConfig/crt/ca.crt");
            if (!caFile.exists()) {
                throw new RuntimeException("证书文件不存在：请检查证书路径！");
            }
            tlsBuilder.trustManager(caFile);
        } catch (IOException e) {
            throw new RuntimeException("加载 TLS 证书失败", e);
        }
        log.info("file");
        byte[] imageBytes = FileUtil.readBytes("D:\\imgTemp\\1859911354680827904\\1737514886-23494_600.jpeg");

        log.info("grpc");
        ManagedChannel channel = Grpc.newChannelBuilderForAddress("*********", 18777, tlsBuilder.build())
                .idleTimeout(30, TimeUnit.SECONDS)  // 空闲超时
                .keepAliveWithoutCalls(true)  // 即使无请求时保持连接
                .keepAliveTime(60, TimeUnit.SECONDS)   // 保持连接的最大时间
                .keepAliveTimeout(1, TimeUnit.SECONDS)  // 超时时间
                .disableRetry()//取消重试
                .maxInboundMetadataSize(32 * 1024) // 调整最大 Header 大小到 32 KB
                .build();
        TaskExchangeGrpc.TaskExchangeBlockingStub taskExchangeBlockingStub1 = TaskExchangeGrpc.newBlockingStub(channel);
        TaskExchangeOuterClass.ImageFormat imgFormat = TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_JPEG;
        TaskExchangeOuterClass.ImageTask.Builder builder = TaskExchangeOuterClass.ImageTask.newBuilder()
                .setImg(ByteString.copyFrom(imageBytes))
                .setImgFormat(imgFormat);
        builder.addAlgorithms(TaskExchangeOuterClass.DetectionAlgorithm.U_DET_CAR);
        TaskExchangeOuterClass.ImageTask request = builder.build();
        log.info("request");
        try {
            taskExchangeBlockingStub1 = taskExchangeBlockingStub1.withDeadlineAfter(2, TimeUnit.SECONDS);
            TaskExchangeOuterClass.OnAIResultGotReply onAIResultGotReply = taskExchangeBlockingStub1.requestForImage(request);
        } catch (Exception e) {
            log.error("error", e);
        }
        log.info("onAIResultGotReply");


    }

    //    @Value("${grpc.server.host:**************}")
    @Value("${grpc.server.host:*********}")
    private String grpcServerHost;

    @Value("${grpc.server.port:18777}")
    private int grpcServerPort;

    private static ManagedChannel channel;

    // TLS 配置
    private TlsChannelCredentials.Builder tlsBuilder;

    @PostConstruct
    public void init() {
        log.info("gRPC 服务配置初始化完成: {}:{}", grpcServerHost, grpcServerPort);
        this.tlsBuilder = createTlsBuilder();
    }

    /**
     * 为指定任务创建或获取 gRPC 通道
     *
     * @return ManagedChannel
     */
    public ManagedChannel getOrCreateChannel() {
        if (channel == null || channel.isShutdown() || channel.isTerminated()) {
            try {
                channel = Grpc.newChannelBuilderForAddress(grpcServerHost, grpcServerPort, tlsBuilder.build())
                        .idleTimeout(30, TimeUnit.SECONDS)  // 空闲超时
                        .keepAliveWithoutCalls(true)  // 即使无请求时保持连接
                        .keepAliveTime(60, TimeUnit.SECONDS)   // 保持连接的最大时间
                        .keepAliveTimeout(20, TimeUnit.SECONDS)  // 超时时间
                        .disableRetry()//取消重试
                        .maxInboundMetadataSize(32 * 1024) // 调整最大 Header 大小到 32 KB
                        .build();
                log.info("图片任务 的 gRPC 通道已成功创建");
                return channel;
            } catch (Exception e) {
                log.error("创建 gRPC 通道时发生异常: ", e);
                return null;
            }
        }
        return channel;
    }

    /**
     * 获取任务对应的 gRPC BlockingStub
     *
     * @return TaskExchangeGrpc.TaskExchangeBlockingStub
     */
    public TaskExchangeGrpc.TaskExchangeBlockingStub getTaskExchangeStub() {
        ManagedChannel channel = getOrCreateChannel();
        TaskExchangeGrpc.TaskExchangeBlockingStub taskExchangeBlockingStub = TaskExchangeGrpc.newBlockingStub(channel);
        taskExchangeBlockingStub = taskExchangeBlockingStub.withDeadlineAfter(1, TimeUnit.SECONDS);
        return taskExchangeBlockingStub;
    }

    /**
     * 应用关闭时清理所有 gRPC 通道
     */
    @PreDestroy
    public void shutdownAllChannels() {
        log.info("应用停止，开始关闭所有任务的 gRPC 通道");
        if (channel != null) {
            channel.shutdownNow();
            channel = null;
        }
    }

    /**
     * 创建 TLS 配置
     */
    private TlsChannelCredentials.Builder createTlsBuilder() {
        TlsChannelCredentials.Builder tlsBuilder = TlsChannelCredentials.newBuilder();
        try {
            ClassPathResource resource = new ClassPathResource("algConfig/ca.crt");
            if (!resource.exists()) {
                log.error("证书文件不存在：请检查证书路径！path:{}", resource.getPath());
                throw new RuntimeException("证书文件不存在：请检查证书路径！");
            }
            tlsBuilder.trustManager(resource.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException("加载 TLS 证书失败", e);
        }
        return tlsBuilder;
    }
}
