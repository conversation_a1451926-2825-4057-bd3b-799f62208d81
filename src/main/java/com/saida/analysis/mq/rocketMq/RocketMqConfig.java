package com.saida.analysis.mq.rocketMq;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Slf4j
@Data
@Component
@ConfigurationProperties("rocketmq")
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class RocketMqConfig {
    private Boolean enable;

    private String nameServer;

    private ProducerDto producer;

    @Data
    public static class ProducerDto{
        //    # 发送同一类消息的设置为同一个group，保证唯一
        //    group: vlinker_producer_group
        //    # 发送消息超时时间，默认3000
        //    sendMessageTimeout: 10000
        //    # 发送消息失败重试次数，默认2
        //    retryTimesWhenSendFailed: 2
        //    # 异步消息重试此处，默认2
        //    retryTimesWhenSendAsyncFailed: 2
        //    # 消息最大长度，默认1024 * 1024 * 4(默认4M)
        //    #    maxMessageSize: 4096
        //    # 压缩消息阈值，默认4k(1024 * 4)
        //    compressMessageBodyThreshold: 4096
        //    # 是否在内部发送失败时重试另一个broker，默认false
        //    retryNextServer: false
        private String group;
        private Integer sendMessageTimeout;
        private Integer retryTimesWhenSendFailed;
        private Integer retryTimesWhenSendAsyncFailed;
        private Integer maxMessageSize;
        private Integer compressMessageBodyThreshold;
        private Boolean retryNextServer;
    }
}
