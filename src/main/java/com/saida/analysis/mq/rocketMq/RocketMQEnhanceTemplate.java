package com.saida.analysis.mq.rocketMq;

import com.alibaba.fastjson.JSONObject;
import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;


@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class RocketMQEnhanceTemplate {

    @Autowired(required = false)
    private RocketMqConfig rocketMqConfig;
    @Resource
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        try {
            initProducer();
        } catch (Exception e) {
            for (int i = 0; i < 10; i++) {
                log.error("❌ rocketmq生产者 初始化失败了 程序退出 springboot RocketMQEnhanceTemplate error");
            }
            log.error("❌ rocketmq生产者 初始化失败了 程序退出 springboot RocketMQEnhanceTemplate error", e);
            SpringApplication.exit(applicationContext, () -> 1);
        }

        scheduler.scheduleAtFixedRate(() -> {
            for (String topic : topicQueues.keySet()) {
                sendBatch(topic);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }


    public static DefaultMQProducer producer = null;

    @PreDestroy
    public void destroy() {
        if (producer != null) {
            producer.shutdown();
        }
    }

    private void initProducer() {
        if (rocketMqConfig == null) {
            log.info("RocketMqConfig is null (未开启mq)");
            return;
        }
        if (producer != null) {
            return;
        }
        // 1. 创建生产者并指定组名
        producer = new DefaultMQProducer(rocketMqConfig.getProducer().getGroup());
        // 2. 设置 NameServer 地址
        producer.setNamesrvAddr(rocketMqConfig.getNameServer());
        // 3. 启动生产者
        try {
            producer.start();
            log.info("DefaultMQProducer start success group:{},nameServer:{}", rocketMqConfig.getProducer().getGroup(), rocketMqConfig.getNameServer());
        } catch (MQClientException e) {
            log.error("DefaultMQProducer start error", e);
        }
    }


    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> SendResult send(String topic, T message) {
        return send(topic, null, message);
    }

    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> SendResult send(String topic, String tag, T message) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }
        Message sendMessage = new Message(topic, tag, message.getKey(), JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8));
        SendResult send;
        try {
            send = producer.send(sendMessage);
        } catch (Exception e) {
            log.error("同步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
        // 此处为了方便查看给日志转了json，根据选择选择日志记录方式，例如ELK采集
        log.info("[{}]同步消息[{}]发送结果[{}]", topic + ":" + tag, JSONObject.toJSON(message), JSONObject.toJSON(send));
        return send;
    }


    public SendResult send(String topic, List<Message> sendMessage) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }
        SendResult send;
        try {
            send = producer.send(sendMessage);
        } catch (Exception e) {
            log.error("批量同步消息内容：{}, 发送异常：{}", sendMessage.size(), e.getMessage());
            throw new RuntimeException(e);
        }
        // 此处为了方便查看给日志转了json，根据选择选择日志记录方式，例如ELK采集
        log.info("[{}]批量同步消息[{}]发送结果[{}]", topic + ":", sendMessage.size(), JSONObject.toJSON(send));
        return send;
    }

    public <T extends BaseMessage> void asyncSend(String topic, String tag, T message, SendCallback callback) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }
        Message sendMessage = new Message(topic, tag, message.getKey(), JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8));
        try {
            producer.send(sendMessage, callback);
            log.info("[{}]异步消息[{}]", topic + ":" + tag, JSONObject.toJSON(message));
        } catch (Exception e) {
            log.error("异步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public <T extends BaseMessage> void asyncSend(String topic, T message, SendCallback callback) {
        asyncSend(topic, null, message, callback);
    }
    // 按 topic 存储队列
    private final Map<String, Queue<Message>> topicQueues = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * 异步发送消息,如果有一百条消息发送一个集合 而且每一秒也发送一次集合内的数据
     */
    public <T extends BaseMessage> void asyncSendToQueue(String topic, T message) {
        // 获取或创建该 topic 的队列
        Queue<Message> messageQueue = topicQueues.computeIfAbsent(
                topic, key -> new ConcurrentLinkedQueue<>());
        Message temp = new Message(topic, message.getKey(), JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8));
        // 添加消息到队列
        messageQueue.add(temp);
        // 达到100条立即发送
        if (messageQueue.size() >= 100) {
            sendBatch(topic);
        }
    }

    /**
     * 批量发送方法
     */
    private synchronized void sendBatch(String topic) {
        Queue<Message> messageQueue = topicQueues.get(topic);
        if (messageQueue == null || messageQueue.isEmpty()) {
            return;
        }
        // 收集批量消息
        Collection<Message> batch = new ArrayList<>();
        Message msg;
        while ((msg = messageQueue.poll()) != null) {
            batch.add(msg);
        }

        if (!batch.isEmpty()) {
            long startTime = System.currentTimeMillis();
            try {
                producer.send(batch, new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        log.info("队列消费监控 批量发送成功: topic:{} size:{} 用时:{} ms, res:{}",
                                topic, batch.size(), System.currentTimeMillis() - startTime, sendResult);
                    }

                    @Override
                    public void onException(Throwable throwable) {
                        log.error("批量发送异常: topic:{} 错误:{}", topic, throwable.getMessage(), throwable);
                    }
                });
            } catch (Exception e) {
                log.error("批量发送方法 异步消息 topic：{}, 发送异常：{}", topic, e.getMessage());
                throw new RuntimeException(e);
            }

        }
    }


}