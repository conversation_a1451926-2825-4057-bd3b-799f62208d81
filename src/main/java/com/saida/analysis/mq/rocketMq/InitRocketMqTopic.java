package com.saida.analysis.mq.rocketMq;

import com.saida.analysis.mq.vlinker.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.TopicConfig;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.apache.rocketmq.remoting.protocol.body.ClusterInfo;
import org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.protocol.route.BrokerData;
import org.apache.rocketmq.tools.admin.DefaultMQAdminExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class InitRocketMqTopic {

    @Autowired(required = false)
    private RocketMqConfig rocketMqConfig;
    @Autowired(required = false)
    private Map<String, VLinkerMqMessageListener> listenerMap;


    private static DefaultMQAdminExt mqAdminExt = null;

    private static final Map<String, DefaultMQPushConsumer> consumerMap = new HashMap<>();
    @Resource
    private ApplicationContext applicationContext;

    @PostConstruct
    public void initTopic() {
        if (listenerMap == null || listenerMap.isEmpty()) {
            return;
        }
        log.info("===== 初始化 RocketMQ 开始 ===== server:{}", rocketMqConfig.getNameServer());
        // 创建 MQ 管理工具
        mqAdminExt = new DefaultMQAdminExt();
        mqAdminExt.setNamesrvAddr(rocketMqConfig.getNameServer());
        try {
            // 启动管理工具
            mqAdminExt.start();
            // 获取集群信息
            ClusterInfo clusterInfo = mqAdminExt.examineBrokerClusterInfo();
            Map<String, BrokerData> brokerAddrTable = clusterInfo.getBrokerAddrTable();
            log.info("===== RocketMQ Broker 列表 =====");
            listenerMap.forEach((listenerKey, listenerValue) -> {
                VLinkerTopicConfig vLinkerTopicConfig = listenerValue.vLinkerTopicConfig();
                for (Map.Entry<String, BrokerData> entry : brokerAddrTable.entrySet()) {
                    String clusterName = entry.getKey();
                    BrokerData brokerData = entry.getValue();
                    log.info("开始创建 Topic：{} clusterName:{} ,Broker name:{} , Broker addr:{}"
                            , vLinkerTopicConfig.getTopic(), clusterName, brokerData.getBrokerName(), brokerData.getBrokerAddrs());
                    HashMap<Long, String> brokerAddrs = brokerData.getBrokerAddrs();
                    brokerAddrs.forEach((k, v) -> {
                        // 创建 Topic
                        TopicConfig topicConfig = new TopicConfig();
                        topicConfig.setTopicName(vLinkerTopicConfig.getTopic());
                        topicConfig.setReadQueueNums(vLinkerTopicConfig.getReadQueueNums());
                        topicConfig.setWriteQueueNums(vLinkerTopicConfig.getWriteQueueNums());
                        try {
                            mqAdminExt.createAndUpdateTopicConfig(v, topicConfig);
                            log.info("创建 Topic：{} 成功 clusterName:{} ,Broker name:{} , Broker addr:{}"
                                    , vLinkerTopicConfig.getTopic(), clusterName, brokerData.getBrokerName(), v);
                        } catch (RemotingException e) {
                            log.error("initTopic RemotingException error", e);
                        } catch (MQBrokerException e) {
                            log.error("initTopic MQBrokerException error", e);
                        } catch (InterruptedException e) {
                            log.error("initTopic InterruptedException error", e);
                        } catch (MQClientException e) {
                            log.error("initTopic MQClientException error", e);
                        }
                    });
                }
                log.info("[Topic:{}]开始订阅消息 nameServer：{},tag:{}",
                        vLinkerTopicConfig.getTopic(), rocketMqConfig.getNameServer(), vLinkerTopicConfig.getTag());
                String consumerGroup = vLinkerTopicConfig.getTopic() + "_consumer_group";
                if (!vLinkerTopicConfig.getTag().equals("*")) {
                    consumerGroup = vLinkerTopicConfig.getTopic() + "_" + vLinkerTopicConfig.getTag() + "_consumer_group";
                }
                DefaultMQPushConsumer consumer = consumerMap.get(consumerGroup);
                if (consumer == null) {
                    consumer = new DefaultMQPushConsumer(consumerGroup);
                    consumer.setNamesrvAddr(rocketMqConfig.getNameServer());
                    try {
                        consumer.subscribe(vLinkerTopicConfig.getTopic(), vLinkerTopicConfig.getTag());
                        if (vLinkerTopicConfig.isOrder()) {
                            consumer.registerMessageListener((MessageListenerOrderly)
                                    (list, context) -> {
                                        onMessage(listenerValue, list);
                                        return ConsumeOrderlyStatus.SUCCESS; //消费成功
                                    });
                        } else {
                            consumer.registerMessageListener((MessageListenerConcurrently)
                                    (list, context) -> {
                                        onMessage(listenerValue, list);
                                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS; //消费成功
                                    });
                        }
                        MessageModel messageModel = MessageModel.CLUSTERING;
                        if (vLinkerTopicConfig.getConsumeModel() == VLinkerConsumeModel.BROADCASTING) {
                            messageModel = MessageModel.BROADCASTING;
                        }
                        consumer.setMessageModel(messageModel);
                        //MessageListenerOrderly 这个是有序的
                        //MessageListenerConcurrently 这个是无序的,并行的方式处理，效率高很多
                        ConsumeFromWhere consumeFromWhere = ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET;
                        if (vLinkerTopicConfig.getConsumeFromWhere() == VLinkerConsumeFromWhere.TIMESTAMP) {
                            consumeFromWhere = ConsumeFromWhere.CONSUME_FROM_TIMESTAMP;
                        }
                        consumer.setConsumeFromWhere(consumeFromWhere);
                        consumer.start();
                        consumerMap.put(consumerGroup, consumer);
                        log.info("[Topic:{}] 订阅消息成功 nameServer：{},tag:{}",
                                vLinkerTopicConfig.getTopic(), rocketMqConfig.getNameServer(), vLinkerTopicConfig.getTag());
                    } catch (Exception e) {
                        log.error("defaultMQPushConsumer 消费消息异常", e);
                    }

                }
            });
        } catch (Exception e) {
            for (int i = 0; i < 10; i++) {
                log.error("❌ rockerMq 初始化失败了 程序退出 springboot initTopic error");
            }
            log.error("❌ rockerMq 初始化失败了 程序退出 springboot initTopic error", e);
            SpringApplication.exit(applicationContext, () -> 1);
        } finally {
            // 关闭管理工具
            mqAdminExt.shutdown();
        }
    }

    private void onMessage(VLinkerMqMessageListener listenerValue, List<MessageExt> list) {
        for (MessageExt messageExt : list) {
            try {
                listenerValue.onMessage(VlinkerMqMessage
                        .builder()
                        .topic(listenerValue.vLinkerTopicConfig().getTag())
                        .key(messageExt.getMsgId())
                        .tag(messageExt.getTags())
                        .data(messageExt.getBody())
                        .headers(messageExt.getProperties())
                        .timestamp(System.currentTimeMillis())
                        .build());
            } catch (Exception e) {
                log.error("[mq:{}]消费消息异常", listenerValue.vLinkerTopicConfig().getTopic(), e);
            }
        }
    }


    @PreDestroy
    public void shutdownAllChannels() {
        log.info("应用停止，开始关闭所有的 mq消费者");
        if (!consumerMap.isEmpty()) {
            consumerMap.forEach((k, v) -> {
                v.shutdown();
            });
        }
    }
}
