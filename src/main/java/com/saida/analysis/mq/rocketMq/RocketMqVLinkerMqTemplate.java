package com.saida.analysis.mq.rocketMq;

import com.saida.analysis.mq.vlinker.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "rocketmq", name = "enable", havingValue = "true")
public class RocketMqVLinkerMqTemplate implements VLinkerMqTemplate {

    @Autowired(required = false)
    private RocketMqConfig rocketMqConfig;
    @Resource
    private RocketMQEnhanceTemplate rocket;

    @Override
    public String getNameServer() {
        return rocketMqConfig.getNameServer();
    }

    @Override
    public <T extends BaseMessage> SendResultWrapper send(String topic, T message) {
        return send(topic, null, message);
    }

    @Override
    public <T extends BaseMessage> SendResultWrapper send(String topic, String tag, T message) {
        SendResult result = rocket.send(topic, tag, message);
        return SendResultWrapper.builder()
                .topic(topic)
                .key(message.getKey())
                .provider("rocketmq")
                .success(SendStatus.SEND_OK.equals(result.getSendStatus()))
                .messageId(result.getMsgId())
                .extraInfo("queueId=" + result.getMessageQueue().getQueueId())
                .build();
    }

    @Override
    public SendResultWrapper send(String topic, List<RawMessageWrapper> messages) {
        List<Message> list = messages.stream()
                .map(m -> new Message(topic, m.getKey(), m.getPayload().getBytes(StandardCharsets.UTF_8)))
                .collect(Collectors.toList());

        SendResult result = rocket.send(topic, list);
        return SendResultWrapper.builder()
                .topic(topic)
                .provider("rocketmq")
                .success(SendStatus.SEND_OK.equals(result.getSendStatus()))
                .messageId(result.getMsgId())
                .extraInfo("batchSize=" + messages.size())
                .build();
    }

    @Override
    public <T extends BaseMessage> void asyncSend(String topic, T message, MqSendCallback callback) {
        asyncSend(topic, null, message, callback);
    }

    @Override
    public <T extends BaseMessage> void asyncSend(String topic, String tag, T message, MqSendCallback callback) {
        rocket.asyncSend(topic, tag, message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                callback.onSuccess(SendResultWrapper.builder()
                        .topic(topic)
                        .key(message.getKey())
                        .provider("rocketmq")
                        .success(true)
                        .messageId(sendResult.getMsgId())
                        .extraInfo("queueId=" + sendResult.getMessageQueue().getQueueId())
                        .build());
            }

            @Override
            public void onException(Throwable e) {
                callback.onFailure(e);
            }
        });
    }

    @Override
    public <T extends BaseMessage> void asyncSendToQueue(String topic, T messages) {
        rocket.asyncSendToQueue(topic, messages);
    }

}

