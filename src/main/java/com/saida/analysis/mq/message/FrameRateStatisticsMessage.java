package com.saida.analysis.mq.message;


import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 抽帧频率统计消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrameRateStatisticsMessage extends BaseMessage implements Serializable {
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 统计时间戳
     */
    private Long timestamp;
    
    /**
     * 设备抽帧频率统计
     * Key: 设备ID
     */
    private Map<Long, DeviceFrameDto> deviceFrameRates;



    @Data
    public static class DeviceFrameDto {
        // 抽帧总数
        private AtomicLong frameCount = new AtomicLong(0);
        // 首帧时间
        private Long firstFrameTime = System.currentTimeMillis();
        // 抽帧频率
        private Double frameRate = 0.0;
    }

}