package com.saida.analysis.mq.message;


import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class TaskRecordMessage extends BaseMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long time;

    private Long taskId;

    private Integer type;

    private String desc;

    // 标注一下流程顺序  因为有时候太快time会重复
    private Long sort ;

}
