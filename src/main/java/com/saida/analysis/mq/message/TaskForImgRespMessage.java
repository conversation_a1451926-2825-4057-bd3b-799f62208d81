package com.saida.analysis.mq.message;


import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskForImgRespMessage extends BaseMessage {

    /**
     * 1 成功 2失败
     */
    private Integer type;

    /**
     * 所选算法
     */
    private String algorithmName;

    /**
     * 这次任务的唯一标识
     */
    private Long uuid;

    /**
     * 图片地址
     */
    private String url;


    private String base64;

    /**
     *
     */
    private Long time;
}
