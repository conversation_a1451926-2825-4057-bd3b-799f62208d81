package com.saida.analysis.mq.message;


import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class GrpcTaskStatusMessage extends BaseMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long time;

    private List<Long> taskIds;

    private String nodeId;

}
