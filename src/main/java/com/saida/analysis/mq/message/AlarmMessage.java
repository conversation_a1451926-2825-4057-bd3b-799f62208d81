package com.saida.analysis.mq.message;


import com.saida.analysis.dto.GrpcAlgorithmResultDto;
import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AlarmMessage extends BaseMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long time;

    private String image;

    private String algorithmName;

    private String algorithmCode;

    private String algorithmMinCode;

    private String analysisImage;

    private String analysisImageNotProb;

    private Long deviceId;

    private List<GrpcAlgorithmResultDto> algorithmsResult;

    private Integer width;

    private Integer height;

    private Long taskId;

    private Long msgId;
}
