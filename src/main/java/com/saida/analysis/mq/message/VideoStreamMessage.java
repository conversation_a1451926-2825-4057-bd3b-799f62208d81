package com.saida.analysis.mq.message;

import com.alibaba.fastjson.annotation.JSONField;

import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.Data;

@Data
public class VideoStreamMessage extends BaseMessage {
    @J<PERSON><PERSON>ield(name = "reqId")
    private String reqId;
    @JSONField(name = "cameraId")
    private Long cameraId;
    @JSONField(name = "protocolCode")
    private String protocolCode;
    @JSONField(name = "url")
    private String url;
    @JSONField(name = "sdPushUrl")
    private String sdPushUrl;
    @JSONField(name = "dcTaskDispatchId")
    private Long dcTaskDispatchId;
}
