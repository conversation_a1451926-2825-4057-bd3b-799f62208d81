package com.saida.analysis.mq.kafka;

import com.alibaba.fastjson.JSONObject;
import com.saida.analysis.mq.vlinker.BaseMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "kafka", name = "enable", havingValue = "true")
public class KafkaEnhanceTemplate {

    @Autowired(required = false)
    private KafkaConfig kafkaConfig;
    @Resource
    private ApplicationContext applicationContext;

    private static Producer<String, byte[]> producer = null;

    @PostConstruct
    public void init() {
        try {
            initProducer();
        } catch (Exception e) {
            for (int i = 0; i < 10; i++) {
                log.error("❌ Kafka生产者 初始化失败了 程序退出 springboot KafkaEnhanceTemplate error");
            }
            log.error("❌ Kafka生产者 初始化失败了 程序退出 springboot KafkaEnhanceTemplate error", e);
            SpringApplication.exit(applicationContext, () -> 1);
        }
        scheduler.scheduleAtFixedRate(() -> {
            for (String topic : topicQueues.keySet()) {
                sendBatch(topic);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }

    @PreDestroy
    public void destroy() {
        if (producer != null) {
            producer.close(Duration.ofSeconds(2));
        }
    }

    private void initProducer() {
        if (kafkaConfig == null) {
            log.info("KafkaConfig is null (未开启kafka)");
            return;
        }
        if (producer != null) {
            return;
        }
        log.info("KafkaConfig 开始创建发布者");
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        // 可选项
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        producer = new KafkaProducer<>(props);
        log.info("KafkaProducer init success, servers: {}", kafkaConfig.getBootstrapServers());
    }

    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> RecordMetadata send(String topic, T message) {
        return send(topic, null, message);
    }

    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> RecordMetadata send(String topic, String tag, T message) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }

        ProducerRecord<String, byte[]> record = new ProducerRecord<>(
                topic,
                message.getKey(),
                JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8)
        );

        try {
            RecordMetadata metadata = producer.send(record).get();
            log.info("[{}]同步消息[{}]发送成功: offset={}, partition={}",
                    topic, JSONObject.toJSON(message), metadata.offset(), metadata.partition());
            return metadata;
        } catch (Exception e) {
            log.error("同步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 批量发送同步消息
     */
    public List<RecordMetadata> send(String topic, List<ProducerRecord<String, byte[]>> records) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }
        List<RecordMetadata> results = new ArrayList<>();
        for (ProducerRecord<String, byte[]> record : records) {
            try {
                RecordMetadata metadata = producer.send(record).get();
                results.add(metadata);
            } catch (Exception e) {
                log.error("批量同步消息发送异常 topic:{}, key:{}, error: {}", record.topic(), record.key(), e.getMessage());
                throw new RuntimeException(e);
            }
        }
        log.info("[{}]批量同步消息[{}]发送成功", topic, records.size());
        return results;
    }

    /**
     * 异步发送
     */
    public <T extends BaseMessage> void asyncSend(String topic, T message, Callback callback) {
        asyncSend(topic, null, message, callback);
    }

    public <T extends BaseMessage> void asyncSend(String topic, String tag, T message, Callback callback) {
        if (producer == null) {
            throw new RuntimeException("producer is null");
        }

        ProducerRecord<String, byte[]> record = new ProducerRecord<>(
                topic,
                message.getKey(),
                JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8)
        );
        try {
            producer.send(record, callback);
            log.info("[{}]异步消息[{}]已提交", topic, JSONObject.toJSON(message));
        } catch (Exception e) {
            log.error("异步消息内容：{}, 发送异常：{}", JSONObject.toJSONString(message), e.getMessage());
            throw new RuntimeException(e);
        }
    }

    // 按 topic 存储队列
    private final Map<String, Queue<ProducerRecord<String, byte[]>>> topicQueues = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * 异步发送消息,如果有一百条消息发送一个集合 而且每一秒也发送一次集合内的数据
     */
    public <T extends BaseMessage> void asyncSendToQueue(String topic, T message) {
        // 获取或创建该 topic 的队列
        Queue<ProducerRecord<String, byte[]>> messageQueue = topicQueues.computeIfAbsent(
                topic, key -> new ConcurrentLinkedQueue<>());
        ProducerRecord<String, byte[]> record = new ProducerRecord<>(
                topic,
                message.getKey(),
                JSONObject.toJSONString(message).getBytes(StandardCharsets.UTF_8)
        );
        // 添加消息到队列
        messageQueue.add(record);
        // 达到100条立即发送
        if (messageQueue.size() >= 100) {
            sendBatch(topic);
        }
    }

    /**
     * 批量发送方法
     */
    private synchronized void sendBatch(String topic) {
        List<ProducerRecord<String, byte[]>> batch = new ArrayList<>(100);
        Queue<ProducerRecord<String, byte[]>> messageQueue = topicQueues.get(topic);
        // 获取或创建该 topic 的队列
        for (int i = 0; i < 100; i++) {
            ProducerRecord<String, byte[]> msg = messageQueue.poll();
            if (msg == null) break;
            batch.add(msg);
        }
        if (batch.isEmpty()) return;
        long start = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(batch.size());
        for (ProducerRecord<String, byte[]> msg : batch) {
            producer.send(msg, (metadata, exception) -> {
                if (exception != null) {
                    log.error("发送失败 topic={} error={}", topic, exception.getMessage(), exception);
                }
                latch.countDown();
            });
        }
        boolean await = false;
        try {
            await = latch.await(3, TimeUnit.SECONDS);// 最多等 3 秒
        } catch (InterruptedException ignored) {
        }
        log.info("批量发送完成 topic={} size={} cost={}ms await={}", topic, batch.size(), System.currentTimeMillis() - start, await);
    }
}

