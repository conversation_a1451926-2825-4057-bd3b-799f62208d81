package com.saida.analysis.mq.vlinker;

import java.util.List;

public interface VLinkerMqTemplate {

    String getNameServer();

    <T extends BaseMessage> SendResultWrapper send(String topic, T message);

    <T extends BaseMessage> SendResultWrapper send(String topic, String key, T message);

    SendResultWrapper send(String topic, List<RawMessageWrapper> messages);

    <T extends BaseMessage> void asyncSend(String topic, T message, MqSendCallback callback);

    <T extends BaseMessage> void asyncSend(String topic, String key, T message, MqSendCallback callback);

    <T extends BaseMessage> void asyncSendToQueue(String topic, T messages);

}

