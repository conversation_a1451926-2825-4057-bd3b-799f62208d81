package com.saida.analysis.mq.vlinker;

import lombok.Data;

import java.util.UUID;

/**
 * Mq消息实体，所有消息都需要继承此类
 *
 */
@Data
public abstract class BaseMessage {

    /**
     * 业务键，用于RocketMQ控制台查看消费情况
     */
    protected String key = "key-" + UUID.randomUUID();
    /**
     * 业务键，用于RocketMQ控制台查看消费情况
     */
    protected String tag = null;

    /**
     * 发送消息来源，用于排查问题
     */
    protected String source = "";

    /**
     * 发送时间
     */
    protected Long sendTime = System.currentTimeMillis();

    /**
     * 重试次数，用于判断重试次数，超过重试次数发送异常警告
     */
    protected Integer retryTimes = 0;
}
