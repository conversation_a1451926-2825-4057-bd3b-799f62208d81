package com.saida.analysis.mq.vlinker;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DelayLevelEnum {

    FOUR(4, "30秒"),
    FIVE(5, "1分钟"),
    <PERSON><PERSON>(6, "2分钟"),
    SEVE<PERSON>(7, "3分钟"),
    EIGHT(8, "4分钟"),
    NINE(9, "5分钟"),
    TEN(10, "6分钟");

    private final Integer code;
    private final String msg;

    public static DelayLevelEnum match(Integer code) {
        if (code == null) {
            return null;
        }
        for (DelayLevelEnum item : DelayLevelEnum.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}