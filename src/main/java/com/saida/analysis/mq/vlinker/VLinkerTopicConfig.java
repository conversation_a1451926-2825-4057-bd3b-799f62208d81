package com.saida.analysis.mq.vlinker;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VLinkerTopicConfig {
    private String topic;
    private int writeQueueNums = 4;
    private int readQueueNums = 4;
    private boolean isOrder = false;
    private VLinkerConsumeModel consumeModel = VLinkerConsumeModel.CLUSTERING;
    private VLinkerConsumeFromWhere consumeFromWhere = VLinkerConsumeFromWhere.TIMESTAMP;

    // 兼容扩展项（如 tag、订阅过滤规则等，RocketMQ 独有）
    private String tag = "*";
}
