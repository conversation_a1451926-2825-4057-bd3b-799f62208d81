package com.saida.analysis.pb;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * 定义 TaskExchange 服务，提供不同类型的任务请求
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.68.1)",
    comments = "Source: task_exchange.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class TaskExchangeGrpc {

  private TaskExchangeGrpc() {}

  public static final java.lang.String SERVICE_NAME = "pb.TaskExchange";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask,
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForStreamMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "RequestForStream",
      requestType = com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.class,
      responseType = com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
  public static io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask,
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForStreamMethod() {
    io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForStreamMethod;
    if ((getRequestForStreamMethod = TaskExchangeGrpc.getRequestForStreamMethod) == null) {
      synchronized (TaskExchangeGrpc.class) {
        if ((getRequestForStreamMethod = TaskExchangeGrpc.getRequestForStreamMethod) == null) {
          TaskExchangeGrpc.getRequestForStreamMethod = getRequestForStreamMethod =
              io.grpc.MethodDescriptor.<com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "RequestForStream"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.getDefaultInstance()))
              .setSchemaDescriptor(new TaskExchangeMethodDescriptorSupplier("RequestForStream"))
              .build();
        }
      }
    }
    return getRequestForStreamMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.FileTask,
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForFileMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "RequestForFile",
      requestType = com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.class,
      responseType = com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
  public static io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.FileTask,
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForFileMethod() {
    io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.FileTask, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForFileMethod;
    if ((getRequestForFileMethod = TaskExchangeGrpc.getRequestForFileMethod) == null) {
      synchronized (TaskExchangeGrpc.class) {
        if ((getRequestForFileMethod = TaskExchangeGrpc.getRequestForFileMethod) == null) {
          TaskExchangeGrpc.getRequestForFileMethod = getRequestForFileMethod =
              io.grpc.MethodDescriptor.<com.saida.analysis.pb.TaskExchangeOuterClass.FileTask, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "RequestForFile"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.getDefaultInstance()))
              .setSchemaDescriptor(new TaskExchangeMethodDescriptorSupplier("RequestForFile"))
              .build();
        }
      }
    }
    return getRequestForFileMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask,
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForImageMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "RequestForImage",
      requestType = com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.class,
      responseType = com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask,
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForImageMethod() {
    io.grpc.MethodDescriptor<com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> getRequestForImageMethod;
    if ((getRequestForImageMethod = TaskExchangeGrpc.getRequestForImageMethod) == null) {
      synchronized (TaskExchangeGrpc.class) {
        if ((getRequestForImageMethod = TaskExchangeGrpc.getRequestForImageMethod) == null) {
          TaskExchangeGrpc.getRequestForImageMethod = getRequestForImageMethod =
              io.grpc.MethodDescriptor.<com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "RequestForImage"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.getDefaultInstance()))
              .setSchemaDescriptor(new TaskExchangeMethodDescriptorSupplier("RequestForImage"))
              .build();
        }
      }
    }
    return getRequestForImageMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static TaskExchangeStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TaskExchangeStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TaskExchangeStub>() {
        @java.lang.Override
        public TaskExchangeStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TaskExchangeStub(channel, callOptions);
        }
      };
    return TaskExchangeStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static TaskExchangeBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TaskExchangeBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TaskExchangeBlockingStub>() {
        @java.lang.Override
        public TaskExchangeBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TaskExchangeBlockingStub(channel, callOptions);
        }
      };
    return TaskExchangeBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static TaskExchangeFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TaskExchangeFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TaskExchangeFutureStub>() {
        @java.lang.Override
        public TaskExchangeFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TaskExchangeFutureStub(channel, callOptions);
        }
      };
    return TaskExchangeFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * 定义 TaskExchange 服务，提供不同类型的任务请求
   * </pre>
   */
  public interface AsyncService {

    /**
     * <pre>
     * 请求视频流任务，返回 AI 结果流
     * </pre>
     */
    default void requestForStream(com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask request,
        io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRequestForStreamMethod(), responseObserver);
    }

    /**
     * <pre>
     * 请求文件任务，返回 AI 结果流
     * </pre>
     */
    default void requestForFile(com.saida.analysis.pb.TaskExchangeOuterClass.FileTask request,
        io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRequestForFileMethod(), responseObserver);
    }

    /**
     * <pre>
     * 请求图片任务，返回 AI 结果
     * </pre>
     */
    default void requestForImage(com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask request,
        io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRequestForImageMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service TaskExchange.
   * <pre>
   * 定义 TaskExchange 服务，提供不同类型的任务请求
   * </pre>
   */
  public static abstract class TaskExchangeImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return TaskExchangeGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service TaskExchange.
   * <pre>
   * 定义 TaskExchange 服务，提供不同类型的任务请求
   * </pre>
   */
  public static final class TaskExchangeStub
      extends io.grpc.stub.AbstractAsyncStub<TaskExchangeStub> {
    private TaskExchangeStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TaskExchangeStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TaskExchangeStub(channel, callOptions);
    }

    /**
     * <pre>
     * 请求视频流任务，返回 AI 结果流
     * </pre>
     */
    public void requestForStream(com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask request,
        io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncServerStreamingCall(
          getChannel().newCall(getRequestForStreamMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 请求文件任务，返回 AI 结果流
     * </pre>
     */
    public void requestForFile(com.saida.analysis.pb.TaskExchangeOuterClass.FileTask request,
        io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncServerStreamingCall(
          getChannel().newCall(getRequestForFileMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 请求图片任务，返回 AI 结果
     * </pre>
     */
    public void requestForImage(com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask request,
        io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getRequestForImageMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service TaskExchange.
   * <pre>
   * 定义 TaskExchange 服务，提供不同类型的任务请求
   * </pre>
   */
  public static final class TaskExchangeBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<TaskExchangeBlockingStub> {
    private TaskExchangeBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TaskExchangeBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TaskExchangeBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * 请求视频流任务，返回 AI 结果流
     * </pre>
     */
    public java.util.Iterator<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> requestForStream(
        com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask request) {
      return io.grpc.stub.ClientCalls.blockingServerStreamingCall(
          getChannel(), getRequestForStreamMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 请求文件任务，返回 AI 结果流
     * </pre>
     */
    public java.util.Iterator<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> requestForFile(
        com.saida.analysis.pb.TaskExchangeOuterClass.FileTask request) {
      return io.grpc.stub.ClientCalls.blockingServerStreamingCall(
          getChannel(), getRequestForFileMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 请求图片任务，返回 AI 结果
     * </pre>
     */
    public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply requestForImage(com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getRequestForImageMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service TaskExchange.
   * <pre>
   * 定义 TaskExchange 服务，提供不同类型的任务请求
   * </pre>
   */
  public static final class TaskExchangeFutureStub
      extends io.grpc.stub.AbstractFutureStub<TaskExchangeFutureStub> {
    private TaskExchangeFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TaskExchangeFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TaskExchangeFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * 请求图片任务，返回 AI 结果
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply> requestForImage(
        com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getRequestForImageMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_REQUEST_FOR_STREAM = 0;
  private static final int METHODID_REQUEST_FOR_FILE = 1;
  private static final int METHODID_REQUEST_FOR_IMAGE = 2;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_REQUEST_FOR_STREAM:
          serviceImpl.requestForStream((com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask) request,
              (io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>) responseObserver);
          break;
        case METHODID_REQUEST_FOR_FILE:
          serviceImpl.requestForFile((com.saida.analysis.pb.TaskExchangeOuterClass.FileTask) request,
              (io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>) responseObserver);
          break;
        case METHODID_REQUEST_FOR_IMAGE:
          serviceImpl.requestForImage((com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask) request,
              (io.grpc.stub.StreamObserver<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getRequestForStreamMethod(),
          io.grpc.stub.ServerCalls.asyncServerStreamingCall(
            new MethodHandlers<
              com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask,
              com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>(
                service, METHODID_REQUEST_FOR_STREAM)))
        .addMethod(
          getRequestForFileMethod(),
          io.grpc.stub.ServerCalls.asyncServerStreamingCall(
            new MethodHandlers<
              com.saida.analysis.pb.TaskExchangeOuterClass.FileTask,
              com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>(
                service, METHODID_REQUEST_FOR_FILE)))
        .addMethod(
          getRequestForImageMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask,
              com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply>(
                service, METHODID_REQUEST_FOR_IMAGE)))
        .build();
  }

  private static abstract class TaskExchangeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    TaskExchangeBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("TaskExchange");
    }
  }

  private static final class TaskExchangeFileDescriptorSupplier
      extends TaskExchangeBaseDescriptorSupplier {
    TaskExchangeFileDescriptorSupplier() {}
  }

  private static final class TaskExchangeMethodDescriptorSupplier
      extends TaskExchangeBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    TaskExchangeMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (TaskExchangeGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new TaskExchangeFileDescriptorSupplier())
              .addMethod(getRequestForStreamMethod())
              .addMethod(getRequestForFileMethod())
              .addMethod(getRequestForImageMethod())
              .build();
        }
      }
    }
    return result;
  }
}
