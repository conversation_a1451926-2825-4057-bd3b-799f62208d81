package com.saida.analysis.pb;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.protobuf.ByteString;
import io.grpc.Grpc;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import io.grpc.stub.StreamObserver;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

public class Main {
    public static void main(String[] args) {
        TlsChannelCredentials.Builder tlsBuilder = getBuilder();
        System.out.println("qwer");
        ManagedChannel channel = Grpc.newChannelBuilderForAddress("**************",
                18777, tlsBuilder.build()).build();
        TaskExchangeGrpc.TaskExchangeStub taskExchangeStub = TaskExchangeGrpc.newStub(channel);
        // java.util.Iterator<TaskExchangeOuterClass.OnAIResultGotReply> rspStream = cc.requestForStream(TaskExchangeOuterClass.StreamTask.newBuilder().setStreamUrl("rtmp://*********:1935/live/niao").setArgs(TaskExchangeOuterClass.VideoCommonArgs.newBuilder().setFrameStep(1).setSkipStep(2).setVideoRecordDurationInSeconds(10)).build());
        TaskExchangeOuterClass.StreamTask request = TaskExchangeOuterClass.StreamTask.newBuilder()
                .setArgs(TaskExchangeOuterClass.VideoCommonArgs.newBuilder()
                        .setFrameStep(2)
                        .setSkipStep(2)
                        // .setVideoRecordDurationInSeconds(maxFrameExtractionTime)
                        .build())
                .addAlgorithms(TaskExchangeOuterClass.DetectionAlgorithm.valueOf("H_DET_PERSON"))
                .setStreamUrl("rtsp://*************:5545/live/virtual_001?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.3imsONku7bRpkH9ZGpJsq09uSu-_vMv-KFzD5NQZex4&expired=20250214171743")
                .build();
        taskExchangeStub.requestForStream(request, new StreamObserver<TaskExchangeOuterClass.OnAIResultGotReply>() {
            @Override
            public void onNext(TaskExchangeOuterClass.OnAIResultGotReply onAIResultGotReply) {

                System.out.println("收到告警");

                ByteString imageData = onAIResultGotReply.getImageData();
//                byte[] byteArray = imageData.toByteArray();

                String base64Image = Base64.getEncoder().encodeToString(imageData.toByteArray());
//                System.out.println("s:");
//                System.out.println(s);
                //base64转成本地图片
//                saveImage(imageData);
                handleReply(onAIResultGotReply, base64Image);

            }

            @Override
            public void onError(Throwable throwable) {
                System.out.println("error了");
                throwable.printStackTrace();
            }

            @Override
            public void onCompleted() {
                System.out.println("结束了");
            }
        });

        System.out.println("qwer jiehsu");


        while (true) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static void handleReply(TaskExchangeOuterClass.OnAIResultGotReply reply
            , String base64) {
//        List<CheckIntersectResultDto> intersectList = new ArrayList<>();
        //图像格式
        int fmtValue = reply.getFmtValue();
        //go返回结果
        List<TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> resultList = reply.getResultList();
//        log.info("单次 go返回结果: 图像格式：{},图像数据: 太长了 只打印长度： {},时间戳:{}, resultList的size：{}"
//                , fmtValue, base64.length(),
//                reply.getTimestamp(), resultList.size());
        //算法结果(可能存在多个结果)
        for (TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper result : resultList) {
            List<TaskExchangeOuterClass.OnAIResultGotReply.Result> rectList = result.getRsList();
            for (TaskExchangeOuterClass.OnAIResultGotReply.Result rectResult : rectList) {
                List<Double> rect = new ArrayList<>();
                rect.add((double) rectResult.getRect().getMinX());
                rect.add((double) rectResult.getRect().getMinY());
                rect.add((double) rectResult.getRect().getMaxX());
                rect.add((double) rectResult.getRect().getMaxY());
//                double[][] algorithmPoints = PolygonUtil.parseRectToDoubleArray(rect);
//                CheckIntersectResultDto checkIntersectResultDto = new CheckIntersectResultDto();
//                checkIntersectResultDto.setPoints(algorithmPoints);
//                checkIntersectResultDto.setAlgLabel(rectResult.getLabel());
//                checkIntersectResultDto.setProb(rectResult.getProb());
//                intersectList.add(checkIntersectResultDto);
            }
        }
//        if (!intersectList.isEmpty()) {
//            // 绘图allIndex
////            byte[] decode = PointsUtils.drawRectangle(base64, intersectList, "123", true);
////            byte[] decode = Base64.getDecoder().decode(drawBase64Image);
////            saveImage(decode);
//        }


    }

    public static void saveImage(byte[] byteArray) {
        // 确保保存路径存在
        File dir = new File("tempImg");
        if (!dir.exists()) {
            dir.mkdirs();  // 创建目录
        }
        // 生成文件名
        String fileName = "tempImg/" + IdWorker.getId() + ".jpg";
        // 使用 try-with-resources 来确保资源被正确关闭
        try (FileOutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(byteArray);
            System.out.println("图片保存成功：" + fileName);
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("保存图片时发生错误");
        }
    }

    private static TlsChannelCredentials.Builder getBuilder() {
        TlsChannelCredentials.Builder tlsBuilder = TlsChannelCredentials.newBuilder();
        try {
            tlsBuilder.keyManager(new File("/Users/<USER>/Documents/ideaProject/algorithm-analysis/aap-modules/aap-decoder/src/main/resources/grpc/127.crt"), new File("/Users/<USER>/Documents/ideaProject/algorithm-analysis/aap-modules/aap-decoder/src/main/resources/grpc/127.key"))
//                        // 配置服务器的证书（或者CA证书链）
                    .trustManager(new File("/Users/<USER>/Documents/ideaProject/algorithm-analysis/aap-modules/aap-decoder/src/main/resources/grpc/ca.crt")); // 加入CA证书链
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return tlsBuilder;
    }
}
