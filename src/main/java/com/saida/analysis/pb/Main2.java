package com.saida.analysis.pb;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.protobuf.ByteString;
import io.grpc.Grpc;
import io.grpc.ManagedChannel;
import io.grpc.TlsChannelCredentials;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Slf4j
public class Main2 {
    public static void main(String[] args) {
        TlsChannelCredentials.Builder tlsBuilder = getBuilder();
        System.out.println("qwer");
        ManagedChannel channel = Grpc.newChannelBuilderForAddress("**************",
                18777, tlsBuilder.build()).build();
        TaskExchangeGrpc.TaskExchangeBlockingStub taskExchangeStub = TaskExchangeGrpc.newBlockingStub(channel);
        String url = "https://test-vlinker-minio-api.sdccx.cn:48801/v-linker-algorithm/file/2024/12/04/877474600cc2415ea8c02852d50b8c6a.png";
        BufferedImage image = downloadImage(url);
        if (image == null) {
            log.error("图片下载失败：{}", url);
            return;
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ByteString byteString = null;
        String base64 = "";
        try {
            //用jpg 会有损失
            ImageIO.write(image, "png", outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            byteString = ByteString.copyFrom(imageBytes);
            base64 = Base64.getEncoder().encodeToString(imageBytes);

//            saveImage(imageBytes);
        } catch (IOException e) {
            log.error("图片读取失败 url:{}", url);
        }
        TaskExchangeOuterClass.ImageFormat imgFormat = null;
        if (url.endsWith("png")) {
            imgFormat = TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_PNG;
        } else if (url.endsWith("jpg")) {
            imgFormat = TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_JPEG;
        } else {
            log.error("图片格式不支持：{}", url);
            return;
        }
        TaskExchangeOuterClass.DetectionAlgorithm value = TaskExchangeOuterClass.DetectionAlgorithm.N_DET_PERDUTY;
        TaskExchangeOuterClass.ImageTask request = TaskExchangeOuterClass.ImageTask.newBuilder()
                .addAlgorithms(value)
                .setImg(byteString)
                .setImgFormat(imgFormat)
                .build();

        // 获取或创建异步 gRPC 通道
        TaskExchangeOuterClass.OnAIResultGotReply onAIResultGotReply = taskExchangeStub.requestForImage(request);
        handleReply(onAIResultGotReply, base64);


    }


    public static void handleReply(TaskExchangeOuterClass.OnAIResultGotReply reply
            , String base64) {
//        List<CheckIntersectResultDto> intersectList = new ArrayList<>();
        //图像格式
        int fmtValue = reply.getFmtValue();
        //go返回结果
        List<TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> resultList = reply.getResultList();
        log.info("单次 go返回结果: 图像格式：{},图像数据: 太长了 只打印长度： {},时间戳:{}, resultList的size：{}"
                , fmtValue, base64.length(),
                reply.getTimestamp(), resultList.size());
        //算法结果(可能存在多个结果)
        for (TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper result : resultList) {
            List<TaskExchangeOuterClass.OnAIResultGotReply.Result> rectList = result.getRsList();
            for (TaskExchangeOuterClass.OnAIResultGotReply.Result rectResult : rectList) {
//                List<Double> rect = new ArrayList<>();
//                rect.add((double) rectResult.getRect().getMinX());
//                rect.add((double) rectResult.getRect().getMinY());
//                rect.add((double) rectResult.getRect().getMaxX());
//                rect.add((double) rectResult.getRect().getMaxY());
//                double[][] algorithmPoints = PolygonUtil.parseRectToDoubleArray(rect);
//                CheckIntersectResultDto checkIntersectResultDto = new CheckIntersectResultDto();
//                checkIntersectResultDto.setPoints(algorithmPoints);
//                checkIntersectResultDto.setAlgLabel(rectResult.getLabel());
//                checkIntersectResultDto.setProb(rectResult.getProb());
//                intersectList.add(checkIntersectResultDto);
            }
        }

//        List<Double> rect = new ArrayList<>();
//        rect.add(0D);
//        rect.add(0D);
//        rect.add(100D);
//        rect.add(200D);
//        double[][] algorithmPoints = PolygonUtil.parseRectToDoubleArray(rect);
//        CheckIntersectResultDto checkIntersectResultDto = new CheckIntersectResultDto();
//        checkIntersectResultDto.setName("name");
//        checkIntersectResultDto.setType(0);
//        checkIntersectResultDto.setPoints(algorithmPoints);
//        checkIntersectResultDto.setAlgLabel(1);
//        checkIntersectResultDto.setProb(2D);
//        intersectList.add(checkIntersectResultDto);

//        if (!intersectList.isEmpty()) {
//            // 绘图allIndex
////            byte[] decode = PointsUtils.drawRectangle(base64, intersectList, "123", true);
////            byte[] decode = Base64.getDecoder().decode(drawBase64Image);
////            saveImage(decode);
//        }


    }


    public static void saveImage(byte[] byteArray) {
        // 确保保存路径存在
        File dir = new File("tempImg");
        if (!dir.exists()) {
            dir.mkdirs();  // 创建目录
        }
        // 生成文件名
        String fileName = "tempImg/" + IdWorker.getId() + ".jpg";
        // 使用 try-with-resources 来确保资源被正确关闭
        try (FileOutputStream outputStream = new FileOutputStream(fileName)) {
            outputStream.write(byteArray);
            System.out.println("图片保存成功：" + fileName);
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("保存图片时发生错误");
        }
    }

    private static TlsChannelCredentials.Builder getBuilder() {
        TlsChannelCredentials.Builder tlsBuilder = TlsChannelCredentials.newBuilder();
        try {
            tlsBuilder.keyManager(new File("/Users/<USER>/Documents/ideaProject/algorithm-analysis/aap-modules/aap-decoder/src/main/resources/grpc/127.crt"), new File("/Users/<USER>/Documents/ideaProject/algorithm-analysis/aap-modules/aap-decoder/src/main/resources/grpc/127.key"))
//                        // 配置服务器的证书（或者CA证书链）
                    .trustManager(new File("/Users/<USER>/Documents/ideaProject/algorithm-analysis/aap-modules/aap-decoder/src/main/resources/grpc/ca.crt")); // 加入CA证书链
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return tlsBuilder;
    }


    public static BufferedImage downloadImage(String imgSrc) {
        if (StringUtils.isEmpty(imgSrc)) {
            log.error("图片为空：{}", imgSrc);
            return null;
        }
        try {
            URL url = new URL(imgSrc);
            return ImageIO.read(url);
        } catch (IOException e) {
            log.error("下载图片失败：{}", imgSrc, e);
            return null;
        }
    }

}
