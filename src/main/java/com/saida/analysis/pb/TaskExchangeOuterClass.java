// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: task_exchange.proto

// Protobuf Java Version: 3.25.5
package com.saida.analysis.pb;

public final class TaskExchangeOuterClass {
  private TaskExchangeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   * 枚举类型：表示支持的图片格式
   * </pre>
   *
   * Protobuf enum {@code pb.ImageFormat}
   */
  public enum ImageFormat
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 未知格式
     * </pre>
     *
     * <code>IMAGE_FORMAT_UNKNOWN = 0;</code>
     */
    IMAGE_FORMAT_UNKNOWN(0),
    /**
     * <pre>
     * PNG 格式
     * </pre>
     *
     * <code>IMAGE_FORMAT_PNG = 1;</code>
     */
    IMAGE_FORMAT_PNG(1),
    /**
     * <pre>
     * JPEG 格式
     * </pre>
     *
     * <code>IMAGE_FORMAT_JPEG = 2;</code>
     */
    IMAGE_FORMAT_JPEG(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 未知格式
     * </pre>
     *
     * <code>IMAGE_FORMAT_UNKNOWN = 0;</code>
     */
    public static final int IMAGE_FORMAT_UNKNOWN_VALUE = 0;
    /**
     * <pre>
     * PNG 格式
     * </pre>
     *
     * <code>IMAGE_FORMAT_PNG = 1;</code>
     */
    public static final int IMAGE_FORMAT_PNG_VALUE = 1;
    /**
     * <pre>
     * JPEG 格式
     * </pre>
     *
     * <code>IMAGE_FORMAT_JPEG = 2;</code>
     */
    public static final int IMAGE_FORMAT_JPEG_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static ImageFormat valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static ImageFormat forNumber(int value) {
      switch (value) {
        case 0: return IMAGE_FORMAT_UNKNOWN;
        case 1: return IMAGE_FORMAT_PNG;
        case 2: return IMAGE_FORMAT_JPEG;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ImageFormat>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ImageFormat> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ImageFormat>() {
            public ImageFormat findValueByNumber(int number) {
              return ImageFormat.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.getDescriptor().getEnumTypes().get(0);
    }

    private static final ImageFormat[] VALUES = values();

    public static ImageFormat valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ImageFormat(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:pb.ImageFormat)
  }

  /**
   * <pre>
   * 枚举类型：表示支持的检测算法类型
   * </pre>
   *
   * Protobuf enum {@code pb.DetectionAlgorithm}
   */
  public enum DetectionAlgorithm
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 未知检测算法
     * </pre>
     *
     * <code>DETECTION_ALGORITHM_UNKNOWN = 0;</code>
     */
    DETECTION_ALGORITHM_UNKNOWN(0),
    /**
     * <pre>
     * "普通_消防通道占用检测",
     * </pre>
     *
     * <code>N_DET_OCCU = 1;</code>
     */
    N_DET_OCCU(1),
    /**
     * <pre>
     * "普通_人员在岗",
     * </pre>
     *
     * <code>N_DET_PERDUTY = 2;</code>
     */
    N_DET_PERDUTY(2),
    /**
     * <pre>
     * "普通_安全生产检测",
     * </pre>
     *
     * <code>N_DET_SAFETY = 3;</code>
     */
    N_DET_SAFETY(3),
    /**
     * <pre>
     * "无人机_车辆检测",
     * </pre>
     *
     * <code>U_DET_CAR = 4;</code>
     */
    U_DET_CAR(4),
    /**
     * <pre>
     * "无人机_水面漂浮物检测",
     * </pre>
     *
     * <code>U_DET_FLOAT = 5;</code>
     */
    U_DET_FLOAT(5),
    /**
     * <pre>
     * "无人机_垃圾检测",
     * </pre>
     *
     * <code>U_DET_TRASH = 6;</code>
     */
    U_DET_TRASH(6),
    /**
     * <pre>
     * "高点_鸟类检测_安徽",
     * </pre>
     *
     * <code>H_DET_BIRD_AH = 7;</code>
     */
    H_DET_BIRD_AH(7),
    /**
     * <pre>
     * "高点_车辆检测",
     * </pre>
     *
     * <code>H_DET_CAR = 8;</code>
     */
    H_DET_CAR(8),
    /**
     * <pre>
     * "高点_烟火检测",
     * </pre>
     *
     * <code>H_DET_FIRE = 9;</code>
     */
    H_DET_FIRE(9),
    /**
     * <pre>
     * "高点_违法捕捞",
     * </pre>
     *
     * <code>H_DET_ILLCATCH = 10;</code>
     */
    H_DET_ILLCATCH(10),
    /**
     * <pre>
     * "高点_违法搭建",
     * </pre>
     *
     * <code>H_DET_ILLCONST = 11;</code>
     */
    H_DET_ILLCONST(11),
    /**
     * <pre>
     * "高点_非法垂钓",
     * </pre>
     *
     * <code>H_DET_ILLFISH = 12;</code>
     */
    H_DET_ILLFISH(12),
    /**
     * <pre>
     * "高点_土壤裸露",
     * </pre>
     *
     * <code>H_DET_LAND = 13;</code>
     */
    H_DET_LAND(13),
    /**
     * <pre>
     * "高点_人员检测",
     * </pre>
     *
     * <code>H_DET_PERSON = 14;</code>
     */
    H_DET_PERSON(14),
    /**
     * <pre>
     * "高点_船只检测",
     * </pre>
     *
     * <code>H_DET_SHIP = 15;</code>
     */
    H_DET_SHIP(15),
    /**
     * <pre>
     * "高点_垃圾裸露",
     * </pre>
     *
     * <code>H_DET_TRASH = 16;</code>
     */
    H_DET_TRASH(16),
    /**
     * <pre>
     * "普通_吸烟",
     * </pre>
     *
     * <code>N_DET_CIG = 17;</code>
     */
    N_DET_CIG(17),
    /**
     * <pre>
     * "普通_电梯中电瓶车检测",
     * </pre>
     *
     * <code>N_DET_ELE = 18;</code>
     */
    N_DET_ELE(18),
    /**
     * <pre>
     * "普通_人员倒地",
     * </pre>
     *
     * <code>N_DET_FALL = 19;</code>
     */
    N_DET_FALL(19),
    /**
     * <pre>
     * "普通_烟火检测",
     * </pre>
     *
     * <code>N_DET_FIRE = 20;</code>
     */
    N_DET_FIRE(20),
    /**
     * <pre>
     * "普通_灭火器检测",
     * </pre>
     *
     * <code>N_DET_FIREEX = 21;</code>
     */
    N_DET_FIREEX(21),
    /**
     * <pre>
     * "普通_人数统计",
     * </pre>
     *
     * <code>N_DET_PERCOUNT = 22;</code>
     */
    N_DET_PERCOUNT(22),
    /**
     * <pre>
     * "普通_打电话",
     * </pre>
     *
     * <code>N_DET_PHONE = 23;</code>
     */
    N_DET_PHONE(23),
    /**
     * <pre>
     * "无人机_工程车检测",
     * </pre>
     *
     * <code>U_DET_CONVEH = 24;</code>
     */
    U_DET_CONVEH(24),
    /**
     * <pre>
     * "无人机_人车检测"
     * </pre>
     *
     * <code>U_DET_PERCAR = 25;</code>
     */
    U_DET_PERCAR(25),
    /**
     * <pre>
     * "普通_工程车--------工程车辆",
     * </pre>
     *
     * <code>N_DET_ENGINEVEICHLE = 26;</code>
     */
    N_DET_ENGINEVEICHLE(26),
    /**
     * <pre>
     * "无人机_船只检测----非法船只",
     * </pre>
     *
     * <code>U_DET_SHIP = 27;</code>
     */
    U_DET_SHIP(27),
    /**
     * <pre>
     * "无人机_烟火检测----烟火识别"
     * </pre>
     *
     * <code>U_DET_FIRE = 28;</code>
     */
    U_DET_FIRE(28),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 未知检测算法
     * </pre>
     *
     * <code>DETECTION_ALGORITHM_UNKNOWN = 0;</code>
     */
    public static final int DETECTION_ALGORITHM_UNKNOWN_VALUE = 0;
    /**
     * <pre>
     * "普通_消防通道占用检测",
     * </pre>
     *
     * <code>N_DET_OCCU = 1;</code>
     */
    public static final int N_DET_OCCU_VALUE = 1;
    /**
     * <pre>
     * "普通_人员在岗",
     * </pre>
     *
     * <code>N_DET_PERDUTY = 2;</code>
     */
    public static final int N_DET_PERDUTY_VALUE = 2;
    /**
     * <pre>
     * "普通_安全生产检测",
     * </pre>
     *
     * <code>N_DET_SAFETY = 3;</code>
     */
    public static final int N_DET_SAFETY_VALUE = 3;
    /**
     * <pre>
     * "无人机_车辆检测",
     * </pre>
     *
     * <code>U_DET_CAR = 4;</code>
     */
    public static final int U_DET_CAR_VALUE = 4;
    /**
     * <pre>
     * "无人机_水面漂浮物检测",
     * </pre>
     *
     * <code>U_DET_FLOAT = 5;</code>
     */
    public static final int U_DET_FLOAT_VALUE = 5;
    /**
     * <pre>
     * "无人机_垃圾检测",
     * </pre>
     *
     * <code>U_DET_TRASH = 6;</code>
     */
    public static final int U_DET_TRASH_VALUE = 6;
    /**
     * <pre>
     * "高点_鸟类检测_安徽",
     * </pre>
     *
     * <code>H_DET_BIRD_AH = 7;</code>
     */
    public static final int H_DET_BIRD_AH_VALUE = 7;
    /**
     * <pre>
     * "高点_车辆检测",
     * </pre>
     *
     * <code>H_DET_CAR = 8;</code>
     */
    public static final int H_DET_CAR_VALUE = 8;
    /**
     * <pre>
     * "高点_烟火检测",
     * </pre>
     *
     * <code>H_DET_FIRE = 9;</code>
     */
    public static final int H_DET_FIRE_VALUE = 9;
    /**
     * <pre>
     * "高点_违法捕捞",
     * </pre>
     *
     * <code>H_DET_ILLCATCH = 10;</code>
     */
    public static final int H_DET_ILLCATCH_VALUE = 10;
    /**
     * <pre>
     * "高点_违法搭建",
     * </pre>
     *
     * <code>H_DET_ILLCONST = 11;</code>
     */
    public static final int H_DET_ILLCONST_VALUE = 11;
    /**
     * <pre>
     * "高点_非法垂钓",
     * </pre>
     *
     * <code>H_DET_ILLFISH = 12;</code>
     */
    public static final int H_DET_ILLFISH_VALUE = 12;
    /**
     * <pre>
     * "高点_土壤裸露",
     * </pre>
     *
     * <code>H_DET_LAND = 13;</code>
     */
    public static final int H_DET_LAND_VALUE = 13;
    /**
     * <pre>
     * "高点_人员检测",
     * </pre>
     *
     * <code>H_DET_PERSON = 14;</code>
     */
    public static final int H_DET_PERSON_VALUE = 14;
    /**
     * <pre>
     * "高点_船只检测",
     * </pre>
     *
     * <code>H_DET_SHIP = 15;</code>
     */
    public static final int H_DET_SHIP_VALUE = 15;
    /**
     * <pre>
     * "高点_垃圾裸露",
     * </pre>
     *
     * <code>H_DET_TRASH = 16;</code>
     */
    public static final int H_DET_TRASH_VALUE = 16;
    /**
     * <pre>
     * "普通_吸烟",
     * </pre>
     *
     * <code>N_DET_CIG = 17;</code>
     */
    public static final int N_DET_CIG_VALUE = 17;
    /**
     * <pre>
     * "普通_电梯中电瓶车检测",
     * </pre>
     *
     * <code>N_DET_ELE = 18;</code>
     */
    public static final int N_DET_ELE_VALUE = 18;
    /**
     * <pre>
     * "普通_人员倒地",
     * </pre>
     *
     * <code>N_DET_FALL = 19;</code>
     */
    public static final int N_DET_FALL_VALUE = 19;
    /**
     * <pre>
     * "普通_烟火检测",
     * </pre>
     *
     * <code>N_DET_FIRE = 20;</code>
     */
    public static final int N_DET_FIRE_VALUE = 20;
    /**
     * <pre>
     * "普通_灭火器检测",
     * </pre>
     *
     * <code>N_DET_FIREEX = 21;</code>
     */
    public static final int N_DET_FIREEX_VALUE = 21;
    /**
     * <pre>
     * "普通_人数统计",
     * </pre>
     *
     * <code>N_DET_PERCOUNT = 22;</code>
     */
    public static final int N_DET_PERCOUNT_VALUE = 22;
    /**
     * <pre>
     * "普通_打电话",
     * </pre>
     *
     * <code>N_DET_PHONE = 23;</code>
     */
    public static final int N_DET_PHONE_VALUE = 23;
    /**
     * <pre>
     * "无人机_工程车检测",
     * </pre>
     *
     * <code>U_DET_CONVEH = 24;</code>
     */
    public static final int U_DET_CONVEH_VALUE = 24;
    /**
     * <pre>
     * "无人机_人车检测"
     * </pre>
     *
     * <code>U_DET_PERCAR = 25;</code>
     */
    public static final int U_DET_PERCAR_VALUE = 25;
    /**
     * <pre>
     * "普通_工程车--------工程车辆",
     * </pre>
     *
     * <code>N_DET_ENGINEVEICHLE = 26;</code>
     */
    public static final int N_DET_ENGINEVEICHLE_VALUE = 26;
    /**
     * <pre>
     * "无人机_船只检测----非法船只",
     * </pre>
     *
     * <code>U_DET_SHIP = 27;</code>
     */
    public static final int U_DET_SHIP_VALUE = 27;
    /**
     * <pre>
     * "无人机_烟火检测----烟火识别"
     * </pre>
     *
     * <code>U_DET_FIRE = 28;</code>
     */
    public static final int U_DET_FIRE_VALUE = 28;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static DetectionAlgorithm valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static DetectionAlgorithm forNumber(int value) {
      switch (value) {
        case 0: return DETECTION_ALGORITHM_UNKNOWN;
        case 1: return N_DET_OCCU;
        case 2: return N_DET_PERDUTY;
        case 3: return N_DET_SAFETY;
        case 4: return U_DET_CAR;
        case 5: return U_DET_FLOAT;
        case 6: return U_DET_TRASH;
        case 7: return H_DET_BIRD_AH;
        case 8: return H_DET_CAR;
        case 9: return H_DET_FIRE;
        case 10: return H_DET_ILLCATCH;
        case 11: return H_DET_ILLCONST;
        case 12: return H_DET_ILLFISH;
        case 13: return H_DET_LAND;
        case 14: return H_DET_PERSON;
        case 15: return H_DET_SHIP;
        case 16: return H_DET_TRASH;
        case 17: return N_DET_CIG;
        case 18: return N_DET_ELE;
        case 19: return N_DET_FALL;
        case 20: return N_DET_FIRE;
        case 21: return N_DET_FIREEX;
        case 22: return N_DET_PERCOUNT;
        case 23: return N_DET_PHONE;
        case 24: return U_DET_CONVEH;
        case 25: return U_DET_PERCAR;
        case 26: return N_DET_ENGINEVEICHLE;
        case 27: return U_DET_SHIP;
        case 28: return U_DET_FIRE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<DetectionAlgorithm>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        DetectionAlgorithm> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<DetectionAlgorithm>() {
            public DetectionAlgorithm findValueByNumber(int number) {
              return DetectionAlgorithm.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.getDescriptor().getEnumTypes().get(1);
    }

    private static final DetectionAlgorithm[] VALUES = values();

    public static DetectionAlgorithm valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private DetectionAlgorithm(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:pb.DetectionAlgorithm)
  }

  public interface VideoCommonArgsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pb.VideoCommonArgs)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
     * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
     * </pre>
     *
     * <code>bool key_frame_only = 1;</code>
     * @return The keyFrameOnly.
     */
    boolean getKeyFrameOnly();

    /**
     * <pre>
     * 帧步长：指定处理视频时连续多少帧（单位：帧）
     * </pre>
     *
     * <code>uint32 frame_step = 2;</code>
     * @return The frameStep.
     */
    int getFrameStep();

    /**
     * <pre>
     * 跳过的帧数：指定在处理视频时跳过的帧数
     * </pre>
     *
     * <code>uint32 skip_step = 3;</code>
     * @return The skipStep.
     */
    int getSkipStep();

    /**
     * <pre>
     * 视频录制时长：单位为秒，指定录制的时长
     * </pre>
     *
     * <code>uint32 video_record_duration_in_seconds = 4;</code>
     * @return The videoRecordDurationInSeconds.
     */
    int getVideoRecordDurationInSeconds();

    /**
     * <pre>
     * 上报间隔: 单位为毫秒, 如果距离上次上报不超过这个值,则会丢弃,当然 这是非常特定场景的设计
     * 比如人员检测,如果是这样的情况下,猥琐男子周某跟随女神ABC,同时经过一个IPC,这时候,
     * 女神ABC被拍到了,猥琐男子被拍到了,分析到了,丢弃了
     * 所以这个仅在 key_frame_only 为true 时读取,比如看一个厨房哪个B没带帽子,这种消极的场景中这样的操作是有效的
     * </pre>
     *
     * <code>uint32 callback_interval_in_mill_seconds = 5;</code>
     * @return The callbackIntervalInMillSeconds.
     */
    int getCallbackIntervalInMillSeconds();
  }
  /**
   * <pre>
   * 通用视频参数，适用于不同的视频任务
   * </pre>
   *
   * Protobuf type {@code pb.VideoCommonArgs}
   */
  public static final class VideoCommonArgs extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pb.VideoCommonArgs)
      VideoCommonArgsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use VideoCommonArgs.newBuilder() to construct.
    private VideoCommonArgs(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private VideoCommonArgs() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new VideoCommonArgs();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_VideoCommonArgs_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_VideoCommonArgs_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.class, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder.class);
    }

    public static final int KEY_FRAME_ONLY_FIELD_NUMBER = 1;
    private boolean keyFrameOnly_ = false;
    /**
     * <pre>
     * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
     * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
     * </pre>
     *
     * <code>bool key_frame_only = 1;</code>
     * @return The keyFrameOnly.
     */
    @java.lang.Override
    public boolean getKeyFrameOnly() {
      return keyFrameOnly_;
    }

    public static final int FRAME_STEP_FIELD_NUMBER = 2;
    private int frameStep_ = 0;
    /**
     * <pre>
     * 帧步长：指定处理视频时连续多少帧（单位：帧）
     * </pre>
     *
     * <code>uint32 frame_step = 2;</code>
     * @return The frameStep.
     */
    @java.lang.Override
    public int getFrameStep() {
      return frameStep_;
    }

    public static final int SKIP_STEP_FIELD_NUMBER = 3;
    private int skipStep_ = 0;
    /**
     * <pre>
     * 跳过的帧数：指定在处理视频时跳过的帧数
     * </pre>
     *
     * <code>uint32 skip_step = 3;</code>
     * @return The skipStep.
     */
    @java.lang.Override
    public int getSkipStep() {
      return skipStep_;
    }

    public static final int VIDEO_RECORD_DURATION_IN_SECONDS_FIELD_NUMBER = 4;
    private int videoRecordDurationInSeconds_ = 0;
    /**
     * <pre>
     * 视频录制时长：单位为秒，指定录制的时长
     * </pre>
     *
     * <code>uint32 video_record_duration_in_seconds = 4;</code>
     * @return The videoRecordDurationInSeconds.
     */
    @java.lang.Override
    public int getVideoRecordDurationInSeconds() {
      return videoRecordDurationInSeconds_;
    }

    public static final int CALLBACK_INTERVAL_IN_MILL_SECONDS_FIELD_NUMBER = 5;
    private int callbackIntervalInMillSeconds_ = 0;
    /**
     * <pre>
     * 上报间隔: 单位为毫秒, 如果距离上次上报不超过这个值,则会丢弃,当然 这是非常特定场景的设计
     * 比如人员检测,如果是这样的情况下,猥琐男子周某跟随女神ABC,同时经过一个IPC,这时候,
     * 女神ABC被拍到了,猥琐男子被拍到了,分析到了,丢弃了
     * 所以这个仅在 key_frame_only 为true 时读取,比如看一个厨房哪个B没带帽子,这种消极的场景中这样的操作是有效的
     * </pre>
     *
     * <code>uint32 callback_interval_in_mill_seconds = 5;</code>
     * @return The callbackIntervalInMillSeconds.
     */
    @java.lang.Override
    public int getCallbackIntervalInMillSeconds() {
      return callbackIntervalInMillSeconds_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (keyFrameOnly_ != false) {
        output.writeBool(1, keyFrameOnly_);
      }
      if (frameStep_ != 0) {
        output.writeUInt32(2, frameStep_);
      }
      if (skipStep_ != 0) {
        output.writeUInt32(3, skipStep_);
      }
      if (videoRecordDurationInSeconds_ != 0) {
        output.writeUInt32(4, videoRecordDurationInSeconds_);
      }
      if (callbackIntervalInMillSeconds_ != 0) {
        output.writeUInt32(5, callbackIntervalInMillSeconds_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (keyFrameOnly_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, keyFrameOnly_);
      }
      if (frameStep_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, frameStep_);
      }
      if (skipStep_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, skipStep_);
      }
      if (videoRecordDurationInSeconds_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, videoRecordDurationInSeconds_);
      }
      if (callbackIntervalInMillSeconds_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, callbackIntervalInMillSeconds_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs)) {
        return super.equals(obj);
      }
      com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs other = (com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs) obj;

      if (getKeyFrameOnly()
          != other.getKeyFrameOnly()) return false;
      if (getFrameStep()
          != other.getFrameStep()) return false;
      if (getSkipStep()
          != other.getSkipStep()) return false;
      if (getVideoRecordDurationInSeconds()
          != other.getVideoRecordDurationInSeconds()) return false;
      if (getCallbackIntervalInMillSeconds()
          != other.getCallbackIntervalInMillSeconds()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + KEY_FRAME_ONLY_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getKeyFrameOnly());
      hash = (37 * hash) + FRAME_STEP_FIELD_NUMBER;
      hash = (53 * hash) + getFrameStep();
      hash = (37 * hash) + SKIP_STEP_FIELD_NUMBER;
      hash = (53 * hash) + getSkipStep();
      hash = (37 * hash) + VIDEO_RECORD_DURATION_IN_SECONDS_FIELD_NUMBER;
      hash = (53 * hash) + getVideoRecordDurationInSeconds();
      hash = (37 * hash) + CALLBACK_INTERVAL_IN_MILL_SECONDS_FIELD_NUMBER;
      hash = (53 * hash) + getCallbackIntervalInMillSeconds();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 通用视频参数，适用于不同的视频任务
     * </pre>
     *
     * Protobuf type {@code pb.VideoCommonArgs}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pb.VideoCommonArgs)
        com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_VideoCommonArgs_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_VideoCommonArgs_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.class, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder.class);
      }

      // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        keyFrameOnly_ = false;
        frameStep_ = 0;
        skipStep_ = 0;
        videoRecordDurationInSeconds_ = 0;
        callbackIntervalInMillSeconds_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_VideoCommonArgs_descriptor;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getDefaultInstanceForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance();
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs build() {
        com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs buildPartial() {
        com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs result = new com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.keyFrameOnly_ = keyFrameOnly_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.frameStep_ = frameStep_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.skipStep_ = skipStep_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.videoRecordDurationInSeconds_ = videoRecordDurationInSeconds_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.callbackIntervalInMillSeconds_ = callbackIntervalInMillSeconds_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs) {
          return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs other) {
        if (other == com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance()) return this;
        if (other.getKeyFrameOnly() != false) {
          setKeyFrameOnly(other.getKeyFrameOnly());
        }
        if (other.getFrameStep() != 0) {
          setFrameStep(other.getFrameStep());
        }
        if (other.getSkipStep() != 0) {
          setSkipStep(other.getSkipStep());
        }
        if (other.getVideoRecordDurationInSeconds() != 0) {
          setVideoRecordDurationInSeconds(other.getVideoRecordDurationInSeconds());
        }
        if (other.getCallbackIntervalInMillSeconds() != 0) {
          setCallbackIntervalInMillSeconds(other.getCallbackIntervalInMillSeconds());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                keyFrameOnly_ = input.readBool();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                frameStep_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                skipStep_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                videoRecordDurationInSeconds_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                callbackIntervalInMillSeconds_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private boolean keyFrameOnly_ ;
      /**
       * <pre>
       * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
       * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
       * </pre>
       *
       * <code>bool key_frame_only = 1;</code>
       * @return The keyFrameOnly.
       */
      @java.lang.Override
      public boolean getKeyFrameOnly() {
        return keyFrameOnly_;
      }
      /**
       * <pre>
       * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
       * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
       * </pre>
       *
       * <code>bool key_frame_only = 1;</code>
       * @param value The keyFrameOnly to set.
       * @return This builder for chaining.
       */
      public Builder setKeyFrameOnly(boolean value) {

        keyFrameOnly_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
       * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
       * </pre>
       *
       * <code>bool key_frame_only = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKeyFrameOnly() {
        bitField0_ = (bitField0_ & ~0x00000001);
        keyFrameOnly_ = false;
        onChanged();
        return this;
      }

      private int frameStep_ ;
      /**
       * <pre>
       * 帧步长：指定处理视频时连续多少帧（单位：帧）
       * </pre>
       *
       * <code>uint32 frame_step = 2;</code>
       * @return The frameStep.
       */
      @java.lang.Override
      public int getFrameStep() {
        return frameStep_;
      }
      /**
       * <pre>
       * 帧步长：指定处理视频时连续多少帧（单位：帧）
       * </pre>
       *
       * <code>uint32 frame_step = 2;</code>
       * @param value The frameStep to set.
       * @return This builder for chaining.
       */
      public Builder setFrameStep(int value) {

        frameStep_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 帧步长：指定处理视频时连续多少帧（单位：帧）
       * </pre>
       *
       * <code>uint32 frame_step = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFrameStep() {
        bitField0_ = (bitField0_ & ~0x00000002);
        frameStep_ = 0;
        onChanged();
        return this;
      }

      private int skipStep_ ;
      /**
       * <pre>
       * 跳过的帧数：指定在处理视频时跳过的帧数
       * </pre>
       *
       * <code>uint32 skip_step = 3;</code>
       * @return The skipStep.
       */
      @java.lang.Override
      public int getSkipStep() {
        return skipStep_;
      }
      /**
       * <pre>
       * 跳过的帧数：指定在处理视频时跳过的帧数
       * </pre>
       *
       * <code>uint32 skip_step = 3;</code>
       * @param value The skipStep to set.
       * @return This builder for chaining.
       */
      public Builder setSkipStep(int value) {

        skipStep_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 跳过的帧数：指定在处理视频时跳过的帧数
       * </pre>
       *
       * <code>uint32 skip_step = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkipStep() {
        bitField0_ = (bitField0_ & ~0x00000004);
        skipStep_ = 0;
        onChanged();
        return this;
      }

      private int videoRecordDurationInSeconds_ ;
      /**
       * <pre>
       * 视频录制时长：单位为秒，指定录制的时长
       * </pre>
       *
       * <code>uint32 video_record_duration_in_seconds = 4;</code>
       * @return The videoRecordDurationInSeconds.
       */
      @java.lang.Override
      public int getVideoRecordDurationInSeconds() {
        return videoRecordDurationInSeconds_;
      }
      /**
       * <pre>
       * 视频录制时长：单位为秒，指定录制的时长
       * </pre>
       *
       * <code>uint32 video_record_duration_in_seconds = 4;</code>
       * @param value The videoRecordDurationInSeconds to set.
       * @return This builder for chaining.
       */
      public Builder setVideoRecordDurationInSeconds(int value) {

        videoRecordDurationInSeconds_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频录制时长：单位为秒，指定录制的时长
       * </pre>
       *
       * <code>uint32 video_record_duration_in_seconds = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearVideoRecordDurationInSeconds() {
        bitField0_ = (bitField0_ & ~0x00000008);
        videoRecordDurationInSeconds_ = 0;
        onChanged();
        return this;
      }

      private int callbackIntervalInMillSeconds_ ;
      /**
       * <pre>
       * 上报间隔: 单位为毫秒, 如果距离上次上报不超过这个值,则会丢弃,当然 这是非常特定场景的设计
       * 比如人员检测,如果是这样的情况下,猥琐男子周某跟随女神ABC,同时经过一个IPC,这时候,
       * 女神ABC被拍到了,猥琐男子被拍到了,分析到了,丢弃了
       * 所以这个仅在 key_frame_only 为true 时读取,比如看一个厨房哪个B没带帽子,这种消极的场景中这样的操作是有效的
       * </pre>
       *
       * <code>uint32 callback_interval_in_mill_seconds = 5;</code>
       * @return The callbackIntervalInMillSeconds.
       */
      @java.lang.Override
      public int getCallbackIntervalInMillSeconds() {
        return callbackIntervalInMillSeconds_;
      }
      /**
       * <pre>
       * 上报间隔: 单位为毫秒, 如果距离上次上报不超过这个值,则会丢弃,当然 这是非常特定场景的设计
       * 比如人员检测,如果是这样的情况下,猥琐男子周某跟随女神ABC,同时经过一个IPC,这时候,
       * 女神ABC被拍到了,猥琐男子被拍到了,分析到了,丢弃了
       * 所以这个仅在 key_frame_only 为true 时读取,比如看一个厨房哪个B没带帽子,这种消极的场景中这样的操作是有效的
       * </pre>
       *
       * <code>uint32 callback_interval_in_mill_seconds = 5;</code>
       * @param value The callbackIntervalInMillSeconds to set.
       * @return This builder for chaining.
       */
      public Builder setCallbackIntervalInMillSeconds(int value) {

        callbackIntervalInMillSeconds_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 上报间隔: 单位为毫秒, 如果距离上次上报不超过这个值,则会丢弃,当然 这是非常特定场景的设计
       * 比如人员检测,如果是这样的情况下,猥琐男子周某跟随女神ABC,同时经过一个IPC,这时候,
       * 女神ABC被拍到了,猥琐男子被拍到了,分析到了,丢弃了
       * 所以这个仅在 key_frame_only 为true 时读取,比如看一个厨房哪个B没带帽子,这种消极的场景中这样的操作是有效的
       * </pre>
       *
       * <code>uint32 callback_interval_in_mill_seconds = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCallbackIntervalInMillSeconds() {
        bitField0_ = (bitField0_ & ~0x00000010);
        callbackIntervalInMillSeconds_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pb.VideoCommonArgs)
    }

    // @@protoc_insertion_point(class_scope:pb.VideoCommonArgs)
    private static final com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs();
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<VideoCommonArgs>
        PARSER = new com.google.protobuf.AbstractParser<VideoCommonArgs>() {
      @java.lang.Override
      public VideoCommonArgs parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<VideoCommonArgs> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<VideoCommonArgs> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StreamTaskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pb.StreamTask)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return Whether the args field is set.
     */
    boolean hasArgs();
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return The args.
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getArgs();
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder getArgsOrBuilder();

    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the algorithms.
     */
    java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return The count of algorithms.
     */
    int getAlgorithmsCount();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the element to return.
     * @return The algorithms at the given index.
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index);
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the enum numeric values on the wire for algorithms.
     */
    java.util.List<java.lang.Integer>
    getAlgorithmsValueList();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of algorithms at the given index.
     */
    int getAlgorithmsValue(int index);

    /**
     * <pre>
     * 视频流的URL地址
     * </pre>
     *
     * <code>string stream_url = 3;</code>
     * @return The streamUrl.
     */
    java.lang.String getStreamUrl();
    /**
     * <pre>
     * 视频流的URL地址
     * </pre>
     *
     * <code>string stream_url = 3;</code>
     * @return The bytes for streamUrl.
     */
    com.google.protobuf.ByteString
        getStreamUrlBytes();
  }
  /**
   * <pre>
   * 流媒体任务，包含视频任务的参数以及视频流地址
   * </pre>
   *
   * Protobuf type {@code pb.StreamTask}
   */
  public static final class StreamTask extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pb.StreamTask)
      StreamTaskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StreamTask.newBuilder() to construct.
    private StreamTask(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StreamTask() {
      algorithms_ = java.util.Collections.emptyList();
      streamUrl_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StreamTask();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_StreamTask_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_StreamTask_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.class, com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.Builder.class);
    }

    private int bitField0_;
    public static final int ARGS_FIELD_NUMBER = 1;
    private com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs args_;
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return Whether the args field is set.
     */
    @java.lang.Override
    public boolean hasArgs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return The args.
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getArgs() {
      return args_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
    }
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder getArgsOrBuilder() {
      return args_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
    }

    public static final int ALGORITHMS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<java.lang.Integer> algorithms_;
    private static final com.google.protobuf.Internal.ListAdapter.Converter<
        java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> algorithms_converter_ =
            new com.google.protobuf.Internal.ListAdapter.Converter<
                java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>() {
              public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm convert(java.lang.Integer from) {
                com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm result = com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.forNumber(from);
                return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.UNRECOGNIZED : result;
              }
            };
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the algorithms.
     */
    @java.lang.Override
    public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>(algorithms_, algorithms_converter_);
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return The count of algorithms.
     */
    @java.lang.Override
    public int getAlgorithmsCount() {
      return algorithms_.size();
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the element to return.
     * @return The algorithms at the given index.
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index) {
      return algorithms_converter_.convert(algorithms_.get(index));
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the enum numeric values on the wire for algorithms.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getAlgorithmsValueList() {
      return algorithms_;
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of algorithms at the given index.
     */
    @java.lang.Override
    public int getAlgorithmsValue(int index) {
      return algorithms_.get(index);
    }
    private int algorithmsMemoizedSerializedSize;

    public static final int STREAM_URL_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object streamUrl_ = "";
    /**
     * <pre>
     * 视频流的URL地址
     * </pre>
     *
     * <code>string stream_url = 3;</code>
     * @return The streamUrl.
     */
    @java.lang.Override
    public java.lang.String getStreamUrl() {
      java.lang.Object ref = streamUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        streamUrl_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 视频流的URL地址
     * </pre>
     *
     * <code>string stream_url = 3;</code>
     * @return The bytes for streamUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getStreamUrlBytes() {
      java.lang.Object ref = streamUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        streamUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getArgs());
      }
      if (getAlgorithmsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(algorithmsMemoizedSerializedSize);
      }
      for (int i = 0; i < algorithms_.size(); i++) {
        output.writeEnumNoTag(algorithms_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(streamUrl_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, streamUrl_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getArgs());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < algorithms_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeEnumSizeNoTag(algorithms_.get(i));
        }
        size += dataSize;
        if (!getAlgorithmsList().isEmpty()) {  size += 1;
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(dataSize);
        }algorithmsMemoizedSerializedSize = dataSize;
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(streamUrl_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, streamUrl_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask)) {
        return super.equals(obj);
      }
      com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask other = (com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask) obj;

      if (hasArgs() != other.hasArgs()) return false;
      if (hasArgs()) {
        if (!getArgs()
            .equals(other.getArgs())) return false;
      }
      if (!algorithms_.equals(other.algorithms_)) return false;
      if (!getStreamUrl()
          .equals(other.getStreamUrl())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasArgs()) {
        hash = (37 * hash) + ARGS_FIELD_NUMBER;
        hash = (53 * hash) + getArgs().hashCode();
      }
      if (getAlgorithmsCount() > 0) {
        hash = (37 * hash) + ALGORITHMS_FIELD_NUMBER;
        hash = (53 * hash) + algorithms_.hashCode();
      }
      hash = (37 * hash) + STREAM_URL_FIELD_NUMBER;
      hash = (53 * hash) + getStreamUrl().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 流媒体任务，包含视频任务的参数以及视频流地址
     * </pre>
     *
     * Protobuf type {@code pb.StreamTask}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pb.StreamTask)
        com.saida.analysis.pb.TaskExchangeOuterClass.StreamTaskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_StreamTask_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_StreamTask_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.class, com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.Builder.class);
      }

      // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getArgsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        args_ = null;
        if (argsBuilder_ != null) {
          argsBuilder_.dispose();
          argsBuilder_ = null;
        }
        algorithms_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        streamUrl_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_StreamTask_descriptor;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask getDefaultInstanceForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.getDefaultInstance();
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask build() {
        com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask buildPartial() {
        com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask result = new com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask result) {
        if (((bitField0_ & 0x00000002) != 0)) {
          algorithms_ = java.util.Collections.unmodifiableList(algorithms_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.algorithms_ = algorithms_;
      }

      private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.args_ = argsBuilder_ == null
              ? args_
              : argsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.streamUrl_ = streamUrl_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask) {
          return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask other) {
        if (other == com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask.getDefaultInstance()) return this;
        if (other.hasArgs()) {
          mergeArgs(other.getArgs());
        }
        if (!other.algorithms_.isEmpty()) {
          if (algorithms_.isEmpty()) {
            algorithms_ = other.algorithms_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAlgorithmsIsMutable();
            algorithms_.addAll(other.algorithms_);
          }
          onChanged();
        }
        if (!other.getStreamUrl().isEmpty()) {
          streamUrl_ = other.streamUrl_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getArgsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                int tmpRaw = input.readEnum();
                ensureAlgorithmsIsMutable();
                algorithms_.add(tmpRaw);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int oldLimit = input.pushLimit(length);
                while(input.getBytesUntilLimit() > 0) {
                  int tmpRaw = input.readEnum();
                  ensureAlgorithmsIsMutable();
                  algorithms_.add(tmpRaw);
                }
                input.popLimit(oldLimit);
                break;
              } // case 18
              case 26: {
                streamUrl_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs args_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder> argsBuilder_;
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       * @return Whether the args field is set.
       */
      public boolean hasArgs() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       * @return The args.
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getArgs() {
        if (argsBuilder_ == null) {
          return args_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
        } else {
          return argsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder setArgs(com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs value) {
        if (argsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          args_ = value;
        } else {
          argsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder setArgs(
          com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder builderForValue) {
        if (argsBuilder_ == null) {
          args_ = builderForValue.build();
        } else {
          argsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder mergeArgs(com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs value) {
        if (argsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            args_ != null &&
            args_ != com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance()) {
            getArgsBuilder().mergeFrom(value);
          } else {
            args_ = value;
          }
        } else {
          argsBuilder_.mergeFrom(value);
        }
        if (args_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder clearArgs() {
        bitField0_ = (bitField0_ & ~0x00000001);
        args_ = null;
        if (argsBuilder_ != null) {
          argsBuilder_.dispose();
          argsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder getArgsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getArgsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder getArgsOrBuilder() {
        if (argsBuilder_ != null) {
          return argsBuilder_.getMessageOrBuilder();
        } else {
          return args_ == null ?
              com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
        }
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder> 
          getArgsFieldBuilder() {
        if (argsBuilder_ == null) {
          argsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder>(
                  getArgs(),
                  getParentForChildren(),
                  isClean());
          args_ = null;
        }
        return argsBuilder_;
      }

      private java.util.List<java.lang.Integer> algorithms_ =
        java.util.Collections.emptyList();
      private void ensureAlgorithmsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          algorithms_ = new java.util.ArrayList<java.lang.Integer>(algorithms_);
          bitField0_ |= 0x00000002;
        }
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return A list containing the algorithms.
       */
      public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList() {
        return new com.google.protobuf.Internal.ListAdapter<
            java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>(algorithms_, algorithms_converter_);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return The count of algorithms.
       */
      public int getAlgorithmsCount() {
        return algorithms_.size();
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index of the element to return.
       * @return The algorithms at the given index.
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index) {
        return algorithms_converter_.convert(algorithms_.get(index));
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index to set the value at.
       * @param value The algorithms to set.
       * @return This builder for chaining.
       */
      public Builder setAlgorithms(
          int index, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAlgorithmsIsMutable();
        algorithms_.set(index, value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param value The algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAlgorithms(com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAlgorithmsIsMutable();
        algorithms_.add(value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param values The algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAllAlgorithms(
          java.lang.Iterable<? extends com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> values) {
        ensureAlgorithmsIsMutable();
        for (com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value : values) {
          algorithms_.add(value.getNumber());
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlgorithms() {
        algorithms_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return A list containing the enum numeric values on the wire for algorithms.
       */
      public java.util.List<java.lang.Integer>
      getAlgorithmsValueList() {
        return java.util.Collections.unmodifiableList(algorithms_);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of algorithms at the given index.
       */
      public int getAlgorithmsValue(int index) {
        return algorithms_.get(index);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index to set the value at.
       * @param value The enum numeric value on the wire for algorithms to set.
       * @return This builder for chaining.
       */
      public Builder setAlgorithmsValue(
          int index, int value) {
        ensureAlgorithmsIsMutable();
        algorithms_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param value The enum numeric value on the wire for algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAlgorithmsValue(int value) {
        ensureAlgorithmsIsMutable();
        algorithms_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param values The enum numeric values on the wire for algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAllAlgorithmsValue(
          java.lang.Iterable<java.lang.Integer> values) {
        ensureAlgorithmsIsMutable();
        for (int value : values) {
          algorithms_.add(value);
        }
        onChanged();
        return this;
      }

      private java.lang.Object streamUrl_ = "";
      /**
       * <pre>
       * 视频流的URL地址
       * </pre>
       *
       * <code>string stream_url = 3;</code>
       * @return The streamUrl.
       */
      public java.lang.String getStreamUrl() {
        java.lang.Object ref = streamUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          streamUrl_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 视频流的URL地址
       * </pre>
       *
       * <code>string stream_url = 3;</code>
       * @return The bytes for streamUrl.
       */
      public com.google.protobuf.ByteString
          getStreamUrlBytes() {
        java.lang.Object ref = streamUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          streamUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 视频流的URL地址
       * </pre>
       *
       * <code>string stream_url = 3;</code>
       * @param value The streamUrl to set.
       * @return This builder for chaining.
       */
      public Builder setStreamUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        streamUrl_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频流的URL地址
       * </pre>
       *
       * <code>string stream_url = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStreamUrl() {
        streamUrl_ = getDefaultInstance().getStreamUrl();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频流的URL地址
       * </pre>
       *
       * <code>string stream_url = 3;</code>
       * @param value The bytes for streamUrl to set.
       * @return This builder for chaining.
       */
      public Builder setStreamUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        streamUrl_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pb.StreamTask)
    }

    // @@protoc_insertion_point(class_scope:pb.StreamTask)
    private static final com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask();
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<StreamTask>
        PARSER = new com.google.protobuf.AbstractParser<StreamTask>() {
      @java.lang.Override
      public StreamTask parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<StreamTask> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StreamTask> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.StreamTask getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FileTaskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pb.FileTask)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return Whether the args field is set.
     */
    boolean hasArgs();
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return The args.
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getArgs();
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder getArgsOrBuilder();

    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the algorithms.
     */
    java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return The count of algorithms.
     */
    int getAlgorithmsCount();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the element to return.
     * @return The algorithms at the given index.
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index);
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the enum numeric values on the wire for algorithms.
     */
    java.util.List<java.lang.Integer>
    getAlgorithmsValueList();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of algorithms at the given index.
     */
    int getAlgorithmsValue(int index);

    /**
     * <pre>
     * 需要处理的文件路径
     * </pre>
     *
     * <code>string file_path = 3;</code>
     * @return The filePath.
     */
    java.lang.String getFilePath();
    /**
     * <pre>
     * 需要处理的文件路径
     * </pre>
     *
     * <code>string file_path = 3;</code>
     * @return The bytes for filePath.
     */
    com.google.protobuf.ByteString
        getFilePathBytes();
  }
  /**
   * <pre>
   * 文件任务，包含视频参数和文件路径
   * </pre>
   *
   * Protobuf type {@code pb.FileTask}
   */
  public static final class FileTask extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pb.FileTask)
      FileTaskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FileTask.newBuilder() to construct.
    private FileTask(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FileTask() {
      algorithms_ = java.util.Collections.emptyList();
      filePath_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FileTask();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_FileTask_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_FileTask_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.class, com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.Builder.class);
    }

    private int bitField0_;
    public static final int ARGS_FIELD_NUMBER = 1;
    private com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs args_;
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return Whether the args field is set.
     */
    @java.lang.Override
    public boolean hasArgs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     * @return The args.
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getArgs() {
      return args_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
    }
    /**
     * <pre>
     * 视频通用参数
     * </pre>
     *
     * <code>.pb.VideoCommonArgs args = 1;</code>
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder getArgsOrBuilder() {
      return args_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
    }

    public static final int ALGORITHMS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<java.lang.Integer> algorithms_;
    private static final com.google.protobuf.Internal.ListAdapter.Converter<
        java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> algorithms_converter_ =
            new com.google.protobuf.Internal.ListAdapter.Converter<
                java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>() {
              public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm convert(java.lang.Integer from) {
                com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm result = com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.forNumber(from);
                return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.UNRECOGNIZED : result;
              }
            };
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the algorithms.
     */
    @java.lang.Override
    public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>(algorithms_, algorithms_converter_);
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return The count of algorithms.
     */
    @java.lang.Override
    public int getAlgorithmsCount() {
      return algorithms_.size();
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the element to return.
     * @return The algorithms at the given index.
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index) {
      return algorithms_converter_.convert(algorithms_.get(index));
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @return A list containing the enum numeric values on the wire for algorithms.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getAlgorithmsValueList() {
      return algorithms_;
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of algorithms at the given index.
     */
    @java.lang.Override
    public int getAlgorithmsValue(int index) {
      return algorithms_.get(index);
    }
    private int algorithmsMemoizedSerializedSize;

    public static final int FILE_PATH_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object filePath_ = "";
    /**
     * <pre>
     * 需要处理的文件路径
     * </pre>
     *
     * <code>string file_path = 3;</code>
     * @return The filePath.
     */
    @java.lang.Override
    public java.lang.String getFilePath() {
      java.lang.Object ref = filePath_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        filePath_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 需要处理的文件路径
     * </pre>
     *
     * <code>string file_path = 3;</code>
     * @return The bytes for filePath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFilePathBytes() {
      java.lang.Object ref = filePath_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        filePath_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getArgs());
      }
      if (getAlgorithmsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(algorithmsMemoizedSerializedSize);
      }
      for (int i = 0; i < algorithms_.size(); i++) {
        output.writeEnumNoTag(algorithms_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(filePath_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, filePath_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getArgs());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < algorithms_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeEnumSizeNoTag(algorithms_.get(i));
        }
        size += dataSize;
        if (!getAlgorithmsList().isEmpty()) {  size += 1;
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(dataSize);
        }algorithmsMemoizedSerializedSize = dataSize;
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(filePath_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, filePath_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.FileTask)) {
        return super.equals(obj);
      }
      com.saida.analysis.pb.TaskExchangeOuterClass.FileTask other = (com.saida.analysis.pb.TaskExchangeOuterClass.FileTask) obj;

      if (hasArgs() != other.hasArgs()) return false;
      if (hasArgs()) {
        if (!getArgs()
            .equals(other.getArgs())) return false;
      }
      if (!algorithms_.equals(other.algorithms_)) return false;
      if (!getFilePath()
          .equals(other.getFilePath())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasArgs()) {
        hash = (37 * hash) + ARGS_FIELD_NUMBER;
        hash = (53 * hash) + getArgs().hashCode();
      }
      if (getAlgorithmsCount() > 0) {
        hash = (37 * hash) + ALGORITHMS_FIELD_NUMBER;
        hash = (53 * hash) + algorithms_.hashCode();
      }
      hash = (37 * hash) + FILE_PATH_FIELD_NUMBER;
      hash = (53 * hash) + getFilePath().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.FileTask prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 文件任务，包含视频参数和文件路径
     * </pre>
     *
     * Protobuf type {@code pb.FileTask}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pb.FileTask)
        com.saida.analysis.pb.TaskExchangeOuterClass.FileTaskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_FileTask_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_FileTask_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.class, com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.Builder.class);
      }

      // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getArgsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        args_ = null;
        if (argsBuilder_ != null) {
          argsBuilder_.dispose();
          argsBuilder_ = null;
        }
        algorithms_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        filePath_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_FileTask_descriptor;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.FileTask getDefaultInstanceForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.getDefaultInstance();
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.FileTask build() {
        com.saida.analysis.pb.TaskExchangeOuterClass.FileTask result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.FileTask buildPartial() {
        com.saida.analysis.pb.TaskExchangeOuterClass.FileTask result = new com.saida.analysis.pb.TaskExchangeOuterClass.FileTask(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.saida.analysis.pb.TaskExchangeOuterClass.FileTask result) {
        if (((bitField0_ & 0x00000002) != 0)) {
          algorithms_ = java.util.Collections.unmodifiableList(algorithms_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.algorithms_ = algorithms_;
      }

      private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.FileTask result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.args_ = argsBuilder_ == null
              ? args_
              : argsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.filePath_ = filePath_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.FileTask) {
          return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.FileTask)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.FileTask other) {
        if (other == com.saida.analysis.pb.TaskExchangeOuterClass.FileTask.getDefaultInstance()) return this;
        if (other.hasArgs()) {
          mergeArgs(other.getArgs());
        }
        if (!other.algorithms_.isEmpty()) {
          if (algorithms_.isEmpty()) {
            algorithms_ = other.algorithms_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAlgorithmsIsMutable();
            algorithms_.addAll(other.algorithms_);
          }
          onChanged();
        }
        if (!other.getFilePath().isEmpty()) {
          filePath_ = other.filePath_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getArgsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                int tmpRaw = input.readEnum();
                ensureAlgorithmsIsMutable();
                algorithms_.add(tmpRaw);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int oldLimit = input.pushLimit(length);
                while(input.getBytesUntilLimit() > 0) {
                  int tmpRaw = input.readEnum();
                  ensureAlgorithmsIsMutable();
                  algorithms_.add(tmpRaw);
                }
                input.popLimit(oldLimit);
                break;
              } // case 18
              case 26: {
                filePath_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs args_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder> argsBuilder_;
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       * @return Whether the args field is set.
       */
      public boolean hasArgs() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       * @return The args.
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs getArgs() {
        if (argsBuilder_ == null) {
          return args_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
        } else {
          return argsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder setArgs(com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs value) {
        if (argsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          args_ = value;
        } else {
          argsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder setArgs(
          com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder builderForValue) {
        if (argsBuilder_ == null) {
          args_ = builderForValue.build();
        } else {
          argsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder mergeArgs(com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs value) {
        if (argsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            args_ != null &&
            args_ != com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance()) {
            getArgsBuilder().mergeFrom(value);
          } else {
            args_ = value;
          }
        } else {
          argsBuilder_.mergeFrom(value);
        }
        if (args_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public Builder clearArgs() {
        bitField0_ = (bitField0_ & ~0x00000001);
        args_ = null;
        if (argsBuilder_ != null) {
          argsBuilder_.dispose();
          argsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder getArgsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getArgsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder getArgsOrBuilder() {
        if (argsBuilder_ != null) {
          return argsBuilder_.getMessageOrBuilder();
        } else {
          return args_ == null ?
              com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.getDefaultInstance() : args_;
        }
      }
      /**
       * <pre>
       * 视频通用参数
       * </pre>
       *
       * <code>.pb.VideoCommonArgs args = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder> 
          getArgsFieldBuilder() {
        if (argsBuilder_ == null) {
          argsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgs.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.VideoCommonArgsOrBuilder>(
                  getArgs(),
                  getParentForChildren(),
                  isClean());
          args_ = null;
        }
        return argsBuilder_;
      }

      private java.util.List<java.lang.Integer> algorithms_ =
        java.util.Collections.emptyList();
      private void ensureAlgorithmsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          algorithms_ = new java.util.ArrayList<java.lang.Integer>(algorithms_);
          bitField0_ |= 0x00000002;
        }
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return A list containing the algorithms.
       */
      public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList() {
        return new com.google.protobuf.Internal.ListAdapter<
            java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>(algorithms_, algorithms_converter_);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return The count of algorithms.
       */
      public int getAlgorithmsCount() {
        return algorithms_.size();
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index of the element to return.
       * @return The algorithms at the given index.
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index) {
        return algorithms_converter_.convert(algorithms_.get(index));
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index to set the value at.
       * @param value The algorithms to set.
       * @return This builder for chaining.
       */
      public Builder setAlgorithms(
          int index, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAlgorithmsIsMutable();
        algorithms_.set(index, value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param value The algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAlgorithms(com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAlgorithmsIsMutable();
        algorithms_.add(value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param values The algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAllAlgorithms(
          java.lang.Iterable<? extends com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> values) {
        ensureAlgorithmsIsMutable();
        for (com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value : values) {
          algorithms_.add(value.getNumber());
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlgorithms() {
        algorithms_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @return A list containing the enum numeric values on the wire for algorithms.
       */
      public java.util.List<java.lang.Integer>
      getAlgorithmsValueList() {
        return java.util.Collections.unmodifiableList(algorithms_);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of algorithms at the given index.
       */
      public int getAlgorithmsValue(int index) {
        return algorithms_.get(index);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param index The index to set the value at.
       * @param value The enum numeric value on the wire for algorithms to set.
       * @return This builder for chaining.
       */
      public Builder setAlgorithmsValue(
          int index, int value) {
        ensureAlgorithmsIsMutable();
        algorithms_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param value The enum numeric value on the wire for algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAlgorithmsValue(int value) {
        ensureAlgorithmsIsMutable();
        algorithms_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 2;</code>
       * @param values The enum numeric values on the wire for algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAllAlgorithmsValue(
          java.lang.Iterable<java.lang.Integer> values) {
        ensureAlgorithmsIsMutable();
        for (int value : values) {
          algorithms_.add(value);
        }
        onChanged();
        return this;
      }

      private java.lang.Object filePath_ = "";
      /**
       * <pre>
       * 需要处理的文件路径
       * </pre>
       *
       * <code>string file_path = 3;</code>
       * @return The filePath.
       */
      public java.lang.String getFilePath() {
        java.lang.Object ref = filePath_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          filePath_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 需要处理的文件路径
       * </pre>
       *
       * <code>string file_path = 3;</code>
       * @return The bytes for filePath.
       */
      public com.google.protobuf.ByteString
          getFilePathBytes() {
        java.lang.Object ref = filePath_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          filePath_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 需要处理的文件路径
       * </pre>
       *
       * <code>string file_path = 3;</code>
       * @param value The filePath to set.
       * @return This builder for chaining.
       */
      public Builder setFilePath(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        filePath_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要处理的文件路径
       * </pre>
       *
       * <code>string file_path = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFilePath() {
        filePath_ = getDefaultInstance().getFilePath();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要处理的文件路径
       * </pre>
       *
       * <code>string file_path = 3;</code>
       * @param value The bytes for filePath to set.
       * @return This builder for chaining.
       */
      public Builder setFilePathBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        filePath_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pb.FileTask)
    }

    // @@protoc_insertion_point(class_scope:pb.FileTask)
    private static final com.saida.analysis.pb.TaskExchangeOuterClass.FileTask DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.FileTask();
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.FileTask getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FileTask>
        PARSER = new com.google.protobuf.AbstractParser<FileTask>() {
      @java.lang.Override
      public FileTask parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FileTask> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FileTask> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.FileTask getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ImageTaskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pb.ImageTask)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @return A list containing the algorithms.
     */
    java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @return The count of algorithms.
     */
    int getAlgorithmsCount();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @param index The index of the element to return.
     * @return The algorithms at the given index.
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index);
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @return A list containing the enum numeric values on the wire for algorithms.
     */
    java.util.List<java.lang.Integer>
    getAlgorithmsValueList();
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of algorithms at the given index.
     */
    int getAlgorithmsValue(int index);

    /**
     * <pre>
     * 图片的格式
     * </pre>
     *
     * <code>.pb.ImageFormat img_format = 2;</code>
     * @return The enum numeric value on the wire for imgFormat.
     */
    int getImgFormatValue();
    /**
     * <pre>
     * 图片的格式
     * </pre>
     *
     * <code>.pb.ImageFormat img_format = 2;</code>
     * @return The imgFormat.
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat getImgFormat();

    /**
     * <pre>
     * 图片的字节数据
     * </pre>
     *
     * <code>bytes img = 3;</code>
     * @return The img.
     */
    com.google.protobuf.ByteString getImg();
  }
  /**
   * <pre>
   * 图片任务，包含图片格式及图片数据
   * </pre>
   *
   * Protobuf type {@code pb.ImageTask}
   */
  public static final class ImageTask extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pb.ImageTask)
      ImageTaskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ImageTask.newBuilder() to construct.
    private ImageTask(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ImageTask() {
      algorithms_ = java.util.Collections.emptyList();
      imgFormat_ = 0;
      img_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ImageTask();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_ImageTask_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_ImageTask_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.class, com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.Builder.class);
    }

    public static final int ALGORITHMS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<java.lang.Integer> algorithms_;
    private static final com.google.protobuf.Internal.ListAdapter.Converter<
        java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> algorithms_converter_ =
            new com.google.protobuf.Internal.ListAdapter.Converter<
                java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>() {
              public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm convert(java.lang.Integer from) {
                com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm result = com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.forNumber(from);
                return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.UNRECOGNIZED : result;
              }
            };
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @return A list containing the algorithms.
     */
    @java.lang.Override
    public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList() {
      return new com.google.protobuf.Internal.ListAdapter<
          java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>(algorithms_, algorithms_converter_);
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @return The count of algorithms.
     */
    @java.lang.Override
    public int getAlgorithmsCount() {
      return algorithms_.size();
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @param index The index of the element to return.
     * @return The algorithms at the given index.
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index) {
      return algorithms_converter_.convert(algorithms_.get(index));
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @return A list containing the enum numeric values on the wire for algorithms.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getAlgorithmsValueList() {
      return algorithms_;
    }
    /**
     * <pre>
     * 需要调用的算法
     * </pre>
     *
     * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
     * @param index The index of the value to return.
     * @return The enum numeric value on the wire of algorithms at the given index.
     */
    @java.lang.Override
    public int getAlgorithmsValue(int index) {
      return algorithms_.get(index);
    }
    private int algorithmsMemoizedSerializedSize;

    public static final int IMG_FORMAT_FIELD_NUMBER = 2;
    private int imgFormat_ = 0;
    /**
     * <pre>
     * 图片的格式
     * </pre>
     *
     * <code>.pb.ImageFormat img_format = 2;</code>
     * @return The enum numeric value on the wire for imgFormat.
     */
    @java.lang.Override public int getImgFormatValue() {
      return imgFormat_;
    }
    /**
     * <pre>
     * 图片的格式
     * </pre>
     *
     * <code>.pb.ImageFormat img_format = 2;</code>
     * @return The imgFormat.
     */
    @java.lang.Override public com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat getImgFormat() {
      com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat result = com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.forNumber(imgFormat_);
      return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.UNRECOGNIZED : result;
    }

    public static final int IMG_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString img_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 图片的字节数据
     * </pre>
     *
     * <code>bytes img = 3;</code>
     * @return The img.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getImg() {
      return img_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getAlgorithmsList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(algorithmsMemoizedSerializedSize);
      }
      for (int i = 0; i < algorithms_.size(); i++) {
        output.writeEnumNoTag(algorithms_.get(i));
      }
      if (imgFormat_ != com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_UNKNOWN.getNumber()) {
        output.writeEnum(2, imgFormat_);
      }
      if (!img_.isEmpty()) {
        output.writeBytes(3, img_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < algorithms_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeEnumSizeNoTag(algorithms_.get(i));
        }
        size += dataSize;
        if (!getAlgorithmsList().isEmpty()) {  size += 1;
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(dataSize);
        }algorithmsMemoizedSerializedSize = dataSize;
      }
      if (imgFormat_ != com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, imgFormat_);
      }
      if (!img_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, img_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask)) {
        return super.equals(obj);
      }
      com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask other = (com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask) obj;

      if (!algorithms_.equals(other.algorithms_)) return false;
      if (imgFormat_ != other.imgFormat_) return false;
      if (!getImg()
          .equals(other.getImg())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getAlgorithmsCount() > 0) {
        hash = (37 * hash) + ALGORITHMS_FIELD_NUMBER;
        hash = (53 * hash) + algorithms_.hashCode();
      }
      hash = (37 * hash) + IMG_FORMAT_FIELD_NUMBER;
      hash = (53 * hash) + imgFormat_;
      hash = (37 * hash) + IMG_FIELD_NUMBER;
      hash = (53 * hash) + getImg().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 图片任务，包含图片格式及图片数据
     * </pre>
     *
     * Protobuf type {@code pb.ImageTask}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pb.ImageTask)
        com.saida.analysis.pb.TaskExchangeOuterClass.ImageTaskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_ImageTask_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_ImageTask_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.class, com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.Builder.class);
      }

      // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        algorithms_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        imgFormat_ = 0;
        img_ = com.google.protobuf.ByteString.EMPTY;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_ImageTask_descriptor;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask getDefaultInstanceForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.getDefaultInstance();
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask build() {
        com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask buildPartial() {
        com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask result = new com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask result) {
        if (((bitField0_ & 0x00000001) != 0)) {
          algorithms_ = java.util.Collections.unmodifiableList(algorithms_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.algorithms_ = algorithms_;
      }

      private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.imgFormat_ = imgFormat_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.img_ = img_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask) {
          return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask other) {
        if (other == com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask.getDefaultInstance()) return this;
        if (!other.algorithms_.isEmpty()) {
          if (algorithms_.isEmpty()) {
            algorithms_ = other.algorithms_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAlgorithmsIsMutable();
            algorithms_.addAll(other.algorithms_);
          }
          onChanged();
        }
        if (other.imgFormat_ != 0) {
          setImgFormatValue(other.getImgFormatValue());
        }
        if (other.getImg() != com.google.protobuf.ByteString.EMPTY) {
          setImg(other.getImg());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int tmpRaw = input.readEnum();
                ensureAlgorithmsIsMutable();
                algorithms_.add(tmpRaw);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int oldLimit = input.pushLimit(length);
                while(input.getBytesUntilLimit() > 0) {
                  int tmpRaw = input.readEnum();
                  ensureAlgorithmsIsMutable();
                  algorithms_.add(tmpRaw);
                }
                input.popLimit(oldLimit);
                break;
              } // case 10
              case 16: {
                imgFormat_ = input.readEnum();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                img_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<java.lang.Integer> algorithms_ =
        java.util.Collections.emptyList();
      private void ensureAlgorithmsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          algorithms_ = new java.util.ArrayList<java.lang.Integer>(algorithms_);
          bitField0_ |= 0x00000001;
        }
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @return A list containing the algorithms.
       */
      public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> getAlgorithmsList() {
        return new com.google.protobuf.Internal.ListAdapter<
            java.lang.Integer, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm>(algorithms_, algorithms_converter_);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @return The count of algorithms.
       */
      public int getAlgorithmsCount() {
        return algorithms_.size();
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param index The index of the element to return.
       * @return The algorithms at the given index.
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgorithms(int index) {
        return algorithms_converter_.convert(algorithms_.get(index));
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param index The index to set the value at.
       * @param value The algorithms to set.
       * @return This builder for chaining.
       */
      public Builder setAlgorithms(
          int index, com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAlgorithmsIsMutable();
        algorithms_.set(index, value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param value The algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAlgorithms(com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAlgorithmsIsMutable();
        algorithms_.add(value.getNumber());
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param values The algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAllAlgorithms(
          java.lang.Iterable<? extends com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm> values) {
        ensureAlgorithmsIsMutable();
        for (com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value : values) {
          algorithms_.add(value.getNumber());
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlgorithms() {
        algorithms_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @return A list containing the enum numeric values on the wire for algorithms.
       */
      public java.util.List<java.lang.Integer>
      getAlgorithmsValueList() {
        return java.util.Collections.unmodifiableList(algorithms_);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param index The index of the value to return.
       * @return The enum numeric value on the wire of algorithms at the given index.
       */
      public int getAlgorithmsValue(int index) {
        return algorithms_.get(index);
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param index The index to set the value at.
       * @param value The enum numeric value on the wire for algorithms to set.
       * @return This builder for chaining.
       */
      public Builder setAlgorithmsValue(
          int index, int value) {
        ensureAlgorithmsIsMutable();
        algorithms_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param value The enum numeric value on the wire for algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAlgorithmsValue(int value) {
        ensureAlgorithmsIsMutable();
        algorithms_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要调用的算法
       * </pre>
       *
       * <code>repeated .pb.DetectionAlgorithm algorithms = 1;</code>
       * @param values The enum numeric values on the wire for algorithms to add.
       * @return This builder for chaining.
       */
      public Builder addAllAlgorithmsValue(
          java.lang.Iterable<java.lang.Integer> values) {
        ensureAlgorithmsIsMutable();
        for (int value : values) {
          algorithms_.add(value);
        }
        onChanged();
        return this;
      }

      private int imgFormat_ = 0;
      /**
       * <pre>
       * 图片的格式
       * </pre>
       *
       * <code>.pb.ImageFormat img_format = 2;</code>
       * @return The enum numeric value on the wire for imgFormat.
       */
      @java.lang.Override public int getImgFormatValue() {
        return imgFormat_;
      }
      /**
       * <pre>
       * 图片的格式
       * </pre>
       *
       * <code>.pb.ImageFormat img_format = 2;</code>
       * @param value The enum numeric value on the wire for imgFormat to set.
       * @return This builder for chaining.
       */
      public Builder setImgFormatValue(int value) {
        imgFormat_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图片的格式
       * </pre>
       *
       * <code>.pb.ImageFormat img_format = 2;</code>
       * @return The imgFormat.
       */
      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat getImgFormat() {
        com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat result = com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.forNumber(imgFormat_);
        return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * 图片的格式
       * </pre>
       *
       * <code>.pb.ImageFormat img_format = 2;</code>
       * @param value The imgFormat to set.
       * @return This builder for chaining.
       */
      public Builder setImgFormat(com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        imgFormat_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图片的格式
       * </pre>
       *
       * <code>.pb.ImageFormat img_format = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearImgFormat() {
        bitField0_ = (bitField0_ & ~0x00000002);
        imgFormat_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString img_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * 图片的字节数据
       * </pre>
       *
       * <code>bytes img = 3;</code>
       * @return The img.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getImg() {
        return img_;
      }
      /**
       * <pre>
       * 图片的字节数据
       * </pre>
       *
       * <code>bytes img = 3;</code>
       * @param value The img to set.
       * @return This builder for chaining.
       */
      public Builder setImg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        img_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图片的字节数据
       * </pre>
       *
       * <code>bytes img = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearImg() {
        bitField0_ = (bitField0_ & ~0x00000004);
        img_ = getDefaultInstance().getImg();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pb.ImageTask)
    }

    // @@protoc_insertion_point(class_scope:pb.ImageTask)
    private static final com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask();
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ImageTask>
        PARSER = new com.google.protobuf.AbstractParser<ImageTask>() {
      @java.lang.Override
      public ImageTask parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ImageTask> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ImageTask> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.ImageTask getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnAIResultGotReplyOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pb.OnAIResultGotReply)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 图像格式
     * </pre>
     *
     * <code>.pb.ImageFormat fmt = 1;</code>
     * @return The enum numeric value on the wire for fmt.
     */
    int getFmtValue();
    /**
     * <pre>
     * 图像格式
     * </pre>
     *
     * <code>.pb.ImageFormat fmt = 1;</code>
     * @return The fmt.
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat getFmt();

    /**
     * <pre>
     * 图像数据，图像的字节流
     * </pre>
     *
     * <code>bytes imageData = 2;</code>
     * @return The imageData.
     */
    com.google.protobuf.ByteString getImageData();

    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> 
        getResultList();
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper getResult(int index);
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    int getResultCount();
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    java.util.List<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder> 
        getResultOrBuilderList();
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder getResultOrBuilder(
        int index);

    /**
     * <code>uint64 timestamp = 4;</code>
     * @return The timestamp.
     */
    long getTimestamp();
  }
  /**
   * <pre>
   * AI结果的响应消息
   * </pre>
   *
   * Protobuf type {@code pb.OnAIResultGotReply}
   */
  public static final class OnAIResultGotReply extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pb.OnAIResultGotReply)
      OnAIResultGotReplyOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnAIResultGotReply.newBuilder() to construct.
    private OnAIResultGotReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnAIResultGotReply() {
      fmt_ = 0;
      imageData_ = com.google.protobuf.ByteString.EMPTY;
      result_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnAIResultGotReply();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Builder.class);
    }

    public interface ResultOrBuilder extends
        // @@protoc_insertion_point(interface_extends:pb.OnAIResultGotReply.Result)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 目标框的坐标
       * </pre>
       *
       * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
       * @return Whether the rect field is set.
       */
      boolean hasRect();
      /**
       * <pre>
       * 目标框的坐标
       * </pre>
       *
       * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
       * @return The rect.
       */
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect getRect();
      /**
       * <pre>
       * 目标框的坐标
       * </pre>
       *
       * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
       */
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.RectOrBuilder getRectOrBuilder();

      /**
       * <pre>
       * 目标的标签ID
       * </pre>
       *
       * <code>uint32 label = 2;</code>
       * @return The label.
       */
      int getLabel();

      /**
       * <pre>
       * 目标的识别概率
       * </pre>
       *
       * <code>double prob = 3;</code>
       * @return The prob.
       */
      double getProb();
    }
    /**
     * <pre>
     * 结果数据，包含目标检测框和相关信息
     * </pre>
     *
     * Protobuf type {@code pb.OnAIResultGotReply.Result}
     */
    public static final class Result extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:pb.OnAIResultGotReply.Result)
        ResultOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Result.newBuilder() to construct.
      private Result(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Result() {
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Result();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder.class);
      }

      public interface RectOrBuilder extends
          // @@protoc_insertion_point(interface_extends:pb.OnAIResultGotReply.Result.Rect)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * 矩形框的左上角 X 坐标
         * </pre>
         *
         * <code>uint32 minX = 1;</code>
         * @return The minX.
         */
        int getMinX();

        /**
         * <pre>
         * 矩形框的右下角 X 坐标
         * </pre>
         *
         * <code>uint32 maxX = 2;</code>
         * @return The maxX.
         */
        int getMaxX();

        /**
         * <pre>
         * 矩形框的左上角 Y 坐标
         * </pre>
         *
         * <code>uint32 minY = 3;</code>
         * @return The minY.
         */
        int getMinY();

        /**
         * <pre>
         * 矩形框的右下角 Y 坐标
         * </pre>
         *
         * <code>uint32 maxY = 4;</code>
         * @return The maxY.
         */
        int getMaxY();
      }
      /**
       * <pre>
       * 矩形框的坐标
       * </pre>
       *
       * Protobuf type {@code pb.OnAIResultGotReply.Result.Rect}
       */
      public static final class Rect extends
          com.google.protobuf.GeneratedMessageV3 implements
          // @@protoc_insertion_point(message_implements:pb.OnAIResultGotReply.Result.Rect)
          RectOrBuilder {
      private static final long serialVersionUID = 0L;
        // Use Rect.newBuilder() to construct.
        private Rect(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
          super(builder);
        }
        private Rect() {
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
            UnusedPrivateParameter unused) {
          return new Rect();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_Rect_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_Rect_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.Builder.class);
        }

        public static final int MINX_FIELD_NUMBER = 1;
        private int minX_ = 0;
        /**
         * <pre>
         * 矩形框的左上角 X 坐标
         * </pre>
         *
         * <code>uint32 minX = 1;</code>
         * @return The minX.
         */
        @java.lang.Override
        public int getMinX() {
          return minX_;
        }

        public static final int MAXX_FIELD_NUMBER = 2;
        private int maxX_ = 0;
        /**
         * <pre>
         * 矩形框的右下角 X 坐标
         * </pre>
         *
         * <code>uint32 maxX = 2;</code>
         * @return The maxX.
         */
        @java.lang.Override
        public int getMaxX() {
          return maxX_;
        }

        public static final int MINY_FIELD_NUMBER = 3;
        private int minY_ = 0;
        /**
         * <pre>
         * 矩形框的左上角 Y 坐标
         * </pre>
         *
         * <code>uint32 minY = 3;</code>
         * @return The minY.
         */
        @java.lang.Override
        public int getMinY() {
          return minY_;
        }

        public static final int MAXY_FIELD_NUMBER = 4;
        private int maxY_ = 0;
        /**
         * <pre>
         * 矩形框的右下角 Y 坐标
         * </pre>
         *
         * <code>uint32 maxY = 4;</code>
         * @return The maxY.
         */
        @java.lang.Override
        public int getMaxY() {
          return maxY_;
        }

        private byte memoizedIsInitialized = -1;
        @java.lang.Override
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          memoizedIsInitialized = 1;
          return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (minX_ != 0) {
            output.writeUInt32(1, minX_);
          }
          if (maxX_ != 0) {
            output.writeUInt32(2, maxX_);
          }
          if (minY_ != 0) {
            output.writeUInt32(3, minY_);
          }
          if (maxY_ != 0) {
            output.writeUInt32(4, maxY_);
          }
          getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (minX_ != 0) {
            size += com.google.protobuf.CodedOutputStream
              .computeUInt32Size(1, minX_);
          }
          if (maxX_ != 0) {
            size += com.google.protobuf.CodedOutputStream
              .computeUInt32Size(2, maxX_);
          }
          if (minY_ != 0) {
            size += com.google.protobuf.CodedOutputStream
              .computeUInt32Size(3, minY_);
          }
          if (maxY_ != 0) {
            size += com.google.protobuf.CodedOutputStream
              .computeUInt32Size(4, maxY_);
          }
          size += getUnknownFields().getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect)) {
            return super.equals(obj);
          }
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect other = (com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect) obj;

          if (getMinX()
              != other.getMinX()) return false;
          if (getMaxX()
              != other.getMaxX()) return false;
          if (getMinY()
              != other.getMinY()) return false;
          if (getMaxY()
              != other.getMaxY()) return false;
          if (!getUnknownFields().equals(other.getUnknownFields())) return false;
          return true;
        }

        @java.lang.Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          hash = (37 * hash) + MINX_FIELD_NUMBER;
          hash = (53 * hash) + getMinX();
          hash = (37 * hash) + MAXX_FIELD_NUMBER;
          hash = (53 * hash) + getMaxX();
          hash = (37 * hash) + MINY_FIELD_NUMBER;
          hash = (53 * hash) + getMinY();
          hash = (37 * hash) + MAXY_FIELD_NUMBER;
          hash = (53 * hash) + getMaxY();
          hash = (29 * hash) + getUnknownFields().hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input);
        }

        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        @java.lang.Override
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * <pre>
         * 矩形框的坐标
         * </pre>
         *
         * Protobuf type {@code pb.OnAIResultGotReply.Result.Rect}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:pb.OnAIResultGotReply.Result.Rect)
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.RectOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_Rect_descriptor;
          }

          @java.lang.Override
          protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
              internalGetFieldAccessorTable() {
            return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_Rect_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.Builder.class);
          }

          // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.newBuilder()
          private Builder() {

          }

          private Builder(
              com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            super(parent);

          }
          @java.lang.Override
          public Builder clear() {
            super.clear();
            bitField0_ = 0;
            minX_ = 0;
            maxX_ = 0;
            minY_ = 0;
            maxY_ = 0;
            return this;
          }

          @java.lang.Override
          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_Rect_descriptor;
          }

          @java.lang.Override
          public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect getDefaultInstanceForType() {
            return com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.getDefaultInstance();
          }

          @java.lang.Override
          public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect build() {
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          @java.lang.Override
          public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect buildPartial() {
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect result = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect(this);
            if (bitField0_ != 0) { buildPartial0(result); }
            onBuilt();
            return result;
          }

          private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect result) {
            int from_bitField0_ = bitField0_;
            if (((from_bitField0_ & 0x00000001) != 0)) {
              result.minX_ = minX_;
            }
            if (((from_bitField0_ & 0x00000002) != 0)) {
              result.maxX_ = maxX_;
            }
            if (((from_bitField0_ & 0x00000004) != 0)) {
              result.minY_ = minY_;
            }
            if (((from_bitField0_ & 0x00000008) != 0)) {
              result.maxY_ = maxY_;
            }
          }

          @java.lang.Override
          public Builder clone() {
            return super.clone();
          }
          @java.lang.Override
          public Builder setField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
            return super.setField(field, value);
          }
          @java.lang.Override
          public Builder clearField(
              com.google.protobuf.Descriptors.FieldDescriptor field) {
            return super.clearField(field);
          }
          @java.lang.Override
          public Builder clearOneof(
              com.google.protobuf.Descriptors.OneofDescriptor oneof) {
            return super.clearOneof(oneof);
          }
          @java.lang.Override
          public Builder setRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              int index, java.lang.Object value) {
            return super.setRepeatedField(field, index, value);
          }
          @java.lang.Override
          public Builder addRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
            return super.addRepeatedField(field, value);
          }
          @java.lang.Override
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect) {
              return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect other) {
            if (other == com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.getDefaultInstance()) return this;
            if (other.getMinX() != 0) {
              setMinX(other.getMinX());
            }
            if (other.getMaxX() != 0) {
              setMaxX(other.getMaxX());
            }
            if (other.getMinY() != 0) {
              setMinY(other.getMinY());
            }
            if (other.getMaxY() != 0) {
              setMaxY(other.getMaxY());
            }
            this.mergeUnknownFields(other.getUnknownFields());
            onChanged();
            return this;
          }

          @java.lang.Override
          public final boolean isInitialized() {
            return true;
          }

          @java.lang.Override
          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            if (extensionRegistry == null) {
              throw new java.lang.NullPointerException();
            }
            try {
              boolean done = false;
              while (!done) {
                int tag = input.readTag();
                switch (tag) {
                  case 0:
                    done = true;
                    break;
                  case 8: {
                    minX_ = input.readUInt32();
                    bitField0_ |= 0x00000001;
                    break;
                  } // case 8
                  case 16: {
                    maxX_ = input.readUInt32();
                    bitField0_ |= 0x00000002;
                    break;
                  } // case 16
                  case 24: {
                    minY_ = input.readUInt32();
                    bitField0_ |= 0x00000004;
                    break;
                  } // case 24
                  case 32: {
                    maxY_ = input.readUInt32();
                    bitField0_ |= 0x00000008;
                    break;
                  } // case 32
                  default: {
                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                      done = true; // was an endgroup tag
                    }
                    break;
                  } // default:
                } // switch (tag)
              } // while (!done)
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.unwrapIOException();
            } finally {
              onChanged();
            } // finally
            return this;
          }
          private int bitField0_;

          private int minX_ ;
          /**
           * <pre>
           * 矩形框的左上角 X 坐标
           * </pre>
           *
           * <code>uint32 minX = 1;</code>
           * @return The minX.
           */
          @java.lang.Override
          public int getMinX() {
            return minX_;
          }
          /**
           * <pre>
           * 矩形框的左上角 X 坐标
           * </pre>
           *
           * <code>uint32 minX = 1;</code>
           * @param value The minX to set.
           * @return This builder for chaining.
           */
          public Builder setMinX(int value) {

            minX_ = value;
            bitField0_ |= 0x00000001;
            onChanged();
            return this;
          }
          /**
           * <pre>
           * 矩形框的左上角 X 坐标
           * </pre>
           *
           * <code>uint32 minX = 1;</code>
           * @return This builder for chaining.
           */
          public Builder clearMinX() {
            bitField0_ = (bitField0_ & ~0x00000001);
            minX_ = 0;
            onChanged();
            return this;
          }

          private int maxX_ ;
          /**
           * <pre>
           * 矩形框的右下角 X 坐标
           * </pre>
           *
           * <code>uint32 maxX = 2;</code>
           * @return The maxX.
           */
          @java.lang.Override
          public int getMaxX() {
            return maxX_;
          }
          /**
           * <pre>
           * 矩形框的右下角 X 坐标
           * </pre>
           *
           * <code>uint32 maxX = 2;</code>
           * @param value The maxX to set.
           * @return This builder for chaining.
           */
          public Builder setMaxX(int value) {

            maxX_ = value;
            bitField0_ |= 0x00000002;
            onChanged();
            return this;
          }
          /**
           * <pre>
           * 矩形框的右下角 X 坐标
           * </pre>
           *
           * <code>uint32 maxX = 2;</code>
           * @return This builder for chaining.
           */
          public Builder clearMaxX() {
            bitField0_ = (bitField0_ & ~0x00000002);
            maxX_ = 0;
            onChanged();
            return this;
          }

          private int minY_ ;
          /**
           * <pre>
           * 矩形框的左上角 Y 坐标
           * </pre>
           *
           * <code>uint32 minY = 3;</code>
           * @return The minY.
           */
          @java.lang.Override
          public int getMinY() {
            return minY_;
          }
          /**
           * <pre>
           * 矩形框的左上角 Y 坐标
           * </pre>
           *
           * <code>uint32 minY = 3;</code>
           * @param value The minY to set.
           * @return This builder for chaining.
           */
          public Builder setMinY(int value) {

            minY_ = value;
            bitField0_ |= 0x00000004;
            onChanged();
            return this;
          }
          /**
           * <pre>
           * 矩形框的左上角 Y 坐标
           * </pre>
           *
           * <code>uint32 minY = 3;</code>
           * @return This builder for chaining.
           */
          public Builder clearMinY() {
            bitField0_ = (bitField0_ & ~0x00000004);
            minY_ = 0;
            onChanged();
            return this;
          }

          private int maxY_ ;
          /**
           * <pre>
           * 矩形框的右下角 Y 坐标
           * </pre>
           *
           * <code>uint32 maxY = 4;</code>
           * @return The maxY.
           */
          @java.lang.Override
          public int getMaxY() {
            return maxY_;
          }
          /**
           * <pre>
           * 矩形框的右下角 Y 坐标
           * </pre>
           *
           * <code>uint32 maxY = 4;</code>
           * @param value The maxY to set.
           * @return This builder for chaining.
           */
          public Builder setMaxY(int value) {

            maxY_ = value;
            bitField0_ |= 0x00000008;
            onChanged();
            return this;
          }
          /**
           * <pre>
           * 矩形框的右下角 Y 坐标
           * </pre>
           *
           * <code>uint32 maxY = 4;</code>
           * @return This builder for chaining.
           */
          public Builder clearMaxY() {
            bitField0_ = (bitField0_ & ~0x00000008);
            maxY_ = 0;
            onChanged();
            return this;
          }
          @java.lang.Override
          public final Builder setUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.setUnknownFields(unknownFields);
          }

          @java.lang.Override
          public final Builder mergeUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.mergeUnknownFields(unknownFields);
          }


          // @@protoc_insertion_point(builder_scope:pb.OnAIResultGotReply.Result.Rect)
        }

        // @@protoc_insertion_point(class_scope:pb.OnAIResultGotReply.Result.Rect)
        private static final com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect();
        }

        public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<Rect>
            PARSER = new com.google.protobuf.AbstractParser<Rect>() {
          @java.lang.Override
          public Rect parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            Builder builder = newBuilder();
            try {
              builder.mergeFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              throw e.setUnfinishedMessage(builder.buildPartial());
            } catch (com.google.protobuf.UninitializedMessageException e) {
              throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
            } catch (java.io.IOException e) {
              throw new com.google.protobuf.InvalidProtocolBufferException(e)
                  .setUnfinishedMessage(builder.buildPartial());
            }
            return builder.buildPartial();
          }
        };

        public static com.google.protobuf.Parser<Rect> parser() {
          return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<Rect> getParserForType() {
          return PARSER;
        }

        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      private int bitField0_;
      public static final int RECT_FIELD_NUMBER = 1;
      private com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect rect_;
      /**
       * <pre>
       * 目标框的坐标
       * </pre>
       *
       * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
       * @return Whether the rect field is set.
       */
      @java.lang.Override
      public boolean hasRect() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 目标框的坐标
       * </pre>
       *
       * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
       * @return The rect.
       */
      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect getRect() {
        return rect_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.getDefaultInstance() : rect_;
      }
      /**
       * <pre>
       * 目标框的坐标
       * </pre>
       *
       * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
       */
      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.RectOrBuilder getRectOrBuilder() {
        return rect_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.getDefaultInstance() : rect_;
      }

      public static final int LABEL_FIELD_NUMBER = 2;
      private int label_ = 0;
      /**
       * <pre>
       * 目标的标签ID
       * </pre>
       *
       * <code>uint32 label = 2;</code>
       * @return The label.
       */
      @java.lang.Override
      public int getLabel() {
        return label_;
      }

      public static final int PROB_FIELD_NUMBER = 3;
      private double prob_ = 0D;
      /**
       * <pre>
       * 目标的识别概率
       * </pre>
       *
       * <code>double prob = 3;</code>
       * @return The prob.
       */
      @java.lang.Override
      public double getProb() {
        return prob_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeMessage(1, getRect());
        }
        if (label_ != 0) {
          output.writeUInt32(2, label_);
        }
        if (java.lang.Double.doubleToRawLongBits(prob_) != 0) {
          output.writeDouble(3, prob_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, getRect());
        }
        if (label_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(2, label_);
        }
        if (java.lang.Double.doubleToRawLongBits(prob_) != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeDoubleSize(3, prob_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result)) {
          return super.equals(obj);
        }
        com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result other = (com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result) obj;

        if (hasRect() != other.hasRect()) return false;
        if (hasRect()) {
          if (!getRect()
              .equals(other.getRect())) return false;
        }
        if (getLabel()
            != other.getLabel()) return false;
        if (java.lang.Double.doubleToLongBits(getProb())
            != java.lang.Double.doubleToLongBits(
                other.getProb())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasRect()) {
          hash = (37 * hash) + RECT_FIELD_NUMBER;
          hash = (53 * hash) + getRect().hashCode();
        }
        hash = (37 * hash) + LABEL_FIELD_NUMBER;
        hash = (53 * hash) + getLabel();
        hash = (37 * hash) + PROB_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getProb()));
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * 结果数据，包含目标检测框和相关信息
       * </pre>
       *
       * Protobuf type {@code pb.OnAIResultGotReply.Result}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:pb.OnAIResultGotReply.Result)
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder.class);
        }

        // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
            getRectFieldBuilder();
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          rect_ = null;
          if (rectBuilder_ != null) {
            rectBuilder_.dispose();
            rectBuilder_ = null;
          }
          label_ = 0;
          prob_ = 0D;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_Result_descriptor;
        }

        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result getDefaultInstanceForType() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.getDefaultInstance();
        }

        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result build() {
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result buildPartial() {
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result result = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result result) {
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.rect_ = rectBuilder_ == null
                ? rect_
                : rectBuilder_.build();
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.label_ = label_;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.prob_ = prob_;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result) {
            return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result other) {
          if (other == com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.getDefaultInstance()) return this;
          if (other.hasRect()) {
            mergeRect(other.getRect());
          }
          if (other.getLabel() != 0) {
            setLabel(other.getLabel());
          }
          if (other.getProb() != 0D) {
            setProb(other.getProb());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 10: {
                  input.readMessage(
                      getRectFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00000001;
                  break;
                } // case 10
                case 16: {
                  label_ = input.readUInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 25: {
                  prob_ = input.readDouble();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 25
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect rect_;
        private com.google.protobuf.SingleFieldBuilderV3<
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.RectOrBuilder> rectBuilder_;
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         * @return Whether the rect field is set.
         */
        public boolean hasRect() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         * @return The rect.
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect getRect() {
          if (rectBuilder_ == null) {
            return rect_ == null ? com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.getDefaultInstance() : rect_;
          } else {
            return rectBuilder_.getMessage();
          }
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         */
        public Builder setRect(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect value) {
          if (rectBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            rect_ = value;
          } else {
            rectBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         */
        public Builder setRect(
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.Builder builderForValue) {
          if (rectBuilder_ == null) {
            rect_ = builderForValue.build();
          } else {
            rectBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         */
        public Builder mergeRect(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect value) {
          if (rectBuilder_ == null) {
            if (((bitField0_ & 0x00000001) != 0) &&
              rect_ != null &&
              rect_ != com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.getDefaultInstance()) {
              getRectBuilder().mergeFrom(value);
            } else {
              rect_ = value;
            }
          } else {
            rectBuilder_.mergeFrom(value);
          }
          if (rect_ != null) {
            bitField0_ |= 0x00000001;
            onChanged();
          }
          return this;
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         */
        public Builder clearRect() {
          bitField0_ = (bitField0_ & ~0x00000001);
          rect_ = null;
          if (rectBuilder_ != null) {
            rectBuilder_.dispose();
            rectBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.Builder getRectBuilder() {
          bitField0_ |= 0x00000001;
          onChanged();
          return getRectFieldBuilder().getBuilder();
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.RectOrBuilder getRectOrBuilder() {
          if (rectBuilder_ != null) {
            return rectBuilder_.getMessageOrBuilder();
          } else {
            return rect_ == null ?
                com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.getDefaultInstance() : rect_;
          }
        }
        /**
         * <pre>
         * 目标框的坐标
         * </pre>
         *
         * <code>.pb.OnAIResultGotReply.Result.Rect rect = 1;</code>
         */
        private com.google.protobuf.SingleFieldBuilderV3<
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.RectOrBuilder> 
            getRectFieldBuilder() {
          if (rectBuilder_ == null) {
            rectBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Rect.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.RectOrBuilder>(
                    getRect(),
                    getParentForChildren(),
                    isClean());
            rect_ = null;
          }
          return rectBuilder_;
        }

        private int label_ ;
        /**
         * <pre>
         * 目标的标签ID
         * </pre>
         *
         * <code>uint32 label = 2;</code>
         * @return The label.
         */
        @java.lang.Override
        public int getLabel() {
          return label_;
        }
        /**
         * <pre>
         * 目标的标签ID
         * </pre>
         *
         * <code>uint32 label = 2;</code>
         * @param value The label to set.
         * @return This builder for chaining.
         */
        public Builder setLabel(int value) {

          label_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 目标的标签ID
         * </pre>
         *
         * <code>uint32 label = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearLabel() {
          bitField0_ = (bitField0_ & ~0x00000002);
          label_ = 0;
          onChanged();
          return this;
        }

        private double prob_ ;
        /**
         * <pre>
         * 目标的识别概率
         * </pre>
         *
         * <code>double prob = 3;</code>
         * @return The prob.
         */
        @java.lang.Override
        public double getProb() {
          return prob_;
        }
        /**
         * <pre>
         * 目标的识别概率
         * </pre>
         *
         * <code>double prob = 3;</code>
         * @param value The prob to set.
         * @return This builder for chaining.
         */
        public Builder setProb(double value) {

          prob_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 目标的识别概率
         * </pre>
         *
         * <code>double prob = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearProb() {
          bitField0_ = (bitField0_ & ~0x00000004);
          prob_ = 0D;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:pb.OnAIResultGotReply.Result)
      }

      // @@protoc_insertion_point(class_scope:pb.OnAIResultGotReply.Result)
      private static final com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result();
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Result>
          PARSER = new com.google.protobuf.AbstractParser<Result>() {
        @java.lang.Override
        public Result parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Result> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Result> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface ResultWrapperOrBuilder extends
        // @@protoc_insertion_point(interface_extends:pb.OnAIResultGotReply.ResultWrapper)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 这个是否需要上报-&gt;若为TRUE,哪怕 rs 字段为空数组,也会推送到客户端
       * </pre>
       *
       * <code>bool shouldUpdate = 1;</code>
       * @return The shouldUpdate.
       */
      boolean getShouldUpdate();

      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result> 
          getRsList();
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result getRs(int index);
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      int getRsCount();
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      java.util.List<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder> 
          getRsOrBuilderList();
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder getRsOrBuilder(
          int index);

      /**
       * <pre>
       * 对应算法
       * </pre>
       *
       * <code>.pb.DetectionAlgorithm algo = 3;</code>
       * @return The enum numeric value on the wire for algo.
       */
      int getAlgoValue();
      /**
       * <pre>
       * 对应算法
       * </pre>
       *
       * <code>.pb.DetectionAlgorithm algo = 3;</code>
       * @return The algo.
       */
      com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgo();

      /**
       * <pre>
       * 算法执行描述
       * </pre>
       *
       * <code>string desc = 4;</code>
       * @return The desc.
       */
      java.lang.String getDesc();
      /**
       * <pre>
       * 算法执行描述
       * </pre>
       *
       * <code>string desc = 4;</code>
       * @return The bytes for desc.
       */
      com.google.protobuf.ByteString
          getDescBytes();
    }
    /**
     * <pre>
     * 结果集合的包装器
     * </pre>
     *
     * Protobuf type {@code pb.OnAIResultGotReply.ResultWrapper}
     */
    public static final class ResultWrapper extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:pb.OnAIResultGotReply.ResultWrapper)
        ResultWrapperOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use ResultWrapper.newBuilder() to construct.
      private ResultWrapper(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private ResultWrapper() {
        rs_ = java.util.Collections.emptyList();
        algo_ = 0;
        desc_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new ResultWrapper();
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_ResultWrapper_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_ResultWrapper_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder.class);
      }

      public static final int SHOULDUPDATE_FIELD_NUMBER = 1;
      private boolean shouldUpdate_ = false;
      /**
       * <pre>
       * 这个是否需要上报-&gt;若为TRUE,哪怕 rs 字段为空数组,也会推送到客户端
       * </pre>
       *
       * <code>bool shouldUpdate = 1;</code>
       * @return The shouldUpdate.
       */
      @java.lang.Override
      public boolean getShouldUpdate() {
        return shouldUpdate_;
      }

      public static final int RS_FIELD_NUMBER = 2;
      @SuppressWarnings("serial")
      private java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result> rs_;
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      @java.lang.Override
      public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result> getRsList() {
        return rs_;
      }
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      @java.lang.Override
      public java.util.List<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder> 
          getRsOrBuilderList() {
        return rs_;
      }
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      @java.lang.Override
      public int getRsCount() {
        return rs_.size();
      }
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result getRs(int index) {
        return rs_.get(index);
      }
      /**
       * <pre>
       * 这个算法的多个检测结果
       * </pre>
       *
       * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
       */
      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder getRsOrBuilder(
          int index) {
        return rs_.get(index);
      }

      public static final int ALGO_FIELD_NUMBER = 3;
      private int algo_ = 0;
      /**
       * <pre>
       * 对应算法
       * </pre>
       *
       * <code>.pb.DetectionAlgorithm algo = 3;</code>
       * @return The enum numeric value on the wire for algo.
       */
      @java.lang.Override public int getAlgoValue() {
        return algo_;
      }
      /**
       * <pre>
       * 对应算法
       * </pre>
       *
       * <code>.pb.DetectionAlgorithm algo = 3;</code>
       * @return The algo.
       */
      @java.lang.Override public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgo() {
        com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm result = com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.forNumber(algo_);
        return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.UNRECOGNIZED : result;
      }

      public static final int DESC_FIELD_NUMBER = 4;
      @SuppressWarnings("serial")
      private volatile java.lang.Object desc_ = "";
      /**
       * <pre>
       * 算法执行描述
       * </pre>
       *
       * <code>string desc = 4;</code>
       * @return The desc.
       */
      @java.lang.Override
      public java.lang.String getDesc() {
        java.lang.Object ref = desc_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          desc_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 算法执行描述
       * </pre>
       *
       * <code>string desc = 4;</code>
       * @return The bytes for desc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getDescBytes() {
        java.lang.Object ref = desc_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          desc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (shouldUpdate_ != false) {
          output.writeBool(1, shouldUpdate_);
        }
        for (int i = 0; i < rs_.size(); i++) {
          output.writeMessage(2, rs_.get(i));
        }
        if (algo_ != com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.DETECTION_ALGORITHM_UNKNOWN.getNumber()) {
          output.writeEnum(3, algo_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 4, desc_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (shouldUpdate_ != false) {
          size += com.google.protobuf.CodedOutputStream
            .computeBoolSize(1, shouldUpdate_);
        }
        for (int i = 0; i < rs_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, rs_.get(i));
        }
        if (algo_ != com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.DETECTION_ALGORITHM_UNKNOWN.getNumber()) {
          size += com.google.protobuf.CodedOutputStream
            .computeEnumSize(3, algo_);
        }
        if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, desc_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper)) {
          return super.equals(obj);
        }
        com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper other = (com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper) obj;

        if (getShouldUpdate()
            != other.getShouldUpdate()) return false;
        if (!getRsList()
            .equals(other.getRsList())) return false;
        if (algo_ != other.algo_) return false;
        if (!getDesc()
            .equals(other.getDesc())) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + SHOULDUPDATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getShouldUpdate());
        if (getRsCount() > 0) {
          hash = (37 * hash) + RS_FIELD_NUMBER;
          hash = (53 * hash) + getRsList().hashCode();
        }
        hash = (37 * hash) + ALGO_FIELD_NUMBER;
        hash = (53 * hash) + algo_;
        hash = (37 * hash) + DESC_FIELD_NUMBER;
        hash = (53 * hash) + getDesc().hashCode();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * 结果集合的包装器
       * </pre>
       *
       * Protobuf type {@code pb.OnAIResultGotReply.ResultWrapper}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:pb.OnAIResultGotReply.ResultWrapper)
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_ResultWrapper_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_ResultWrapper_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder.class);
        }

        // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.newBuilder()
        private Builder() {

        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);

        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          shouldUpdate_ = false;
          if (rsBuilder_ == null) {
            rs_ = java.util.Collections.emptyList();
          } else {
            rs_ = null;
            rsBuilder_.clear();
          }
          bitField0_ = (bitField0_ & ~0x00000002);
          algo_ = 0;
          desc_ = "";
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_ResultWrapper_descriptor;
        }

        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper getDefaultInstanceForType() {
          return com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.getDefaultInstance();
        }

        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper build() {
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper buildPartial() {
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper result = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper(this);
          buildPartialRepeatedFields(result);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartialRepeatedFields(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper result) {
          if (rsBuilder_ == null) {
            if (((bitField0_ & 0x00000002) != 0)) {
              rs_ = java.util.Collections.unmodifiableList(rs_);
              bitField0_ = (bitField0_ & ~0x00000002);
            }
            result.rs_ = rs_;
          } else {
            result.rs_ = rsBuilder_.build();
          }
        }

        private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.shouldUpdate_ = shouldUpdate_;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.algo_ = algo_;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.desc_ = desc_;
          }
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper) {
            return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper other) {
          if (other == com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.getDefaultInstance()) return this;
          if (other.getShouldUpdate() != false) {
            setShouldUpdate(other.getShouldUpdate());
          }
          if (rsBuilder_ == null) {
            if (!other.rs_.isEmpty()) {
              if (rs_.isEmpty()) {
                rs_ = other.rs_;
                bitField0_ = (bitField0_ & ~0x00000002);
              } else {
                ensureRsIsMutable();
                rs_.addAll(other.rs_);
              }
              onChanged();
            }
          } else {
            if (!other.rs_.isEmpty()) {
              if (rsBuilder_.isEmpty()) {
                rsBuilder_.dispose();
                rsBuilder_ = null;
                rs_ = other.rs_;
                bitField0_ = (bitField0_ & ~0x00000002);
                rsBuilder_ = 
                  com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                     getRsFieldBuilder() : null;
              } else {
                rsBuilder_.addAllMessages(other.rs_);
              }
            }
          }
          if (other.algo_ != 0) {
            setAlgoValue(other.getAlgoValue());
          }
          if (!other.getDesc().isEmpty()) {
            desc_ = other.desc_;
            bitField0_ |= 0x00000008;
            onChanged();
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  shouldUpdate_ = input.readBool();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 18: {
                  com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result m =
                      input.readMessage(
                          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.parser(),
                          extensionRegistry);
                  if (rsBuilder_ == null) {
                    ensureRsIsMutable();
                    rs_.add(m);
                  } else {
                    rsBuilder_.addMessage(m);
                  }
                  break;
                } // case 18
                case 24: {
                  algo_ = input.readEnum();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 24
                case 34: {
                  desc_ = input.readStringRequireUtf8();
                  bitField0_ |= 0x00000008;
                  break;
                } // case 34
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private boolean shouldUpdate_ ;
        /**
         * <pre>
         * 这个是否需要上报-&gt;若为TRUE,哪怕 rs 字段为空数组,也会推送到客户端
         * </pre>
         *
         * <code>bool shouldUpdate = 1;</code>
         * @return The shouldUpdate.
         */
        @java.lang.Override
        public boolean getShouldUpdate() {
          return shouldUpdate_;
        }
        /**
         * <pre>
         * 这个是否需要上报-&gt;若为TRUE,哪怕 rs 字段为空数组,也会推送到客户端
         * </pre>
         *
         * <code>bool shouldUpdate = 1;</code>
         * @param value The shouldUpdate to set.
         * @return This builder for chaining.
         */
        public Builder setShouldUpdate(boolean value) {

          shouldUpdate_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 这个是否需要上报-&gt;若为TRUE,哪怕 rs 字段为空数组,也会推送到客户端
         * </pre>
         *
         * <code>bool shouldUpdate = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearShouldUpdate() {
          bitField0_ = (bitField0_ & ~0x00000001);
          shouldUpdate_ = false;
          onChanged();
          return this;
        }

        private java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result> rs_ =
          java.util.Collections.emptyList();
        private void ensureRsIsMutable() {
          if (!((bitField0_ & 0x00000002) != 0)) {
            rs_ = new java.util.ArrayList<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result>(rs_);
            bitField0_ |= 0x00000002;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilderV3<
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder> rsBuilder_;

        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result> getRsList() {
          if (rsBuilder_ == null) {
            return java.util.Collections.unmodifiableList(rs_);
          } else {
            return rsBuilder_.getMessageList();
          }
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public int getRsCount() {
          if (rsBuilder_ == null) {
            return rs_.size();
          } else {
            return rsBuilder_.getCount();
          }
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result getRs(int index) {
          if (rsBuilder_ == null) {
            return rs_.get(index);
          } else {
            return rsBuilder_.getMessage(index);
          }
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder setRs(
            int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result value) {
          if (rsBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureRsIsMutable();
            rs_.set(index, value);
            onChanged();
          } else {
            rsBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder setRs(
            int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder builderForValue) {
          if (rsBuilder_ == null) {
            ensureRsIsMutable();
            rs_.set(index, builderForValue.build());
            onChanged();
          } else {
            rsBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder addRs(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result value) {
          if (rsBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureRsIsMutable();
            rs_.add(value);
            onChanged();
          } else {
            rsBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder addRs(
            int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result value) {
          if (rsBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureRsIsMutable();
            rs_.add(index, value);
            onChanged();
          } else {
            rsBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder addRs(
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder builderForValue) {
          if (rsBuilder_ == null) {
            ensureRsIsMutable();
            rs_.add(builderForValue.build());
            onChanged();
          } else {
            rsBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder addRs(
            int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder builderForValue) {
          if (rsBuilder_ == null) {
            ensureRsIsMutable();
            rs_.add(index, builderForValue.build());
            onChanged();
          } else {
            rsBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder addAllRs(
            java.lang.Iterable<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result> values) {
          if (rsBuilder_ == null) {
            ensureRsIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, rs_);
            onChanged();
          } else {
            rsBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder clearRs() {
          if (rsBuilder_ == null) {
            rs_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000002);
            onChanged();
          } else {
            rsBuilder_.clear();
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public Builder removeRs(int index) {
          if (rsBuilder_ == null) {
            ensureRsIsMutable();
            rs_.remove(index);
            onChanged();
          } else {
            rsBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder getRsBuilder(
            int index) {
          return getRsFieldBuilder().getBuilder(index);
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder getRsOrBuilder(
            int index) {
          if (rsBuilder_ == null) {
            return rs_.get(index);  } else {
            return rsBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public java.util.List<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder> 
             getRsOrBuilderList() {
          if (rsBuilder_ != null) {
            return rsBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(rs_);
          }
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder addRsBuilder() {
          return getRsFieldBuilder().addBuilder(
              com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.getDefaultInstance());
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder addRsBuilder(
            int index) {
          return getRsFieldBuilder().addBuilder(
              index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.getDefaultInstance());
        }
        /**
         * <pre>
         * 这个算法的多个检测结果
         * </pre>
         *
         * <code>repeated .pb.OnAIResultGotReply.Result rs = 2;</code>
         */
        public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder> 
             getRsBuilderList() {
          return getRsFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilderV3<
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder> 
            getRsFieldBuilder() {
          if (rsBuilder_ == null) {
            rsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Result.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultOrBuilder>(
                    rs_,
                    ((bitField0_ & 0x00000002) != 0),
                    getParentForChildren(),
                    isClean());
            rs_ = null;
          }
          return rsBuilder_;
        }

        private int algo_ = 0;
        /**
         * <pre>
         * 对应算法
         * </pre>
         *
         * <code>.pb.DetectionAlgorithm algo = 3;</code>
         * @return The enum numeric value on the wire for algo.
         */
        @java.lang.Override public int getAlgoValue() {
          return algo_;
        }
        /**
         * <pre>
         * 对应算法
         * </pre>
         *
         * <code>.pb.DetectionAlgorithm algo = 3;</code>
         * @param value The enum numeric value on the wire for algo to set.
         * @return This builder for chaining.
         */
        public Builder setAlgoValue(int value) {
          algo_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 对应算法
         * </pre>
         *
         * <code>.pb.DetectionAlgorithm algo = 3;</code>
         * @return The algo.
         */
        @java.lang.Override
        public com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm getAlgo() {
          com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm result = com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.forNumber(algo_);
          return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm.UNRECOGNIZED : result;
        }
        /**
         * <pre>
         * 对应算法
         * </pre>
         *
         * <code>.pb.DetectionAlgorithm algo = 3;</code>
         * @param value The algo to set.
         * @return This builder for chaining.
         */
        public Builder setAlgo(com.saida.analysis.pb.TaskExchangeOuterClass.DetectionAlgorithm value) {
          if (value == null) {
            throw new NullPointerException();
          }
          bitField0_ |= 0x00000004;
          algo_ = value.getNumber();
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 对应算法
         * </pre>
         *
         * <code>.pb.DetectionAlgorithm algo = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearAlgo() {
          bitField0_ = (bitField0_ & ~0x00000004);
          algo_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object desc_ = "";
        /**
         * <pre>
         * 算法执行描述
         * </pre>
         *
         * <code>string desc = 4;</code>
         * @return The desc.
         */
        public java.lang.String getDesc() {
          java.lang.Object ref = desc_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            desc_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 算法执行描述
         * </pre>
         *
         * <code>string desc = 4;</code>
         * @return The bytes for desc.
         */
        public com.google.protobuf.ByteString
            getDescBytes() {
          java.lang.Object ref = desc_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            desc_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 算法执行描述
         * </pre>
         *
         * <code>string desc = 4;</code>
         * @param value The desc to set.
         * @return This builder for chaining.
         */
        public Builder setDesc(
            java.lang.String value) {
          if (value == null) { throw new NullPointerException(); }
          desc_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 算法执行描述
         * </pre>
         *
         * <code>string desc = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearDesc() {
          desc_ = getDefaultInstance().getDesc();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 算法执行描述
         * </pre>
         *
         * <code>string desc = 4;</code>
         * @param value The bytes for desc to set.
         * @return This builder for chaining.
         */
        public Builder setDescBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) { throw new NullPointerException(); }
          checkByteStringIsUtf8(value);
          desc_ = value;
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:pb.OnAIResultGotReply.ResultWrapper)
      }

      // @@protoc_insertion_point(class_scope:pb.OnAIResultGotReply.ResultWrapper)
      private static final com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper();
      }

      public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<ResultWrapper>
          PARSER = new com.google.protobuf.AbstractParser<ResultWrapper>() {
        @java.lang.Override
        public ResultWrapper parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<ResultWrapper> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<ResultWrapper> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int FMT_FIELD_NUMBER = 1;
    private int fmt_ = 0;
    /**
     * <pre>
     * 图像格式
     * </pre>
     *
     * <code>.pb.ImageFormat fmt = 1;</code>
     * @return The enum numeric value on the wire for fmt.
     */
    @java.lang.Override public int getFmtValue() {
      return fmt_;
    }
    /**
     * <pre>
     * 图像格式
     * </pre>
     *
     * <code>.pb.ImageFormat fmt = 1;</code>
     * @return The fmt.
     */
    @java.lang.Override public com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat getFmt() {
      com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat result = com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.forNumber(fmt_);
      return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.UNRECOGNIZED : result;
    }

    public static final int IMAGEDATA_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString imageData_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 图像数据，图像的字节流
     * </pre>
     *
     * <code>bytes imageData = 2;</code>
     * @return The imageData.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getImageData() {
      return imageData_;
    }

    public static final int RESULT_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> result_;
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> getResultList() {
      return result_;
    }
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder> 
        getResultOrBuilderList() {
      return result_;
    }
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    @java.lang.Override
    public int getResultCount() {
      return result_.size();
    }
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper getResult(int index) {
      return result_.get(index);
    }
    /**
     * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
     */
    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder getResultOrBuilder(
        int index) {
      return result_.get(index);
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 4;
    private long timestamp_ = 0L;
    /**
     * <code>uint64 timestamp = 4;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public long getTimestamp() {
      return timestamp_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (fmt_ != com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_UNKNOWN.getNumber()) {
        output.writeEnum(1, fmt_);
      }
      if (!imageData_.isEmpty()) {
        output.writeBytes(2, imageData_);
      }
      for (int i = 0; i < result_.size(); i++) {
        output.writeMessage(3, result_.get(i));
      }
      if (timestamp_ != 0L) {
        output.writeUInt64(4, timestamp_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (fmt_ != com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.IMAGE_FORMAT_UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, fmt_);
      }
      if (!imageData_.isEmpty()) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, imageData_);
      }
      for (int i = 0; i < result_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, result_.get(i));
      }
      if (timestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, timestamp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply)) {
        return super.equals(obj);
      }
      com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply other = (com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply) obj;

      if (fmt_ != other.fmt_) return false;
      if (!getImageData()
          .equals(other.getImageData())) return false;
      if (!getResultList()
          .equals(other.getResultList())) return false;
      if (getTimestamp()
          != other.getTimestamp()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FMT_FIELD_NUMBER;
      hash = (53 * hash) + fmt_;
      hash = (37 * hash) + IMAGEDATA_FIELD_NUMBER;
      hash = (53 * hash) + getImageData().hashCode();
      if (getResultCount() > 0) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResultList().hashCode();
      }
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestamp());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * AI结果的响应消息
     * </pre>
     *
     * Protobuf type {@code pb.OnAIResultGotReply}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pb.OnAIResultGotReply)
        com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReplyOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.class, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.Builder.class);
      }

      // Construct using com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        fmt_ = 0;
        imageData_ = com.google.protobuf.ByteString.EMPTY;
        if (resultBuilder_ == null) {
          result_ = java.util.Collections.emptyList();
        } else {
          result_ = null;
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        timestamp_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.internal_static_pb_OnAIResultGotReply_descriptor;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply getDefaultInstanceForType() {
        return com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.getDefaultInstance();
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply build() {
        com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply buildPartial() {
        com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply result = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply result) {
        if (resultBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            result_ = java.util.Collections.unmodifiableList(result_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.result_ = result_;
        } else {
          result.result_ = resultBuilder_.build();
        }
      }

      private void buildPartial0(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.fmt_ = fmt_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.imageData_ = imageData_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.timestamp_ = timestamp_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply) {
          return mergeFrom((com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply other) {
        if (other == com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.getDefaultInstance()) return this;
        if (other.fmt_ != 0) {
          setFmtValue(other.getFmtValue());
        }
        if (other.getImageData() != com.google.protobuf.ByteString.EMPTY) {
          setImageData(other.getImageData());
        }
        if (resultBuilder_ == null) {
          if (!other.result_.isEmpty()) {
            if (result_.isEmpty()) {
              result_ = other.result_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureResultIsMutable();
              result_.addAll(other.result_);
            }
            onChanged();
          }
        } else {
          if (!other.result_.isEmpty()) {
            if (resultBuilder_.isEmpty()) {
              resultBuilder_.dispose();
              resultBuilder_ = null;
              result_ = other.result_;
              bitField0_ = (bitField0_ & ~0x00000004);
              resultBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getResultFieldBuilder() : null;
            } else {
              resultBuilder_.addAllMessages(other.result_);
            }
          }
        }
        if (other.getTimestamp() != 0L) {
          setTimestamp(other.getTimestamp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                fmt_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                imageData_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper m =
                    input.readMessage(
                        com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.parser(),
                        extensionRegistry);
                if (resultBuilder_ == null) {
                  ensureResultIsMutable();
                  result_.add(m);
                } else {
                  resultBuilder_.addMessage(m);
                }
                break;
              } // case 26
              case 32: {
                timestamp_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int fmt_ = 0;
      /**
       * <pre>
       * 图像格式
       * </pre>
       *
       * <code>.pb.ImageFormat fmt = 1;</code>
       * @return The enum numeric value on the wire for fmt.
       */
      @java.lang.Override public int getFmtValue() {
        return fmt_;
      }
      /**
       * <pre>
       * 图像格式
       * </pre>
       *
       * <code>.pb.ImageFormat fmt = 1;</code>
       * @param value The enum numeric value on the wire for fmt to set.
       * @return This builder for chaining.
       */
      public Builder setFmtValue(int value) {
        fmt_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图像格式
       * </pre>
       *
       * <code>.pb.ImageFormat fmt = 1;</code>
       * @return The fmt.
       */
      @java.lang.Override
      public com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat getFmt() {
        com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat result = com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.forNumber(fmt_);
        return result == null ? com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * 图像格式
       * </pre>
       *
       * <code>.pb.ImageFormat fmt = 1;</code>
       * @param value The fmt to set.
       * @return This builder for chaining.
       */
      public Builder setFmt(com.saida.analysis.pb.TaskExchangeOuterClass.ImageFormat value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        fmt_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图像格式
       * </pre>
       *
       * <code>.pb.ImageFormat fmt = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFmt() {
        bitField0_ = (bitField0_ & ~0x00000001);
        fmt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString imageData_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * 图像数据，图像的字节流
       * </pre>
       *
       * <code>bytes imageData = 2;</code>
       * @return The imageData.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getImageData() {
        return imageData_;
      }
      /**
       * <pre>
       * 图像数据，图像的字节流
       * </pre>
       *
       * <code>bytes imageData = 2;</code>
       * @param value The imageData to set.
       * @return This builder for chaining.
       */
      public Builder setImageData(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        imageData_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 图像数据，图像的字节流
       * </pre>
       *
       * <code>bytes imageData = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearImageData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        imageData_ = getDefaultInstance().getImageData();
        onChanged();
        return this;
      }

      private java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> result_ =
        java.util.Collections.emptyList();
      private void ensureResultIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          result_ = new java.util.ArrayList<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper>(result_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder> resultBuilder_;

      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> getResultList() {
        if (resultBuilder_ == null) {
          return java.util.Collections.unmodifiableList(result_);
        } else {
          return resultBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public int getResultCount() {
        if (resultBuilder_ == null) {
          return result_.size();
        } else {
          return resultBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper getResult(int index) {
        if (resultBuilder_ == null) {
          return result_.get(index);
        } else {
          return resultBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder setResult(
          int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.set(index, value);
          onChanged();
        } else {
          resultBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder setResult(
          int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.set(index, builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder addResult(com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.add(value);
          onChanged();
        } else {
          resultBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder addResult(
          int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.add(index, value);
          onChanged();
        } else {
          resultBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder addResult(
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.add(builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder addResult(
          int index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.add(index, builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder addAllResult(
          java.lang.Iterable<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper> values) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, result_);
          onChanged();
        } else {
          resultBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          resultBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public Builder removeResult(int index) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.remove(index);
          onChanged();
        } else {
          resultBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder getResultBuilder(
          int index) {
        return getResultFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder getResultOrBuilder(
          int index) {
        if (resultBuilder_ == null) {
          return result_.get(index);  } else {
          return resultBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public java.util.List<? extends com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder> 
           getResultOrBuilderList() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(result_);
        }
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder addResultBuilder() {
        return getResultFieldBuilder().addBuilder(
            com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.getDefaultInstance());
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder addResultBuilder(
          int index) {
        return getResultFieldBuilder().addBuilder(
            index, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.getDefaultInstance());
      }
      /**
       * <code>repeated .pb.OnAIResultGotReply.ResultWrapper result = 3;</code>
       */
      public java.util.List<com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder> 
           getResultBuilderList() {
        return getResultFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapper.Builder, com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply.ResultWrapperOrBuilder>(
                  result_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }

      private long timestamp_ ;
      /**
       * <code>uint64 timestamp = 4;</code>
       * @return The timestamp.
       */
      @java.lang.Override
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <code>uint64 timestamp = 4;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(long value) {

        timestamp_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 timestamp = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        timestamp_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pb.OnAIResultGotReply)
    }

    // @@protoc_insertion_point(class_scope:pb.OnAIResultGotReply)
    private static final com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply();
    }

    public static com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<OnAIResultGotReply>
        PARSER = new com.google.protobuf.AbstractParser<OnAIResultGotReply>() {
      @java.lang.Override
      public OnAIResultGotReply parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<OnAIResultGotReply> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnAIResultGotReply> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.saida.analysis.pb.TaskExchangeOuterClass.OnAIResultGotReply getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_VideoCommonArgs_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_VideoCommonArgs_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_StreamTask_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_StreamTask_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_FileTask_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_FileTask_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_ImageTask_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_ImageTask_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_OnAIResultGotReply_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_OnAIResultGotReply_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_OnAIResultGotReply_Result_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_OnAIResultGotReply_Result_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_OnAIResultGotReply_Result_Rect_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_OnAIResultGotReply_Result_Rect_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pb_OnAIResultGotReply_ResultWrapper_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pb_OnAIResultGotReply_ResultWrapper_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023task_exchange.proto\022\002pb\"\245\001\n\017VideoCommo" +
      "nArgs\022\026\n\016key_frame_only\030\001 \001(\010\022\022\n\nframe_s" +
      "tep\030\002 \001(\r\022\021\n\tskip_step\030\003 \001(\r\022(\n video_re" +
      "cord_duration_in_seconds\030\004 \001(\r\022)\n!callba" +
      "ck_interval_in_mill_seconds\030\005 \001(\r\"o\n\nStr" +
      "eamTask\022!\n\004args\030\001 \001(\0132\023.pb.VideoCommonAr" +
      "gs\022*\n\nalgorithms\030\002 \003(\0162\026.pb.DetectionAlg" +
      "orithm\022\022\n\nstream_url\030\003 \001(\t\"l\n\010FileTask\022!" +
      "\n\004args\030\001 \001(\0132\023.pb.VideoCommonArgs\022*\n\nalg" +
      "orithms\030\002 \003(\0162\026.pb.DetectionAlgorithm\022\021\n" +
      "\tfile_path\030\003 \001(\t\"i\n\tImageTask\022*\n\nalgorit" +
      "hms\030\001 \003(\0162\026.pb.DetectionAlgorithm\022#\n\nimg" +
      "_format\030\002 \001(\0162\017.pb.ImageFormat\022\013\n\003img\030\003 " +
      "\001(\014\"\257\003\n\022OnAIResultGotReply\022\034\n\003fmt\030\001 \001(\0162" +
      "\017.pb.ImageFormat\022\021\n\timageData\030\002 \001(\014\0224\n\006r" +
      "esult\030\003 \003(\0132$.pb.OnAIResultGotReply.Resu" +
      "ltWrapper\022\021\n\ttimestamp\030\004 \001(\004\032\227\001\n\006Result\022" +
      "0\n\004rect\030\001 \001(\0132\".pb.OnAIResultGotReply.Re" +
      "sult.Rect\022\r\n\005label\030\002 \001(\r\022\014\n\004prob\030\003 \001(\001\032>" +
      "\n\004Rect\022\014\n\004minX\030\001 \001(\r\022\014\n\004maxX\030\002 \001(\r\022\014\n\004mi" +
      "nY\030\003 \001(\r\022\014\n\004maxY\030\004 \001(\r\032\204\001\n\rResultWrapper" +
      "\022\024\n\014shouldUpdate\030\001 \001(\010\022)\n\002rs\030\002 \003(\0132\035.pb." +
      "OnAIResultGotReply.Result\022$\n\004algo\030\003 \001(\0162" +
      "\026.pb.DetectionAlgorithm\022\014\n\004desc\030\004 \001(\t*T\n" +
      "\013ImageFormat\022\030\n\024IMAGE_FORMAT_UNKNOWN\020\000\022\024" +
      "\n\020IMAGE_FORMAT_PNG\020\001\022\025\n\021IMAGE_FORMAT_JPE" +
      "G\020\002*\235\004\n\022DetectionAlgorithm\022\037\n\033DETECTION_" +
      "ALGORITHM_UNKNOWN\020\000\022\016\n\nN_DET_OCCU\020\001\022\021\n\rN" +
      "_DET_PERDUTY\020\002\022\020\n\014N_DET_SAFETY\020\003\022\r\n\tU_DE" +
      "T_CAR\020\004\022\017\n\013U_DET_FLOAT\020\005\022\017\n\013U_DET_TRASH\020" +
      "\006\022\021\n\rH_DET_BIRD_AH\020\007\022\r\n\tH_DET_CAR\020\010\022\016\n\nH" +
      "_DET_FIRE\020\t\022\022\n\016H_DET_ILLCATCH\020\n\022\022\n\016H_DET" +
      "_ILLCONST\020\013\022\021\n\rH_DET_ILLFISH\020\014\022\016\n\nH_DET_" +
      "LAND\020\r\022\020\n\014H_DET_PERSON\020\016\022\016\n\nH_DET_SHIP\020\017" +
      "\022\017\n\013H_DET_TRASH\020\020\022\r\n\tN_DET_CIG\020\021\022\r\n\tN_DE" +
      "T_ELE\020\022\022\016\n\nN_DET_FALL\020\023\022\016\n\nN_DET_FIRE\020\024\022" +
      "\020\n\014N_DET_FIREEX\020\025\022\022\n\016N_DET_PERCOUNT\020\026\022\017\n" +
      "\013N_DET_PHONE\020\027\022\020\n\014U_DET_CONVEH\020\030\022\020\n\014U_DE" +
      "T_PERCAR\020\031\022\027\n\023N_DET_ENGINEVEICHLE\020\032\022\016\n\nU" +
      "_DET_SHIP\020\033\022\016\n\nU_DET_FIRE\020\0342\300\001\n\014TaskExch" +
      "ange\022<\n\020RequestForStream\022\016.pb.StreamTask" +
      "\032\026.pb.OnAIResultGotReply0\001\0228\n\016RequestFor" +
      "File\022\014.pb.FileTask\032\026.pb.OnAIResultGotRep" +
      "ly0\001\0228\n\017RequestForImage\022\r.pb.ImageTask\032\026" +
      ".pb.OnAIResultGotReplyB\035\n\025com.saida.anal" +
      "ysis.pbZ\004.;pbb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_pb_VideoCommonArgs_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_pb_VideoCommonArgs_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_VideoCommonArgs_descriptor,
        new java.lang.String[] { "KeyFrameOnly", "FrameStep", "SkipStep", "VideoRecordDurationInSeconds", "CallbackIntervalInMillSeconds", });
    internal_static_pb_StreamTask_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_pb_StreamTask_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_StreamTask_descriptor,
        new java.lang.String[] { "Args", "Algorithms", "StreamUrl", });
    internal_static_pb_FileTask_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_pb_FileTask_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_FileTask_descriptor,
        new java.lang.String[] { "Args", "Algorithms", "FilePath", });
    internal_static_pb_ImageTask_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_pb_ImageTask_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_ImageTask_descriptor,
        new java.lang.String[] { "Algorithms", "ImgFormat", "Img", });
    internal_static_pb_OnAIResultGotReply_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_pb_OnAIResultGotReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_OnAIResultGotReply_descriptor,
        new java.lang.String[] { "Fmt", "ImageData", "Result", "Timestamp", });
    internal_static_pb_OnAIResultGotReply_Result_descriptor =
      internal_static_pb_OnAIResultGotReply_descriptor.getNestedTypes().get(0);
    internal_static_pb_OnAIResultGotReply_Result_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_OnAIResultGotReply_Result_descriptor,
        new java.lang.String[] { "Rect", "Label", "Prob", });
    internal_static_pb_OnAIResultGotReply_Result_Rect_descriptor =
      internal_static_pb_OnAIResultGotReply_Result_descriptor.getNestedTypes().get(0);
    internal_static_pb_OnAIResultGotReply_Result_Rect_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_OnAIResultGotReply_Result_Rect_descriptor,
        new java.lang.String[] { "MinX", "MaxX", "MinY", "MaxY", });
    internal_static_pb_OnAIResultGotReply_ResultWrapper_descriptor =
      internal_static_pb_OnAIResultGotReply_descriptor.getNestedTypes().get(1);
    internal_static_pb_OnAIResultGotReply_ResultWrapper_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pb_OnAIResultGotReply_ResultWrapper_descriptor,
        new java.lang.String[] { "ShouldUpdate", "Rs", "Algo", "Desc", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
