package com.saida.analysis.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "saida-alg")
@ConditionalOnProperty(prefix = "saida-alg", name = "enable", havingValue = "true")
public class SaiDaAlgConfig {
    private Boolean enable;
    //请求地址
    private String reqAddress;

    public String getReqAddress() {
        if (reqAddress != null) {
            return reqAddress.endsWith("/") ? reqAddress : reqAddress + "/";
        }
        throw new RuntimeException("saida.reqAddress 不能为空");
    }


    // 回调地址
    private String callbackAddress;

    public String getCallbackAddress() {
        if (callbackAddress != null) {
            return callbackAddress.endsWith("/") ? callbackAddress : callbackAddress + "/";
        }
        throw new RuntimeException("saida.callbackAddress 不能为空");
    }

    // 回调方法
    private String callbackFun = "saida/doAnalysis";
}
