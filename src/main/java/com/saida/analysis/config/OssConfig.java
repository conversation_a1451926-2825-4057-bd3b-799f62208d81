package com.saida.analysis.config;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "oss")
@ConditionalOnProperty(prefix = "oss", name = "enable", havingValue = "true")
public class OssConfig {
    private static final Logger logger = LoggerFactory.getLogger(OssConfig.class);

    /**
     * endPoint是一个URL，域名，IPv4或者IPv6地址
     */
    private String endpoint;

    /**
     * accessKey类似于用户ID，用于唯一标识你的账户
     */
    private String accessKey;

    /**
     * secretKey是你账户的密码
     */
    private String secretKey;

    /**
     * 如果是true，则用的是https而不是http,默认值是true
     */
    private Boolean secure;

    /**
     * 默认存储桶名称
     */
    private String bucketDefaultName;

    /**
     * 文件访问前缀
     */
    private String returnPoint;

    /**
     * 区域
     */
    private String region;
}
