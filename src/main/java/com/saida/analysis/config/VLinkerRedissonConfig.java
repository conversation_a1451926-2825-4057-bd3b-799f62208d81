//package com.saida.analysis.config;
//
//import com.github.pagehelper.util.StringUtil;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.codec.JsonJacksonCodec;
//import org.redisson.config.Config;
//import org.redisson.config.SingleServerConfig;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.SpringApplication;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import javax.annotation.PreDestroy;
//import javax.annotation.Resource;
//
///**
// */
//@Slf4j
//@Configuration
//public class VLinkerRedissonConfig {
//
//    @Getter
//    private static final String REDIS_LOCK = "REDIS_LOCK:";
//
//    @Value("redis://${spring.redis.host}:${spring.redis.port}")
//    private String address;
//
//    @Value("${spring.redis.database}")
//    private Integer database;
//
//    @Value("${spring.redis.password}")
//    private String password;
//
//    @Value("${spring.redis.timeout}")
//    private int timeout;
//
//    @Value("${spring.redis.jedis.pool.max-active:30}")
//    private int poolMaxActive;
//
//    @Value("${spring.redis.jedis.pool.min-idle:10}")
//    private int poolMinIdle;
//
//    private RedissonClient redissonClient;
//
//    @Resource
//    private ApplicationContext applicationContext;
//
//    /**
//     * 所有对redisson的使用都是通过RedissonClient来操作的
//     */
//    @Bean(destroyMethod = "shutdown")
//    public RedissonClient redissonClient() {
//        Config config = new Config();
//        config.setCodec(JsonJacksonCodec.INSTANCE);
//        SingleServerConfig serverConfig = config.useSingleServer();
//        serverConfig.setAddress(address)
//                .setDatabase(database)
//                .setTimeout(timeout)
//                .setConnectionPoolSize(poolMaxActive)
//                .setConnectionMinimumIdleSize(poolMinIdle);
//        if (StringUtil.isNotEmpty(password)) {
//            serverConfig.setPassword(password);
//        }
//        try {
//            redissonClient = Redisson.create(config);
//        } catch (Exception e) {
//            for (int i = 0; i < 10; i++) {
//                log.error("❌ redis 初始化失败了 程序退出");
//            }
//            log.error("❌ redis 初始化失败了 程序退出", e);
//            SpringApplication.exit(applicationContext, () -> 1);
//        }
//        return redissonClient;
//    }
//
//    @PreDestroy
//    public void shutdownRedisson() {
//        log.info("应用停止，开始关闭所有的 redisson");
//        if (redissonClient != null) {
//            redissonClient.shutdown();
//            // 额外确保 Netty 线程池关闭
//            redissonClient.getExecutorService("shutdownRedisson").shutdown();
//        }
//    }
//
//}
