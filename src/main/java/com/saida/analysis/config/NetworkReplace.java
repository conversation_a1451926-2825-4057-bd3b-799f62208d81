package com.saida.analysis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "network")
public class NetworkReplace {

    private List<ReplacementDto> replacement;


    @Data
    public static class ReplacementDto {
        private String id;
        private String oldUrl;
        private String newUrl;
    }
}


