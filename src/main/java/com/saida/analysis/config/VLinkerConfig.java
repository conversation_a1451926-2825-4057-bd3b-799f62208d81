package com.saida.analysis.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;

@Getter
@Slf4j
@Component
public class VLinkerConfig {

    @Value("${vLinker.nodeId:''}")
    private String nodeId = "";

    @PostConstruct
    public void init() {
        log.info("当前节点 nodeId:{}", nodeId);
    }
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
