package com.saida.analysis.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "fu-an-alg")
@ConditionalOnProperty(prefix = "fu-an-alg", name = "enable", havingValue = "true")
public class FuAnAlgConfig {
    private Boolean enable;
    //请求地址
    private String reqAddress;
    //api
    private String submitAITaskUri = "/api/iland/v1/alarmService/submitAITask";

    public String getReqAddress() {
        if (reqAddress != null) {
            return reqAddress.endsWith("/") ? reqAddress : reqAddress + "/";
        }
        throw new RuntimeException("fu-an-alg.reqAddress 不能为空");
    }

    // 回调地址
    private String callbackAddress;

    public String getCallbackAddress() {
        if (callbackAddress != null) {
            return callbackAddress.endsWith("/") ? callbackAddress : callbackAddress + "/";
        }
        throw new RuntimeException("fu-an-alg.callbackAddress 不能为空");
    }

    // 回调方法
    private String callbackFun = "fuAnAlg/doAnalysis";


    //云存前缀
    private String cloudStoragePrefix = "fuanAlg";
}
