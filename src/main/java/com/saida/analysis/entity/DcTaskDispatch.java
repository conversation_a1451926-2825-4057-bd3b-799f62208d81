package com.saida.analysis.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class DcTaskDispatch implements Serializable {

    /** ID */
    private Long id;

    /** 任务名称 */
    private String name;

    /** 设备id */
    private Long deviceId;

    /** 云服务id */
    private Long cloudServerId;

    /** 时间计划id */
    private Long timeTemplateId;

    /** 算法ID */
    private Long algorithmId;

    /**
     * 算法名称，对应算法唯一标识
     */
    private String algorithmName;

    /**
     * 算法名称，对应算法唯一标识
     */
    private String algorithmCode;

    /**
     * 算法名称，对应算法唯一标识
     */
    private String algorithmMinCode;

    /**
     * 算法参数配置
     * 例：{
     *     "defaultFrameRate": "1",	//N帧
     *     "defaultFrameExtractionTime": "10",	//N秒
     *     "humNum": "1",  //聚集人数
     *     "stayTime": "11",  //停留时长
     *     "sensitivity": 50,  //灵敏度
     *     "faultTolerantFrameRate": 50,  //容错帧率
     *     "alarmDuration": "5"  //告警时长
     * }
     */
    private String algorithmParamConfig;


    /**
     * 时间计划
     * [{"timeList":[{"endTime":"23:59","startTime":"00:00"}],"weekIndex":0},{"timeList":[{"endTime":"23:59","startTime":"00:00"}],"weekIndex":1},{"timeList":[{"endTime":"23:59","startTime":"00:00"}],"weekIndex":2},{"timeList":[{"endTime":"23:59","startTime":"00:00"}],"weekIndex":3},{"timeList":[{"endTime":"23:59","startTime":"00:00"}],"weekIndex":4},{"timeList":[{"endTime":"23:59","startTime":"00:00"}],"weekIndex":5},{"timeList":[{"endTime":"23:59","startTime":"00:00"}],"weekIndex":6}]
     */
    private String timePlan;

    /** 自动跟踪 0：跟踪 1：不跟踪 */
    private Integer autoTrace;
    /**
     * 算法叠框 0：禁用 1：启用
     */
    private Integer cascadeDetection;

    /** 预设点设置信息 */
    private String presetPointSetDetail;

    /** 运行状态 0：未开始 1：运行中 2：暂停 3：失败 */
    private Integer status;


}
