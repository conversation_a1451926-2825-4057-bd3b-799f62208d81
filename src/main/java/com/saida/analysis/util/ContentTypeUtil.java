package com.saida.analysis.util;

import com.github.pagehelper.util.StringUtil;

import java.util.HashMap;
import java.util.Map;

public class ContentTypeUtil {

    // 定义一个MIME类型映射
    private static final Map<String, String> MIME_TYPES = new HashMap<>();

    static {
        // 初始化MIME类型映射
        MIME_TYPES.put("jpg", "image/jpeg");
        MIME_TYPES.put("jpeg", "image/jpeg");
        MIME_TYPES.put("png", "image/png");
        MIME_TYPES.put("gif", "image/gif");
        MIME_TYPES.put("bmp", "image/bmp");
        MIME_TYPES.put("mp4", "video/mp4");
        MIME_TYPES.put("avi", "video/x-msvideo");
        MIME_TYPES.put("mov", "video/quicktime");
        MIME_TYPES.put("doc", "application/msword");
        MIME_TYPES.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_TYPES.put("pdf", "application/pdf");
        // 可以根据需要添加更多文件类型和对应的MIME类型
    }

    public static String getMimeType(String fileName) {
        String extension = getFileExtension(fileName);
        return MIME_TYPES.getOrDefault(extension, "application/octet-stream");
    }

    private static String getFileExtension(String fileName) {
        if (StringUtil.isEmpty(fileName)) {
            return "";
        }
        int i = fileName.lastIndexOf('.');
        if (i > 0) {
            return fileName.substring(i + 1).toLowerCase();
        }
        return "";
    }
}
