package com.saida.analysis.util;

import com.saida.analysis.dto.GrpcAlgorithmResultDto;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 检测非法垂钓的工具类
 */
public class IllegalFishingDetector {

    /**
     * 根据算法检测结果集合，计算出所有非法垂钓的组合框
     * 组合规则：
     * 1. 组合对象为 "人+鱼竿" 或 "人+渔网"。
     * 2. 两个对象的矩形框必须相交，
     * 或者水平、垂直方向上的间隔都不超过30像素。
     * 3. 组合后的外接矩形框正好框住这两个对象，且 label 设为 -1 表示非法垂钓。
     *
     * @param algorithmResultDtoList 原始检测结果集合
     * @return 非法垂钓组合后的矩形框集合
     */
    public static List<GrpcAlgorithmResultDto> detectIllegalFishing(List<GrpcAlgorithmResultDto> algorithmResultDtoList, int w, int h) {
        // 分别存储人、鱼竿和渔网的检测结果
        List<GrpcAlgorithmResultDto> persons = new ArrayList<>();
        List<GrpcAlgorithmResultDto> rods = new ArrayList<>();
        List<GrpcAlgorithmResultDto> nets = new ArrayList<>();
        List<GrpcAlgorithmResultDto> illegalList = new ArrayList<>();

        // 根据 label 分类
        for (GrpcAlgorithmResultDto dto : algorithmResultDtoList) {
            switch (dto.getLabel()) {
                case 0:
                    // 横向放大一倍并确保不超出边界
                    int width = dto.getMaxx() - dto.getMinx();
                    int newMinX = Math.max(0, dto.getMinx() - width / 2);
                    int newMaxX = Math.min(w, dto.getMaxx() + width / 2);
                    dto.setMinx(newMinX);
                    dto.setMaxx(newMaxX);
                    persons.add(dto);
                    break;
                case 59:
                    rods.add(dto);
                    break;
                case 60:
                    nets.add(dto);
                    break;
                default:
                    illegalList.add(dto);
                    break;
            }
        }

        // 使用迭代器遍历 persons 列表
        Iterator<GrpcAlgorithmResultDto> personIterator = persons.iterator();
        while (personIterator.hasNext()) {
            GrpcAlgorithmResultDto person = personIterator.next();
            boolean matched = false;

            // 检查 “人 + 鱼竿” 的组合
            for (GrpcAlgorithmResultDto rod : rods) {
                if (areBoxesOverlappingOrClose(person, rod)) {
                    GrpcAlgorithmResultDto illegalDetection = unionBox(person, rod);
                    illegalList.add(illegalDetection);
                    matched = true;
                    break; // 匹配成功，跳出循环
                }
            }

            // 如果未匹配到鱼竿，再检查 “人 + 渔网” 的组合
            if (!matched) {
                for (GrpcAlgorithmResultDto net : nets) {
                    if (areBoxesOverlappingOrClose(person, net)) {
                        GrpcAlgorithmResultDto illegalDetection = unionBox(person, net);
                        illegalList.add(illegalDetection);
                        matched = true;
                        break; // 匹配成功，跳出循环
                    }
                }
            }

            // 如果匹配成功，将该人从列表中移除
            if (matched) {
                personIterator.remove();
            }
        }

        return illegalList;
    }

    /**
     * 判断两个矩形框是否重叠或者间隔距离不超过30像素
     */
    private static boolean areBoxesOverlappingOrClose(GrpcAlgorithmResultDto a, GrpcAlgorithmResultDto b) {
        // 如果两个矩形框相交，则认为满足条件
        if (isIntersecting(a, b)) {
            return true;
        }
        // 不相交时，计算水平和垂直方向上的间隔
        int horizontalGap = Math.max(0, Math.max(a.getMinx(), b.getMinx()) - Math.min(a.getMaxx(), b.getMaxx()));
        int verticalGap = Math.max(0, Math.max(a.getMiny(), b.getMiny()) - Math.min(a.getMaxy(), b.getMaxy()));
        return horizontalGap <= 30 && verticalGap <= 30;
    }

    /**
     * 判断两个矩形是否有交集
     */
    private static boolean isIntersecting(GrpcAlgorithmResultDto a, GrpcAlgorithmResultDto b) {
        // 如果一个矩形在另一个矩形的左侧或右侧，则不相交
        if (a.getMaxx() < b.getMinx() || b.getMaxx() < a.getMinx()) {
            return false;
        }
        // 如果一个矩形在另一个矩形的上方或下方，则不相交
        if (a.getMaxy() < b.getMiny() || b.getMaxy() < a.getMiny()) {
            return false;
        }
        return true;
    }

    /**
     * 计算两个矩形的外接矩形框
     * 这里的外接框正好框住了两个对象
     */
    private static GrpcAlgorithmResultDto unionBox(GrpcAlgorithmResultDto a, GrpcAlgorithmResultDto b) {
        GrpcAlgorithmResultDto unionDto = new GrpcAlgorithmResultDto();
        unionDto.setMinx(Math.min(a.getMinx(), b.getMinx()));
        unionDto.setMiny(Math.min(a.getMiny(), b.getMiny()));
        unionDto.setMaxx(Math.max(a.getMaxx(), b.getMaxx()));
        unionDto.setMaxy(Math.max(a.getMaxy(), b.getMaxy()));
        // 设置 label 为 -1 表示这是非法垂钓检测的结果
        unionDto.setLabel(-1);
        // 概率取两个框中较高的值（可根据需要调整）
        unionDto.setProb(Math.max(a.getProb(), b.getProb()));
        return unionDto;
    }

    // 测试示例
    public static void main(String[] args) {
        // 构造一些测试数据（假设坐标单位为像素）
        List<GrpcAlgorithmResultDto> algorithmResultDtoList = new ArrayList<>();

        // 添加一个人检测结果
        GrpcAlgorithmResultDto person = new GrpcAlgorithmResultDto();
        person.setLabel(0);
        person.setProb(0.95);
        person.setMinx(50);
        person.setMiny(50);
        person.setMaxx(150);
        person.setMaxy(200);
        algorithmResultDtoList.add(person);

        // 添加一个鱼竿检测结果，与人框接近或部分重叠
        GrpcAlgorithmResultDto rod = new GrpcAlgorithmResultDto();
        rod.setLabel(1);
        rod.setProb(0.90);
        rod.setMinx(140);  // 与人的右侧接近
        rod.setMiny(60);
        rod.setMaxx(200);
        rod.setMaxy(180);
        algorithmResultDtoList.add(rod);

        // 添加一个渔网检测结果，故意设置较远
        GrpcAlgorithmResultDto net = new GrpcAlgorithmResultDto();
        net.setLabel(2);
        net.setProb(0.88);
        net.setMinx(300);
        net.setMiny(300);
        net.setMaxx(400);
        net.setMaxy(450);
        algorithmResultDtoList.add(net);

        // 调用检测非法垂钓的方法
        List<GrpcAlgorithmResultDto> illegalList = IllegalFishingDetector.detectIllegalFishing(algorithmResultDtoList, 1080, 1920);

        // 输出结果
        for (GrpcAlgorithmResultDto dto : illegalList) {
            System.out.println("非法垂钓检测框: (" + dto.getMinx() + ", " + dto.getMiny() + ") 到 (" +
                    dto.getMaxx() + ", " + dto.getMaxy() + "), 概率: " + dto.getProb());
        }
    }
}
