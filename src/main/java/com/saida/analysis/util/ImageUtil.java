package com.saida.analysis.util;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

@Slf4j
public class ImageUtil {

    /**
     *
     */
    public static byte[] bufferedImageToBase64(BufferedImage image) {
        ByteArrayOutputStream baos = null;
        ImageOutputStream ios = null;
        try {
            baos = new ByteArrayOutputStream();
            ios = ImageIO.createImageOutputStream(baos);

            // 获取 JPEG 格式的 ImageWriter
            ImageWriter writer = ImageIO.getImageWritersByFormatName("jpg").next();
            writer.setOutput(ios);

            // 配置 ImageWriteParam 设置质量
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.9f); // 设置压缩质量，0.0f 最低，1.0f 最高
            }
            // 写入图像
            writer.write(null, new IIOImage(image, null, null), param);
            // 获取字节数组
            return baos.toByteArray();
        } catch (Exception e) {
            log.error("Error converting image to JPEG Base64", e);
        } finally {
            // 关闭资源
            if (ios != null) {
                try {
                    ios.close();
                } catch (IOException e) {
                    log.error("Error closing ImageOutputStream", e);
                }
            }
            if (baos != null) {
                try {
                    baos.close();
                } catch (IOException e) {
                    log.error("Error closing ByteArrayOutputStream", e);
                }
            }
        }
        return null;
    }

    /**
     */
    public static BufferedImage base64ToBufferedImage(byte[] imageBytes) throws IOException {
        // 解码Base64字符串
        ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
        // Read the image from the input stream
        return ImageIO.read(bis);
    }

}
