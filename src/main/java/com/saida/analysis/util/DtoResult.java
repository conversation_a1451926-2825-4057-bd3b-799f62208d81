package com.saida.analysis.util;

import com.github.pagehelper.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.io.Serializable;

/**
 * DTO统一响应结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public final class DtoResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final Logger logger = LoggerFactory.getLogger(DtoResult.class);

    private Integer code;

    private String message;

    private String error;

    private T data;

    private String traceId;

    private String spanId;


    public static <T> DtoResult<T> ok() {
        return new DtoResult<>(ResultEnum.SUCCESS.getCode(), ResultEnum.SUCCESS.getDesc(), null, null, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> ok(T data) {
        return new DtoResult<>(ResultEnum.SUCCESS.getCode(), ResultEnum.SUCCESS.getDesc(), null, data, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> ok(String message, T data) {
        return new DtoResult<>(ResultEnum.SUCCESS.getCode(), message, null, data, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> ok(Integer code, String message, T data) {
        return new DtoResult<>(code, message, null, data, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> error() {
        return new DtoResult<>(ResultEnum.ERROR.getCode(), ResultEnum.ERROR.getDesc(), null, null, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> error(String message) {
        if (StringUtil.isEmpty(message)) {
            message = ResultEnum.ERROR.getDesc();
        }
        return new DtoResult<>(ResultEnum.ERROR.getCode(), message, null, null, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> error(String message, String error) {
        if (StringUtil.isEmpty(message)) {
            message = ResultEnum.ERROR.getDesc();
        }
        return new DtoResult<>(ResultEnum.ERROR.getCode(), message, error, null, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> error(Integer code, String message, String error) {
        if (StringUtil.isEmpty(message)) {
            message = ResultEnum.ERROR.getDesc();
        }
        return new DtoResult<>(code, message, error, null, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> build(int code, String message) {
        if (StringUtil.isEmpty(message)) {
            message = ResultEnum.ERROR.getDesc();
        }
        return new DtoResult<>(code, message, null, null, MDC.get("traceId"), MDC.get("spanId"));
    }

    public static <T> DtoResult<T> build(ResultEnum resultEnum) {
        return new DtoResult<>(resultEnum.getCode(), resultEnum.getDesc(), null, null, MDC.get("traceId"), MDC.get("spanId"));
    }

    public boolean success() {
        return ResultEnum.SUCCESS.getCode().equals(this.getCode());
    }
}