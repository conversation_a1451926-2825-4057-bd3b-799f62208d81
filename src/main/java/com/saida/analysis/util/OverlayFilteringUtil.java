package com.saida.analysis.util;

import com.saida.analysis.dto.GrpcAlgorithmResultDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 过滤叠加框
 */
public class OverlayFilteringUtil {

    public static List<GrpcAlgorithmResultDto> filterResults(List<GrpcAlgorithmResultDto> algorithmResultDtoList) {
        // 结果集
        List<GrpcAlgorithmResultDto> result = new ArrayList<>();

        // 标志位，标记已处理的框
        boolean[] removed = new boolean[algorithmResultDtoList.size()];

        for (int i = 0; i < algorithmResultDtoList.size(); i++) {
            if (removed[i]) continue;

            GrpcAlgorithmResultDto a = algorithmResultDtoList.get(i);
            for (int j = i + 1; j < algorithmResultDtoList.size(); j++) {
                if (removed[j]) continue;

                GrpcAlgorithmResultDto b = algorithmResultDtoList.get(j);

                // 计算相交面积
                int intersectionArea = calculateIntersectionArea(a, b);
                int aArea = calculateArea(a);
                int bArea = calculateArea(b);

                // 计算重叠比例
                double overlapA = (double) intersectionArea / aArea;
                double overlapB = (double) intersectionArea / bArea;

                if (overlapA > 0.95 || overlapB > 0.95) {
                    if (a.getProb() > b.getProb()) {
                        removed[j] = true;
                    } else if (a.getProb() < b.getProb()) {
                        removed[i] = true;
                        break; // 当前a已被淘汰，无需继续检查
                    } else { // 概率相同，比较面积
                        if (aArea >= bArea) {
                            removed[j] = true;
                        } else {
                            removed[i] = true;
                            break;
                        }
                    }
                }
            }
        }

        // 收集未被移除的框
        for (int i = 0; i < algorithmResultDtoList.size(); i++) {
            if (!removed[i]) {
                result.add(algorithmResultDtoList.get(i));
            }
        }
        return result;
    }

    // 计算相交面积
    private static int calculateIntersectionArea(GrpcAlgorithmResultDto a, GrpcAlgorithmResultDto b) {
        int xOverlap = Math.max(0, Math.min(a.getMaxx(), b.getMaxx()) - Math.max(a.getMinx(), b.getMinx()));
        int yOverlap = Math.max(0, Math.min(a.getMaxy(), b.getMaxy()) - Math.max(a.getMiny(), b.getMiny()));
        return xOverlap * yOverlap;
    }

    // 计算矩形面积
    private static int calculateArea(GrpcAlgorithmResultDto rect) {
        return (rect.getMaxx() - rect.getMinx()) * (rect.getMaxy() - rect.getMiny());
    }

    public static void main(String[] args) {
        List<GrpcAlgorithmResultDto> testData = new ArrayList<>();

        // 框1 - 概率0.9，面积较大
        GrpcAlgorithmResultDto box1 = new GrpcAlgorithmResultDto();
        box1.setMinx(10); box1.setMiny(10); box1.setMaxx(110); box1.setMaxy(110);
        box1.setProb(0.9);

        // 框2 - 概率0.8，面积较小，与box1重叠面积>95%
        GrpcAlgorithmResultDto box2 = new GrpcAlgorithmResultDto();
        box2.setMinx(15); box2.setMiny(15); box2.setMaxx(105); box2.setMaxy(105);
        box2.setProb(0.8);

        // 框3 - 概率0.9，与box1和box2无重叠
        GrpcAlgorithmResultDto box3 = new GrpcAlgorithmResultDto();
        box3.setMinx(200); box3.setMiny(200); box3.setMaxx(300); box3.setMaxy(300);
        box3.setProb(0.9);

        // 框4 - 与box3部分重叠，但重叠面积 < 95%
        GrpcAlgorithmResultDto box4 = new GrpcAlgorithmResultDto();
        box4.setMinx(280); box4.setMiny(280); box4.setMaxx(350); box4.setMaxy(350);
        box4.setProb(0.7);

        testData.add(box1);
        testData.add(box2);
        testData.add(box3);
        testData.add(box4);

        // 执行筛选
        List<GrpcAlgorithmResultDto> result = OverlayFilteringUtil.filterResults(testData);

        // 输出结果
        System.out.println("筛选后的结果：");
        for (GrpcAlgorithmResultDto dto : result) {
            System.out.println(String.format("框: [%d, %d, %d, %d], 概率: %.2f",
                    dto.getMinx(), dto.getMiny(), dto.getMaxx(), dto.getMaxy(), dto.getProb()));
        }
    }
}
