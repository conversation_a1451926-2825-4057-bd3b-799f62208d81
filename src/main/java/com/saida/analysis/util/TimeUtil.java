package com.saida.analysis.util;

import com.alibaba.fastjson.JSON;
import com.saida.analysis.dto.DcTimeDto;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

public class TimeUtil {

    public static void main(String[] args) {
        String a = "[{\"timeList\":[{\"endTime\":\"06:00\",\"startTime\":\"00:00\"},{\"endTime\":\"23:59\",\"startTime\":\"21:00\"}],\"weekIndex\":1},{\"timeList\":[{\"endTime\":\"06:00\",\"startTime\":\"00:00\"},{\"endTime\":\"23:59\",\"startTime\":\"21:00\"}],\"weekIndex\":2},{\"timeList\":[{\"endTime\":\"06:00\",\"startTime\":\"00:00\"},{\"endTime\":\"23:59\",\"startTime\":\"21:00\"}],\"weekIndex\":3},{\"timeList\":[{\"endTime\":\"06:00\",\"startTime\":\"00:00\"},{\"endTime\":\"23:59\",\"startTime\":\"21:00\"}],\"weekIndex\":4},{\"timeList\":[{\"endTime\":\"06:00\",\"startTime\":\"00:00\"},{\"endTime\":\"23:59\",\"startTime\":\"21:00\"}],\"weekIndex\":5},{\"timeList\":[{\"endTime\":\"06:00\",\"startTime\":\"00:00\"},{\"endTime\":\"23:59\",\"startTime\":\"21:00\"}],\"weekIndex\":6},{\"timeList\":[{\"endTime\":\"06:00\",\"startTime\":\"00:00\"},{\"endTime\":\"23:59\",\"startTime\":\"21:00\"}],\"weekIndex\":0}]";
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalTime currentTime = currentDateTime.toLocalTime();  // 获取当前时间
        int currentWeekday = currentDateTime.getDayOfWeek().getValue();  // 获取今天是周几 (1-7, 1为周一)
        List<DcTimeDto> dcTimeDtos = JSON.parseArray(a, DcTimeDto.class);
        System.out.println(TimeUtil.isCurrentTimeInTemplate(dcTimeDtos, currentWeekday, currentTime));
    }

    /**
     * 判断当前时间是否在时间模板中的时间段内
     * @param dcTimeDtos 时间模板列表
     * @param currentWeekday 当前星期几 (1-7, 1为周一)
     * @param currentTime 当前时间
     * @return 如果当前时间在模板时间段内，返回true，否则返回false
     */
    public static boolean isCurrentTimeInTemplate(List<DcTimeDto> dcTimeDtos, int currentWeekday, LocalTime currentTime) {
        // 遍历时间模板列表
        for (DcTimeDto dcTimeDto : dcTimeDtos) {
            // 检查当前星期几是否与模板中的匹配
            if (dcTimeDto.getWeekIndex() + 1 == currentWeekday && dcTimeDto.getTimeList() != null) {

                // 遍历该星期几的所有时间段
                for (DcTimeDto.DcTime timeTemplate : dcTimeDto.getTimeList()) {
                    LocalTime startTime = LocalTime.parse(timeTemplate.getStartTime());
                    LocalTime endTime = LocalTime.parse(timeTemplate.getEndTime());

                    // 判断当前时间是否在该时间段内
                    if (currentTime.isAfter(startTime) && currentTime.isBefore(endTime)) {
                        return true;  // 当前时间在模板的时间段内
                    }
                }
            }
        }
        return false;  // 当前时间不在任何模板的时间段内
    }

}
