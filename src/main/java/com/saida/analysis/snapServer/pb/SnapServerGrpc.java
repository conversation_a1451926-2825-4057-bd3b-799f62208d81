package com.saida.analysis.snapServer.pb;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.68.1)",
    comments = "Source: snap_server.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class SnapServerGrpc {

  private SnapServerGrpc() {}

  public static final java.lang.String SERVICE_NAME = "SnapServer";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel,
      com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply> getNewSnapTaskMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "NewSnapTask",
      requestType = com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.class,
      responseType = com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.class,
      methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
  public static io.grpc.MethodDescriptor<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel,
      com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply> getNewSnapTaskMethod() {
    io.grpc.MethodDescriptor<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel, com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply> getNewSnapTaskMethod;
    if ((getNewSnapTaskMethod = SnapServerGrpc.getNewSnapTaskMethod) == null) {
      synchronized (SnapServerGrpc.class) {
        if ((getNewSnapTaskMethod = SnapServerGrpc.getNewSnapTaskMethod) == null) {
          SnapServerGrpc.getNewSnapTaskMethod = getNewSnapTaskMethod =
              io.grpc.MethodDescriptor.<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel, com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "NewSnapTask"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.getDefaultInstance()))
              .setSchemaDescriptor(new SnapServerMethodDescriptorSupplier("NewSnapTask"))
              .build();
        }
      }
    }
    return getNewSnapTaskMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static SnapServerStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<SnapServerStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<SnapServerStub>() {
        @java.lang.Override
        public SnapServerStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new SnapServerStub(channel, callOptions);
        }
      };
    return SnapServerStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static SnapServerBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<SnapServerBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<SnapServerBlockingStub>() {
        @java.lang.Override
        public SnapServerBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new SnapServerBlockingStub(channel, callOptions);
        }
      };
    return SnapServerBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static SnapServerFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<SnapServerFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<SnapServerFutureStub>() {
        @java.lang.Override
        public SnapServerFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new SnapServerFutureStub(channel, callOptions);
        }
      };
    return SnapServerFutureStub.newStub(factory, channel);
  }

  /**
   */
  public interface AsyncService {

    /**
     */
    default void newSnapTask(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel request,
        io.grpc.stub.StreamObserver<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getNewSnapTaskMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service SnapServer.
   */
  public static abstract class SnapServerImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return SnapServerGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service SnapServer.
   */
  public static final class SnapServerStub
      extends io.grpc.stub.AbstractAsyncStub<SnapServerStub> {
    private SnapServerStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected SnapServerStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new SnapServerStub(channel, callOptions);
    }

    /**
     */
    public void newSnapTask(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel request,
        io.grpc.stub.StreamObserver<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply> responseObserver) {
      io.grpc.stub.ClientCalls.asyncServerStreamingCall(
          getChannel().newCall(getNewSnapTaskMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service SnapServer.
   */
  public static final class SnapServerBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<SnapServerBlockingStub> {
    private SnapServerBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected SnapServerBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new SnapServerBlockingStub(channel, callOptions);
    }

    /**
     */
    public java.util.Iterator<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply> newSnapTask(
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel request) {
      return io.grpc.stub.ClientCalls.blockingServerStreamingCall(
          getChannel(), getNewSnapTaskMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service SnapServer.
   */
  public static final class SnapServerFutureStub
      extends io.grpc.stub.AbstractFutureStub<SnapServerFutureStub> {
    private SnapServerFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected SnapServerFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new SnapServerFutureStub(channel, callOptions);
    }
  }

  private static final int METHODID_NEW_SNAP_TASK = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_NEW_SNAP_TASK:
          serviceImpl.newSnapTask((com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel) request,
              (io.grpc.stub.StreamObserver<com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getNewSnapTaskMethod(),
          io.grpc.stub.ServerCalls.asyncServerStreamingCall(
            new MethodHandlers<
              com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel,
              com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply>(
                service, METHODID_NEW_SNAP_TASK)))
        .build();
  }

  private static abstract class SnapServerBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    SnapServerBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.saida.analysis.snapServer.pb.SnapServerOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("SnapServer");
    }
  }

  private static final class SnapServerFileDescriptorSupplier
      extends SnapServerBaseDescriptorSupplier {
    SnapServerFileDescriptorSupplier() {}
  }

  private static final class SnapServerMethodDescriptorSupplier
      extends SnapServerBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    SnapServerMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (SnapServerGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new SnapServerFileDescriptorSupplier())
              .addMethod(getNewSnapTaskMethod())
              .build();
        }
      }
    }
    return result;
  }
}
