// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: snap_server.proto

// Protobuf Java Version: 3.25.5
package com.saida.analysis.snapServer.pb;

public final class SnapServerOuterClass {
  private SnapServerOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code TaskReplyType}
   */
  public enum TaskReplyType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>TASK_REPLY_TYPE_UNKNOWN = 0;</code>
     */
    TASK_REPLY_TYPE_UNKNOWN(0),
    /**
     * <code>TASK_REPLY_TYPE_INFO_WITHOUT_ERROR = 1;</code>
     */
    TASK_REPLY_TYPE_INFO_WITHOUT_ERROR(1),
    /**
     * <code>TASK_REPLY_TYPE_DATA = 2;</code>
     */
    TASK_REPLY_TYPE_DATA(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>TASK_REPLY_TYPE_UNKNOWN = 0;</code>
     */
    public static final int TASK_REPLY_TYPE_UNKNOWN_VALUE = 0;
    /**
     * <code>TASK_REPLY_TYPE_INFO_WITHOUT_ERROR = 1;</code>
     */
    public static final int TASK_REPLY_TYPE_INFO_WITHOUT_ERROR_VALUE = 1;
    /**
     * <code>TASK_REPLY_TYPE_DATA = 2;</code>
     */
    public static final int TASK_REPLY_TYPE_DATA_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static TaskReplyType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static TaskReplyType forNumber(int value) {
      switch (value) {
        case 0: return TASK_REPLY_TYPE_UNKNOWN;
        case 1: return TASK_REPLY_TYPE_INFO_WITHOUT_ERROR;
        case 2: return TASK_REPLY_TYPE_DATA;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<TaskReplyType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        TaskReplyType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<TaskReplyType>() {
            public TaskReplyType findValueByNumber(int number) {
              return TaskReplyType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.saida.analysis.snapServer.pb.SnapServerOuterClass.getDescriptor().getEnumTypes().get(0);
    }

    private static final TaskReplyType[] VALUES = values();

    public static TaskReplyType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private TaskReplyType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:TaskReplyType)
  }

  public interface TaskModelOrBuilder extends
      // @@protoc_insertion_point(interface_extends:TaskModel)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
     * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
     * </pre>
     *
     * <code>bool key_frame_only = 1;</code>
     * @return The keyFrameOnly.
     */
    boolean getKeyFrameOnly();

    /**
     * <pre>
     * 帧步长：指定处理视频时连续多少帧（单位：帧）
     * </pre>
     *
     * <code>uint32 frame_step = 2;</code>
     * @return The frameStep.
     */
    int getFrameStep();

    /**
     * <pre>
     * 跳过的帧数：指定在处理视频时跳过的帧数
     * </pre>
     *
     * <code>uint32 skip_step = 3;</code>
     * @return The skipStep.
     */
    int getSkipStep();

    /**
     * <pre>
     * 输入
     * </pre>
     *
     * <code>string inputURL = 4;</code>
     * @return The inputURL.
     */
    java.lang.String getInputURL();
    /**
     * <pre>
     * 输入
     * </pre>
     *
     * <code>string inputURL = 4;</code>
     * @return The bytes for inputURL.
     */
    com.google.protobuf.ByteString
        getInputURLBytes();

    /**
     * <pre>
     * 输出
     * </pre>
     *
     * <code>string outputPath = 5;</code>
     * @return The outputPath.
     */
    java.lang.String getOutputPath();
    /**
     * <pre>
     * 输出
     * </pre>
     *
     * <code>string outputPath = 5;</code>
     * @return The bytes for outputPath.
     */
    com.google.protobuf.ByteString
        getOutputPathBytes();
  }
  /**
   * Protobuf type {@code TaskModel}
   */
  public static final class TaskModel extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:TaskModel)
      TaskModelOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskModel.newBuilder() to construct.
    private TaskModel(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskModel() {
      inputURL_ = "";
      outputPath_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskModel();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskModel_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskModel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.class, com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.Builder.class);
    }

    public static final int KEY_FRAME_ONLY_FIELD_NUMBER = 1;
    private boolean keyFrameOnly_ = false;
    /**
     * <pre>
     * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
     * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
     * </pre>
     *
     * <code>bool key_frame_only = 1;</code>
     * @return The keyFrameOnly.
     */
    @java.lang.Override
    public boolean getKeyFrameOnly() {
      return keyFrameOnly_;
    }

    public static final int FRAME_STEP_FIELD_NUMBER = 2;
    private int frameStep_ = 0;
    /**
     * <pre>
     * 帧步长：指定处理视频时连续多少帧（单位：帧）
     * </pre>
     *
     * <code>uint32 frame_step = 2;</code>
     * @return The frameStep.
     */
    @java.lang.Override
    public int getFrameStep() {
      return frameStep_;
    }

    public static final int SKIP_STEP_FIELD_NUMBER = 3;
    private int skipStep_ = 0;
    /**
     * <pre>
     * 跳过的帧数：指定在处理视频时跳过的帧数
     * </pre>
     *
     * <code>uint32 skip_step = 3;</code>
     * @return The skipStep.
     */
    @java.lang.Override
    public int getSkipStep() {
      return skipStep_;
    }

    public static final int INPUTURL_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object inputURL_ = "";
    /**
     * <pre>
     * 输入
     * </pre>
     *
     * <code>string inputURL = 4;</code>
     * @return The inputURL.
     */
    @java.lang.Override
    public java.lang.String getInputURL() {
      java.lang.Object ref = inputURL_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        inputURL_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 输入
     * </pre>
     *
     * <code>string inputURL = 4;</code>
     * @return The bytes for inputURL.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInputURLBytes() {
      java.lang.Object ref = inputURL_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        inputURL_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OUTPUTPATH_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object outputPath_ = "";
    /**
     * <pre>
     * 输出
     * </pre>
     *
     * <code>string outputPath = 5;</code>
     * @return The outputPath.
     */
    @java.lang.Override
    public java.lang.String getOutputPath() {
      java.lang.Object ref = outputPath_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        outputPath_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 输出
     * </pre>
     *
     * <code>string outputPath = 5;</code>
     * @return The bytes for outputPath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOutputPathBytes() {
      java.lang.Object ref = outputPath_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        outputPath_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (keyFrameOnly_ != false) {
        output.writeBool(1, keyFrameOnly_);
      }
      if (frameStep_ != 0) {
        output.writeUInt32(2, frameStep_);
      }
      if (skipStep_ != 0) {
        output.writeUInt32(3, skipStep_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(inputURL_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, inputURL_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(outputPath_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, outputPath_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (keyFrameOnly_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, keyFrameOnly_);
      }
      if (frameStep_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, frameStep_);
      }
      if (skipStep_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, skipStep_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(inputURL_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, inputURL_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(outputPath_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, outputPath_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel)) {
        return super.equals(obj);
      }
      com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel other = (com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel) obj;

      if (getKeyFrameOnly()
          != other.getKeyFrameOnly()) return false;
      if (getFrameStep()
          != other.getFrameStep()) return false;
      if (getSkipStep()
          != other.getSkipStep()) return false;
      if (!getInputURL()
          .equals(other.getInputURL())) return false;
      if (!getOutputPath()
          .equals(other.getOutputPath())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + KEY_FRAME_ONLY_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getKeyFrameOnly());
      hash = (37 * hash) + FRAME_STEP_FIELD_NUMBER;
      hash = (53 * hash) + getFrameStep();
      hash = (37 * hash) + SKIP_STEP_FIELD_NUMBER;
      hash = (53 * hash) + getSkipStep();
      hash = (37 * hash) + INPUTURL_FIELD_NUMBER;
      hash = (53 * hash) + getInputURL().hashCode();
      hash = (37 * hash) + OUTPUTPATH_FIELD_NUMBER;
      hash = (53 * hash) + getOutputPath().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code TaskModel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:TaskModel)
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskModel_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskModel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.class, com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.Builder.class);
      }

      // Construct using com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        keyFrameOnly_ = false;
        frameStep_ = 0;
        skipStep_ = 0;
        inputURL_ = "";
        outputPath_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskModel_descriptor;
      }

      @java.lang.Override
      public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel getDefaultInstanceForType() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.getDefaultInstance();
      }

      @java.lang.Override
      public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel build() {
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel buildPartial() {
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel result = new com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.keyFrameOnly_ = keyFrameOnly_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.frameStep_ = frameStep_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.skipStep_ = skipStep_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.inputURL_ = inputURL_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.outputPath_ = outputPath_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel) {
          return mergeFrom((com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel other) {
        if (other == com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel.getDefaultInstance()) return this;
        if (other.getKeyFrameOnly() != false) {
          setKeyFrameOnly(other.getKeyFrameOnly());
        }
        if (other.getFrameStep() != 0) {
          setFrameStep(other.getFrameStep());
        }
        if (other.getSkipStep() != 0) {
          setSkipStep(other.getSkipStep());
        }
        if (!other.getInputURL().isEmpty()) {
          inputURL_ = other.inputURL_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.getOutputPath().isEmpty()) {
          outputPath_ = other.outputPath_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                keyFrameOnly_ = input.readBool();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                frameStep_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                skipStep_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                inputURL_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                outputPath_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private boolean keyFrameOnly_ ;
      /**
       * <pre>
       * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
       * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
       * </pre>
       *
       * <code>bool key_frame_only = 1;</code>
       * @return The keyFrameOnly.
       */
      @java.lang.Override
      public boolean getKeyFrameOnly() {
        return keyFrameOnly_;
      }
      /**
       * <pre>
       * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
       * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
       * </pre>
       *
       * <code>bool key_frame_only = 1;</code>
       * @param value The keyFrameOnly to set.
       * @return This builder for chaining.
       */
      public Builder setKeyFrameOnly(boolean value) {

        keyFrameOnly_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
       * 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
       * </pre>
       *
       * <code>bool key_frame_only = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKeyFrameOnly() {
        bitField0_ = (bitField0_ & ~0x00000001);
        keyFrameOnly_ = false;
        onChanged();
        return this;
      }

      private int frameStep_ ;
      /**
       * <pre>
       * 帧步长：指定处理视频时连续多少帧（单位：帧）
       * </pre>
       *
       * <code>uint32 frame_step = 2;</code>
       * @return The frameStep.
       */
      @java.lang.Override
      public int getFrameStep() {
        return frameStep_;
      }
      /**
       * <pre>
       * 帧步长：指定处理视频时连续多少帧（单位：帧）
       * </pre>
       *
       * <code>uint32 frame_step = 2;</code>
       * @param value The frameStep to set.
       * @return This builder for chaining.
       */
      public Builder setFrameStep(int value) {

        frameStep_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 帧步长：指定处理视频时连续多少帧（单位：帧）
       * </pre>
       *
       * <code>uint32 frame_step = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFrameStep() {
        bitField0_ = (bitField0_ & ~0x00000002);
        frameStep_ = 0;
        onChanged();
        return this;
      }

      private int skipStep_ ;
      /**
       * <pre>
       * 跳过的帧数：指定在处理视频时跳过的帧数
       * </pre>
       *
       * <code>uint32 skip_step = 3;</code>
       * @return The skipStep.
       */
      @java.lang.Override
      public int getSkipStep() {
        return skipStep_;
      }
      /**
       * <pre>
       * 跳过的帧数：指定在处理视频时跳过的帧数
       * </pre>
       *
       * <code>uint32 skip_step = 3;</code>
       * @param value The skipStep to set.
       * @return This builder for chaining.
       */
      public Builder setSkipStep(int value) {

        skipStep_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 跳过的帧数：指定在处理视频时跳过的帧数
       * </pre>
       *
       * <code>uint32 skip_step = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkipStep() {
        bitField0_ = (bitField0_ & ~0x00000004);
        skipStep_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object inputURL_ = "";
      /**
       * <pre>
       * 输入
       * </pre>
       *
       * <code>string inputURL = 4;</code>
       * @return The inputURL.
       */
      public java.lang.String getInputURL() {
        java.lang.Object ref = inputURL_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          inputURL_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 输入
       * </pre>
       *
       * <code>string inputURL = 4;</code>
       * @return The bytes for inputURL.
       */
      public com.google.protobuf.ByteString
          getInputURLBytes() {
        java.lang.Object ref = inputURL_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          inputURL_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 输入
       * </pre>
       *
       * <code>string inputURL = 4;</code>
       * @param value The inputURL to set.
       * @return This builder for chaining.
       */
      public Builder setInputURL(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        inputURL_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 输入
       * </pre>
       *
       * <code>string inputURL = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearInputURL() {
        inputURL_ = getDefaultInstance().getInputURL();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 输入
       * </pre>
       *
       * <code>string inputURL = 4;</code>
       * @param value The bytes for inputURL to set.
       * @return This builder for chaining.
       */
      public Builder setInputURLBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        inputURL_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object outputPath_ = "";
      /**
       * <pre>
       * 输出
       * </pre>
       *
       * <code>string outputPath = 5;</code>
       * @return The outputPath.
       */
      public java.lang.String getOutputPath() {
        java.lang.Object ref = outputPath_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          outputPath_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 输出
       * </pre>
       *
       * <code>string outputPath = 5;</code>
       * @return The bytes for outputPath.
       */
      public com.google.protobuf.ByteString
          getOutputPathBytes() {
        java.lang.Object ref = outputPath_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          outputPath_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 输出
       * </pre>
       *
       * <code>string outputPath = 5;</code>
       * @param value The outputPath to set.
       * @return This builder for chaining.
       */
      public Builder setOutputPath(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        outputPath_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 输出
       * </pre>
       *
       * <code>string outputPath = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutputPath() {
        outputPath_ = getDefaultInstance().getOutputPath();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 输出
       * </pre>
       *
       * <code>string outputPath = 5;</code>
       * @param value The bytes for outputPath to set.
       * @return This builder for chaining.
       */
      public Builder setOutputPathBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        outputPath_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:TaskModel)
    }

    // @@protoc_insertion_point(class_scope:TaskModel)
    private static final com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel();
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskModel>
        PARSER = new com.google.protobuf.AbstractParser<TaskModel>() {
      @java.lang.Override
      public TaskModel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskModel> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskModel> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskModel getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskReplyOrBuilder extends
      // @@protoc_insertion_point(interface_extends:TaskReply)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.TaskReplyType type = 1;</code>
     * @return The enum numeric value on the wire for type.
     */
    int getTypeValue();
    /**
     * <code>.TaskReplyType type = 1;</code>
     * @return The type.
     */
    com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType getType();

    /**
     * <code>string output_path = 2;</code>
     * @return The outputPath.
     */
    java.lang.String getOutputPath();
    /**
     * <code>string output_path = 2;</code>
     * @return The bytes for outputPath.
     */
    com.google.protobuf.ByteString
        getOutputPathBytes();

    /**
     * <code>string desc = 3;</code>
     * @return The desc.
     */
    java.lang.String getDesc();
    /**
     * <code>string desc = 3;</code>
     * @return The bytes for desc.
     */
    com.google.protobuf.ByteString
        getDescBytes();
  }
  /**
   * Protobuf type {@code TaskReply}
   */
  public static final class TaskReply extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:TaskReply)
      TaskReplyOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TaskReply.newBuilder() to construct.
    private TaskReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TaskReply() {
      type_ = 0;
      outputPath_ = "";
      desc_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TaskReply();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.class, com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>.TaskReplyType type = 1;</code>
     * @return The enum numeric value on the wire for type.
     */
    @java.lang.Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.TaskReplyType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType getType() {
      com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType result = com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType.forNumber(type_);
      return result == null ? com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType.UNRECOGNIZED : result;
    }

    public static final int OUTPUT_PATH_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object outputPath_ = "";
    /**
     * <code>string output_path = 2;</code>
     * @return The outputPath.
     */
    @java.lang.Override
    public java.lang.String getOutputPath() {
      java.lang.Object ref = outputPath_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        outputPath_ = s;
        return s;
      }
    }
    /**
     * <code>string output_path = 2;</code>
     * @return The bytes for outputPath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOutputPathBytes() {
      java.lang.Object ref = outputPath_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        outputPath_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DESC_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object desc_ = "";
    /**
     * <code>string desc = 3;</code>
     * @return The desc.
     */
    @java.lang.Override
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      }
    }
    /**
     * <code>string desc = 3;</code>
     * @return The bytes for desc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType.TASK_REPLY_TYPE_UNKNOWN.getNumber()) {
        output.writeEnum(1, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(outputPath_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, outputPath_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, desc_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType.TASK_REPLY_TYPE_UNKNOWN.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(outputPath_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, outputPath_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(desc_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, desc_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply)) {
        return super.equals(obj);
      }
      com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply other = (com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply) obj;

      if (type_ != other.type_) return false;
      if (!getOutputPath()
          .equals(other.getOutputPath())) return false;
      if (!getDesc()
          .equals(other.getDesc())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + type_;
      hash = (37 * hash) + OUTPUT_PATH_FIELD_NUMBER;
      hash = (53 * hash) + getOutputPath().hashCode();
      hash = (37 * hash) + DESC_FIELD_NUMBER;
      hash = (53 * hash) + getDesc().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code TaskReply}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:TaskReply)
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskReply_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskReply_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.class, com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.Builder.class);
      }

      // Construct using com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        outputPath_ = "";
        desc_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.internal_static_TaskReply_descriptor;
      }

      @java.lang.Override
      public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply getDefaultInstanceForType() {
        return com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.getDefaultInstance();
      }

      @java.lang.Override
      public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply build() {
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply buildPartial() {
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply result = new com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.outputPath_ = outputPath_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.desc_ = desc_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply) {
          return mergeFrom((com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply other) {
        if (other == com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply.getDefaultInstance()) return this;
        if (other.type_ != 0) {
          setTypeValue(other.getTypeValue());
        }
        if (!other.getOutputPath().isEmpty()) {
          outputPath_ = other.outputPath_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (!other.getDesc().isEmpty()) {
          desc_ = other.desc_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readEnum();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                outputPath_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                desc_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <code>.TaskReplyType type = 1;</code>
       * @return The enum numeric value on the wire for type.
       */
      @java.lang.Override public int getTypeValue() {
        return type_;
      }
      /**
       * <code>.TaskReplyType type = 1;</code>
       * @param value The enum numeric value on the wire for type to set.
       * @return This builder for chaining.
       */
      public Builder setTypeValue(int value) {
        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.TaskReplyType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType getType() {
        com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType result = com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType.forNumber(type_);
        return result == null ? com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType.UNRECOGNIZED : result;
      }
      /**
       * <code>.TaskReplyType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReplyType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>.TaskReplyType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object outputPath_ = "";
      /**
       * <code>string output_path = 2;</code>
       * @return The outputPath.
       */
      public java.lang.String getOutputPath() {
        java.lang.Object ref = outputPath_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          outputPath_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string output_path = 2;</code>
       * @return The bytes for outputPath.
       */
      public com.google.protobuf.ByteString
          getOutputPathBytes() {
        java.lang.Object ref = outputPath_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          outputPath_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string output_path = 2;</code>
       * @param value The outputPath to set.
       * @return This builder for chaining.
       */
      public Builder setOutputPath(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        outputPath_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string output_path = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutputPath() {
        outputPath_ = getDefaultInstance().getOutputPath();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string output_path = 2;</code>
       * @param value The bytes for outputPath to set.
       * @return This builder for chaining.
       */
      public Builder setOutputPathBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        outputPath_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object desc_ = "";
      /**
       * <code>string desc = 3;</code>
       * @return The desc.
       */
      public java.lang.String getDesc() {
        java.lang.Object ref = desc_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          desc_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string desc = 3;</code>
       * @return The bytes for desc.
       */
      public com.google.protobuf.ByteString
          getDescBytes() {
        java.lang.Object ref = desc_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          desc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string desc = 3;</code>
       * @param value The desc to set.
       * @return This builder for chaining.
       */
      public Builder setDesc(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        desc_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string desc = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDesc() {
        desc_ = getDefaultInstance().getDesc();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string desc = 3;</code>
       * @param value The bytes for desc to set.
       * @return This builder for chaining.
       */
      public Builder setDescBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        desc_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:TaskReply)
    }

    // @@protoc_insertion_point(class_scope:TaskReply)
    private static final com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply();
    }

    public static com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskReply>
        PARSER = new com.google.protobuf.AbstractParser<TaskReply>() {
      @java.lang.Override
      public TaskReply parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskReply> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskReply> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.saida.analysis.snapServer.pb.SnapServerOuterClass.TaskReply getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TaskModel_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_TaskModel_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_TaskReply_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_TaskReply_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021snap_server.proto\"p\n\tTaskModel\022\026\n\016key_" +
      "frame_only\030\001 \001(\010\022\022\n\nframe_step\030\002 \001(\r\022\021\n\t" +
      "skip_step\030\003 \001(\r\022\020\n\010inputURL\030\004 \001(\t\022\022\n\nout" +
      "putPath\030\005 \001(\t\"L\n\tTaskReply\022\034\n\004type\030\001 \001(\016" +
      "2\016.TaskReplyType\022\023\n\013output_path\030\002 \001(\t\022\014\n" +
      "\004desc\030\003 \001(\t*n\n\rTaskReplyType\022\033\n\027TASK_REP" +
      "LY_TYPE_UNKNOWN\020\000\022&\n\"TASK_REPLY_TYPE_INF" +
      "O_WITHOUT_ERROR\020\001\022\030\n\024TASK_REPLY_TYPE_DAT" +
      "A\020\00225\n\nSnapServer\022\'\n\013NewSnapTask\022\n.TaskM" +
      "odel\032\n.TaskReply0\001B*\n com.saida.analysis" +
      ".snapServer.pbZ\006.;mainb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_TaskModel_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_TaskModel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_TaskModel_descriptor,
        new java.lang.String[] { "KeyFrameOnly", "FrameStep", "SkipStep", "InputURL", "OutputPath", });
    internal_static_TaskReply_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_TaskReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_TaskReply_descriptor,
        new java.lang.String[] { "Type", "OutputPath", "Desc", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
