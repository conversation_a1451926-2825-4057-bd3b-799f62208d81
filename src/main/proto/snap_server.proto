syntax = "proto3";
option go_package = ".;main";
option java_package = "com.saida.analysis.snapServer.pb";
message TaskModel{
  // 是否只分析关键帧,如果只分析关键帧,那么会忽略 frame_step 和 skip_step 参数,
  // 特别的 如果frame_step 和 skip_step 传输了无效值比如0,0 也会只处理关键帧
  bool key_frame_only = 1;
  // 帧步长：指定处理视频时连续多少帧（单位：帧）
  uint32 frame_step = 2;
  // 跳过的帧数：指定在处理视频时跳过的帧数
  uint32 skip_step = 3;
  // 输入
  string inputURL = 4;
  // 输出
  string outputPath = 5;
}
enum TaskReplyType{
  TASK_REPLY_TYPE_UNKNOWN = 0;
  TASK_REPLY_TYPE_INFO_WITHOUT_ERROR = 1;
  TASK_REPLY_TYPE_DATA = 2;
}
message TaskReply{
  TaskReplyType type = 1;
  string output_path = 2;
  string desc = 3;
}
service SnapServer {
  rpc NewSnapTask(TaskModel) returns (stream TaskReply);
}
